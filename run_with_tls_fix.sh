#!/bin/bash

# OpenSSL 3.x 兼容性修复脚本
# 解决 "native-tls error: unexpected EOF" 问题

echo "设置OpenSSL兼容性环境变量..."

# 禁用OpenSSL配置文件，避免严格的安全策略
export OPENSSL_CONF=/dev/null

# 设置CA证书路径
export SSL_CERT_FILE=/etc/ssl/certs/ca-certificates.crt
export SSL_CERT_DIR=/etc/ssl/certs

# 允许代理证书
export OPENSSL_ALLOW_PROXY_CERTS=1

# 降低TLS安全级别 (1=兼容模式, 2=默认)
export SECLEVEL=1

# 设置更宽松的密码套件
export OPENSSL_CIPHERS="HIGH:!aNULL:!eNULL:!EXPORT:!DES:!RC4:!MD5:!PSK:!SRP:!CAMELLIA"

# 设置日志级别
export RUST_LOG=${RUST_LOG:-info}

echo "环境变量设置完成:"
echo "  OPENSSL_CONF=$OPENSSL_CONF"
echo "  SSL_CERT_FILE=$SSL_CERT_FILE"
echo "  SECLEVEL=$SECLEVEL"
echo ""

# 检查OpenSSL版本
echo "当前OpenSSL版本:"
openssl version
echo ""

# 运行程序
echo "启动程序..."
cargo run --release "$@"
