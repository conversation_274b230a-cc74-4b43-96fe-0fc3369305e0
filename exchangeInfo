{"timezone": "UTC", "serverTime": 1752489740081, "futuresType": "U_MARGINED", "rateLimits": [{"rateLimitType": "REQUEST_WEIGHT", "interval": "MINUTE", "intervalNum": 1, "limit": 2400}, {"rateLimitType": "ORDERS", "interval": "MINUTE", "intervalNum": 1, "limit": 1200}, {"rateLimitType": "ORDERS", "interval": "SECOND", "intervalNum": 10, "limit": 300}], "exchangeFilters": [], "assets": [{"asset": "USDT", "marginAvailable": true, "autoAssetExchange": "-10000"}, {"asset": "BTC", "marginAvailable": true, "autoAssetExchange": "-0.10000000"}, {"asset": "BNB", "marginAvailable": true, "autoAssetExchange": "0"}, {"asset": "ETH", "marginAvailable": true, "autoAssetExchange": "0"}], "symbols": [{"symbol": "BTCUSDT", "pair": "BTCUSDT", "contractType": "PERPETUAL", "deliveryDate": 4133404800000, "onboardDate": 1569398400000, "status": "TRADING", "maintMarginPercent": "2.5000", "requiredMarginPercent": "5.0000", "baseAsset": "BTC", "quoteAsset": "USDT", "marginAsset": "USDT", "pricePrecision": 2, "quantityPrecision": 3, "baseAssetPrecision": 8, "quotePrecision": 8, "underlyingType": "COIN", "underlyingSubType": ["PoW"], "triggerProtect": "0.0500", "liquidationFee": "0.012500", "marketTakeBound": "0.05", "maxMoveOrderLimit": 10000, "filters": [{"filterType": "PRICE_FILTER", "maxPrice": "4529764", "minPrice": "556.80", "tickSize": "0.10"}, {"filterType": "LOT_SIZE", "maxQty": "1000", "minQty": "0.001", "stepSize": "0.001"}, {"filterType": "MARKET_LOT_SIZE", "maxQty": "120", "minQty": "0.001", "stepSize": "0.001"}, {"filterType": "MAX_NUM_ORDERS", "limit": 200}, {"filterType": "MAX_NUM_ALGO_ORDERS", "limit": 10}, {"filterType": "MIN_NOTIONAL", "notional": "100"}, {"filterType": "PERCENT_PRICE", "multiplierDecimal": "4", "multiplierDown": "0.9500", "multiplierUp": "1.0500"}, {"filterType": "POSITION_RISK_CONTROL", "positionControlSide": "NONE"}], "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"], "timeInForce": ["GTC", "IOC", "FOK", "GTX", "GTD"], "permissionSets": ["GRID", "COPY"]}], "sors": [{"baseAsset": "BTC", "symbols": ["BTCUSDT", "BTCUSDC"]}]}