# Trader

## 简介

Trader 是一款基于事件驱动、策略解耦的量化交易系统。系统支持无需更改策略代码即可进行历史数据回测，兼容多个交易所，涵盖现货、杠杆现货、合约等类型，支持统一账户以及同一交易所的多个账号管理。通过组件化设计，用户可以灵活组合各类功能模块，以实现个性化的交易策略和系统扩展。

## 系统架构图

下面是 Trader 系统的整体架构图，展示了各组件如何协同工作：

![系统架构图](./trader.png)

该架构图展示了从数据源、策略、风控到执行引擎的完整流程，各模块通过消息总线进行通信，并支持低延迟的消息传递与处理。

## 启动引擎

启动引擎是系统的核心调度模块。它根据用户配置启动各个组件，实现交易系统的整体运行。启动引擎支持以下几种模式：

- 实时交易
- 实时模拟
- 历史回测

## 组件介绍

### 数据源

数据源组件用于获取交易所的实时数据或历史数据。用户可以通过实现数据源接口，自定义自己的数据源，支持多种数据格式与类型。

### 策略

策略组件负责根据交易所的行情数据、订单信息等进行计算，生成交易指令（如下单、撤单等）。此外，策略组件还能响应来自平台或风控的指令（如平仓、暂停交易、重启策略等）。

### 消息总线

消息总线是系统中各个组件之间通信的核心。它采用发布-订阅模式，确保系统的高效性和低延迟。

### 执行引擎

执行器接收来自策略和风控的交易指令，执行具体的交易操作，并将执行结果通过消息总线返回给相关组件。

### 缓存

系统中的缓存组件（如 Redis）用于存储当前的交易信息，便于无需低延迟的组件间共享数据。缓存组件支持后台数据处理、报表生成等操作。需要注意的是，策略组件通常不会依赖该缓存，而是自行缓存数据以确保更低延迟。

## 其他可选组件

除了核心组件外，Trader 还提供了一些非必需但实用的扩展组件：

### Balance

用于多个交易所之间的资金均衡，帮助用户自动调整不同交易所的资金分配。

### Buy Token

自动购买平台币以享受平台的手续费折扣。

### 更多扩展

Trader 系统提供了多种扩展组件，用户可以根据实际需求自行开发和集成更多功能。

---

## 使用方法

1. 克隆项目：

   ```bash
   git clone ssh://***************:8022/quant/trader.git
   ```

2. 安装依赖并编译项目：

   ```bash
   cargo build
   ```

3. 启动历史数据回测或实时交易：

   ```bash
   cargo run --release
   ```

4. 查看系统日志和结果，调试或优化策略。
