# 交易命令JSON文档

本文档包含所有交易命令序列化后的JSON格式示例，用于API调用参考。

## 同步命令

同步命令会立即返回结果。

### 自定义请求

```json
{
  "account_id": 1,
  "method": "Request",
  "request": {
    "auth": true,
    "body": {
      "key2": "value2"
    },
    "headers": {
      "Content-Type": "application/json"
    },
    "method": "GET",
    "path": "/api/v1/test",
    "query": {
      "key": "value"
    },
    "url": "https://api.example.com/api/v1/test"
  },
  "sync": true
}
```

### 获取订单历史

```json
{
  "account_id": 1,
  "end_time": *************,
  "method": "GetOrders",
  "start_time": **********000,
  "symbol": "BTC_USDT",
  "sync": true
}
```

### 获取当前挂单

```json
{
  "account_id": 1,
  "method": "GetOpenOrders",
  "symbol": "BTC_USDT",
  "sync": true
}
```

### 获取所有挂单

```json
{
  "account_id": 1,
  "method": "GetAllOpenOrders",
  "sync": true
}
```

### 获取订单

```json
{
  "account_id": 1,
  "method": "GetOrderById",
  "order_id": {
    "Id": "test_order_id"
  },
  "symbol": "BTC_USDT",
  "sync": true
}
```

### 同步下单

```json
{
  "account_id": 1,
  "method": "PlaceOrder",
  "order": {
    "amount": 0.1,
    "cid": "sync_client123456",
    "filled": 0.0,
    "filled_avg_price": 0.0,
    "id": "",
    "order_type": "Limit",
    "pos_side": "Long",
    "price": 50000.0,
    "quote_amount": null,
    "side": "Buy",
    "source": "Order",
    "status": "Open",
    "symbol": "BTC_USDT",
    "time_in_force": "GTC",
    "timestamp": 0
  },
  "params": {
    "is_dual_side": false,
    "leverage": 10,
    "margin_mode": "Cross",
    "market_order_mode": "Safe",
    "market_order_slippage": 0.002
  },
  "sync": true
}
```

### 同步批量下单

```json
{
  "account_id": 1,
  "method": "BatchPlaceOrder",
  "orders": [
    {
      "amount": 0.1,
      "cid": "sync_batch_client1",
      "filled": 0.0,
      "filled_avg_price": 0.0,
      "id": "",
      "order_type": "Limit",
      "pos_side": "Long",
      "price": 50000.0,
      "quote_amount": null,
      "side": "Buy",
      "source": "Order",
      "status": "Open",
      "symbol": "BTC_USDT",
      "time_in_force": "GTC",
      "timestamp": 0
    },
    {
      "amount": 1.0,
      "cid": "sync_batch_client2",
      "filled": 0.0,
      "filled_avg_price": 0.0,
      "id": "",
      "order_type": "Limit",
      "pos_side": "Short",
      "price": 3000.0,
      "quote_amount": null,
      "side": "Sell",
      "source": "Order",
      "status": "Open",
      "symbol": "ETH_USDT",
      "time_in_force": "GTC",
      "timestamp": 0
    }
  ],
  "params": {
    "is_dual_side": false,
    "leverage": 10,
    "margin_mode": "Cross",
    "market_order_mode": "Safe",
    "market_order_slippage": 0.002
  },
  "sync": true
}
```

### 同步修改订单

```json
{
  "account_id": 1,
  "method": "AmendOrder",
  "order": {
    "amount": 0.15,
    "cid": "sync_amend_client",
    "filled": 0.0,
    "filled_avg_price": 0.0,
    "id": "",
    "order_type": "Limit",
    "pos_side": null,
    "price": 52000.0,
    "quote_amount": null,
    "side": "Buy",
    "source": "Order",
    "status": "Open",
    "symbol": "BTC_USDT",
    "time_in_force": "GTC",
    "timestamp": 0
  },
  "sync": true
}
```

### 同步撤单

```json
{
  "account_id": 1,
  "method": "CancelOrder",
  "order_id": {
    "Id": "sync_cancel_order_id"
  },
  "symbol": "BTC_USDT",
  "sync": true
}
```

### 同步批量撤单

```json
{
  "account_id": 1,
  "method": "BatchCancelOrder",
  "symbol": "BTC_USDT",
  "sync": true
}
```

### 同步批量撤单根据订单ID

```json
{
  "account_id": 1,
  "cids": [
    "sync_client1",
    "sync_client2"
  ],
  "ids": [
    "sync_order1",
    "sync_order2"
  ],
  "method": "BatchCancelOrderById",
  "symbol": "BTC_USDT",
  "sync": true
}
```

### 查询持仓

```json
{
  "account_id": 1,
  "method": "Position",
  "symbol": "BTC_USDT",
  "sync": true
}
```

### 查询所有持仓

```json
{
  "account_id": 1,
  "method": "Positions",
  "sync": true
}
```

### 获取最大持仓

```json
{
  "account_id": 1,
  "method": "MaxPosition",
  "symbol": "BTC_USDT",
  "sync": true
}
```

### 查询结算资金费

```json
{
  "account_id": 1,
  "method": "FundingFee",
  "symbol": "BTC_USDT",
  "sync": true
}
```

### 最大杠杆

```json
{
  "account_id": 1,
  "method": "MaxLeverage",
  "symbol": "BTC_USDT",
  "sync": true
}
```

### 查询保证金模式

```json
{
  "account_id": 1,
  "method": "MarginMode",
  "symbol": "BTC_USDT",
  "sync": true
}
```

### 设置保证金模式

```json
{
  "account_id": 1,
  "margin_coin": "USDT",
  "margin_mode": "Cross",
  "method": "SetMarginMode",
  "symbol": "BTC_USDT",
  "sync": true
}
```

### 查询USDT余额

```json
{
  "account_id": 1,
  "method": "UsdtBalance",
  "sync": true
}
```

### 查询多币种余额

```json
{
  "account_id": 1,
  "method": "Balance",
  "sync": true
}
```

### 查询指定币种余额

```json
{
  "account_id": 1,
  "asset": "BTC",
  "method": "BalanceByCoin",
  "sync": true
}
```

### 获取手续费

```json
{
  "account_id": 1,
  "method": "FeeRate",
  "symbol": "BTC_USDT",
  "sync": true
}
```

### 设置杠杆

```json
{
  "account_id": 1,
  "leverage": 20,
  "method": "SetLeverage",
  "symbol": "BTC_USDT",
  "sync": true
}
```

### 查询是否双向持仓

```json
{
  "account_id": 1,
  "method": "IsDualSidePosition",
  "sync": true
}
```

### 设置双向持仓

```json
{
  "account_id": 1,
  "is_dual_side": true,
  "method": "SetDualSidePosition",
  "sync": true
}
```

### 万向划转

```json
{
  "account_id": 1,
  "method": "Transfer",
  "sync": true,
  "transfer": {
    "amount": 100.0,
    "asset": "USDT",
    "from": "Spot",
    "to": "UsdtFuture"
  }
}
```

### 子账户划转

```json
{
  "account_id": 1,
  "method": "SubTransfer",
  "sync": true,
  "transfer": {
    "amount": 50.0,
    "asset": "USDT",
    "cid": "sub_transfer_123",
    "direction": "SubToSub",
    "from": "Spot",
    "from_account": "sub_account_1",
    "to": "UsdtFuture",
    "to_account": "sub_account_2"
  }
}
```

### 获取充值地址

```json
{
  "account_id": 1,
  "amount": 100.0,
  "ccy": "USDT",
  "chain": "Trc20",
  "method": "GetDepositAddress",
  "sync": true
}
```

### 提币

```json
{
  "account_id": 1,
  "method": "Withdrawal",
  "params": {
    "addr": {
      "OnChain": {
        "address": "TRX_ADDRESS_EXAMPLE",
        "chain": "Trc20",
        "tag": null
      }
    },
    "amt": 100.0,
    "asset": "USDT",
    "cid": "withdrawal_client_123"
  },
  "sync": true
}
```

### 获取账户信息

```json
{
  "account_id": 1,
  "method": "GetAccountInfo",
  "sync": true
}
```

### 获取账户模式

```json
{
  "account_id": 1,
  "method": "GetAccountMode",
  "sync": true
}
```

### 设置账户模式

```json
{
  "account_id": 1,
  "method": "SetAccountMode",
  "mode": "MultiCurrency",
  "sync": true
}
```

### 获取用户ID

```json
{
  "account_id": 1,
  "method": "GetUserId",
  "sync": true
}
```

### 查询交易所折扣信息

```json
{
  "account_id": 1,
  "method": "GetFeeDiscountInfo",
  "sync": true
}
```

### 查询账户是否开启原生币手续费折扣

```json
{
  "account_id": 1,
  "method": "IsFeeDiscountEnabled",
  "sync": true
}
```

### 开关账户原生币手续费折扣

```json
{
  "account_id": 1,
  "enable": true,
  "method": "SetFeeDiscountEnabled",
  "sync": true
}
```

### 借币

```json
{
  "account_id": 1,
  "amount": 1000.0,
  "coin": "USDT",
  "method": "Borrow",
  "sync": true
}
```

### 查询借币

```json
{
  "account_id": 1,
  "coin": "USDT",
  "method": "GetBorrowed",
  "sync": true
}
```

### 还币

```json
{
  "account_id": 1,
  "amount": 500.0,
  "coin": "USDT",
  "method": "Repay",
  "sync": true
}
```

### 获取借贷利率

```json
{
  "account_id": 1,
  "coin": "USDT",
  "method": "GetBorrowRate",
  "sync": true
}
```

### 获取借贷限额

```json
{
  "account_id": 1,
  "coin": "USDT",
  "method": "GetBorrowLimit",
  "sync": true
}
```

### 查询24h交易信息

```json
{
  "account_id": 1,
  "method": "Ticker",
  "symbol": "BTC_USDT",
  "sync": true
}
```

### 查询所有交易对的24h交易信息

```json
{
  "account_id": 1,
  "method": "Tickers",
  "sync": true
}
```

### 查询最佳买卖价

```json
{
  "account_id": 1,
  "method": "Bbo",
  "symbol": "BTC_USDT",
  "sync": true
}
```

### 查询所有交易对的最佳买卖价

```json
{
  "account_id": 1,
  "method": "BboTickers",
  "sync": true
}
```

### 查询深度

```json
{
  "account_id": 1,
  "limit": 20,
  "method": "Depth",
  "symbol": "BTC_USDT",
  "sync": true
}
```

### 查询产品信息

```json
{
  "account_id": 1,
  "method": "Instrument",
  "symbol": "BTC_USDT",
  "sync": true
}
```

### 查询所有产品信息

```json
{
  "account_id": 1,
  "method": "Instruments",
  "sync": true
}
```

### 查询资金费率

```json
{
  "account_id": 1,
  "method": "FundingRate",
  "sync": true
}
```

### 查询单个交易对资金费率

```json
{
  "account_id": 1,
  "method": "FundingRateBySymbol",
  "symbol": "BTC_USDT",
  "sync": true
}
```

### 标记价格

```json
{
  "account_id": 1,
  "method": "MarkPrice",
  "symbol": "BTC_USDT",
  "sync": true
}
```

### K线数据

```json
{
  "account_id": 1,
  "end_time": *************,
  "interval": "1m",
  "limit": 100,
  "method": "Kline",
  "start_time": **********000,
  "symbol": "BTC_USDT",
  "sync": true
}
```

### 查询资金费率历史

```json
{
  "account_id": 1,
  "limit": 100,
  "method": "FundingRateHistory",
  "since_secs": **********,
  "symbol": "BTC_USDT",
  "sync": true
}
```

## 异步命令

异步命令会通过回调或事件返回结果。

### 异步下单

```json
{
  "account_id": 1,
  "method": "PlaceOrder",
  "order": {
    "amount": 0.1,
    "cid": "client123456",
    "filled": 0.0,
    "filled_avg_price": 0.0,
    "id": "",
    "order_type": "Limit",
    "pos_side": "Long",
    "price": 50000.0,
    "quote_amount": null,
    "side": "Buy",
    "source": "Order",
    "status": "Open",
    "symbol": "BTC_USDT",
    "time_in_force": "GTC",
    "timestamp": 0
  },
  "params": {
    "is_dual_side": false,
    "leverage": 10,
    "margin_mode": "Cross",
    "market_order_mode": "Safe",
    "market_order_slippage": 0.002
  },
  "sync": false
}
```

### 异步批量下单

```json
{
  "account_id": 1,
  "method": "BatchPlaceOrder",
  "orders": [
    {
      "amount": 0.1,
      "cid": "client123456",
      "filled": 0.0,
      "filled_avg_price": 0.0,
      "id": "",
      "order_type": "Limit",
      "pos_side": "Long",
      "price": 50000.0,
      "quote_amount": null,
      "side": "Buy",
      "source": "Order",
      "status": "Open",
      "symbol": "BTC_USDT",
      "time_in_force": "GTC",
      "timestamp": 0
    },
    {
      "amount": 1.0,
      "cid": "client789012",
      "filled": 0.0,
      "filled_avg_price": 0.0,
      "id": "",
      "order_type": "Limit",
      "pos_side": "Short",
      "price": 3000.0,
      "quote_amount": null,
      "side": "Sell",
      "source": "Order",
      "status": "Open",
      "symbol": "ETH_USDT",
      "time_in_force": "GTC",
      "timestamp": 0
    }
  ],
  "params": {
    "is_dual_side": false,
    "leverage": 10,
    "margin_mode": "Cross",
    "market_order_mode": "Safe",
    "market_order_slippage": 0.002
  },
  "sync": false
}
```

### 异步修改订单

```json
{
  "account_id": 1,
  "method": "AmendOrder",
  "order": {
    "amount": 0.15,
    "cid": "client123456",
    "filled": 0.0,
    "filled_avg_price": 0.0,
    "id": "",
    "order_type": "Limit",
    "pos_side": null,
    "price": 52000.0,
    "quote_amount": null,
    "side": "Buy",
    "source": "Order",
    "status": "Open",
    "symbol": "BTC_USDT",
    "time_in_force": "GTC",
    "timestamp": 0
  },
  "sync": false
}
```

### 异步撤单

```json
{
  "account_id": 1,
  "method": "CancelOrder",
  "order_id": {
    "Id": "test_order_id"
  },
  "symbol": "BTC_USDT",
  "sync": false
}
```

### 异步批量撤单

```json
{
  "account_id": 1,
  "method": "BatchCancelOrder",
  "symbol": "BTC_USDT",
  "sync": false
}
```

### 异步批量撤单根据订单ID

```json
{
  "account_id": 1,
  "cids": [
    "client1",
    "client2"
  ],
  "ids": [
    "order1",
    "order2"
  ],
  "method": "BatchCancelOrderById",
  "symbol": "BTC_USDT",
  "sync": false
}
```
