# Changelog 模板

## [x.y.z] - yyyy-mm-dd

### 💥 不兼容改动
- 修改了哪些会影响用户使用的功能
- 需要用户修改代码或配置的改动
- API 变更等重大改动

### ✨ 新特性
- 新增了哪些功能
- 新的 API 或配置选项
- 新增的特性说明

### 🐛 问题修复
- 修复了哪些 bug
- 解决了哪些已知问题
- 修复的问题描述和影响

### 🔧 优化改进
- 性能优化
- 代码重构
- 架构改进
- 依赖更新

### 📝 文档更新
- 文档改进
- 示例更新
- 注释完善

### 🔨 工具链
- CI/CD 改进
- 构建系统更新
- 开发工具改进

### 📦 其他改动
- 不属于以上类别的改动
- 小的改进或变更

## 升级指南

### 必要的修改
1. 需要修改的配置项
2. 需要更新的代码
3. 需要注意的兼容性问题

### 建议的修改
1. 推荐的最佳实践
2. 可选的优化建议
3. 新特性的使用建议

## 技术细节

### 架构变更
- 详细的架构改动说明
- 核心组件的变化

### 性能影响
- 性能改进的具体数据
- 可能的性能影响

### 依赖更新
- 主要依赖的版本变化
- 依赖更新的影响

## 注意事项
- 特别需要注意的问题
- 已知的限制
- 重要的提醒
