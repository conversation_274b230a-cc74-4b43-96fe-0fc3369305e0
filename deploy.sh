#!/bin/bash
#
# OpenQuant 构建脚本
# 用途：自动化构建、打包 OpenQuant 项目
# 使用方法：./deploy.sh [选项]
#

# 严格模式：遇到错误立即退出
set -e

# ==================== 颜色配置 ====================
GREEN='\033[0;32m'  # 成功信息
BLUE='\033[0;34m'   # 过程信息
YELLOW='\033[0;33m' # 警告信息
RED='\033[0;31m'    # 错误信息
NC='\033[0m'        # 重置颜色

# ==================== 辅助函数 ====================

# 打印带颜色的信息
print_msg() {
  local color=$1
  local msg=$2
  echo -e "${color}${msg}${NC}"
}

# 打印分隔行
print_separator() {
  print_msg $BLUE "====================================================="
}

# 打印帮助信息
print_help() {
  echo "用法: $0 [选项]"
  echo ""
  echo "选项:"
  echo "  --skip-build       跳过构建步骤"
  echo "  --windows          同时构建Windows版本"
  echo "  --cross-build      使用交叉编译模式构建"
  echo "  --target=<target>  指定交叉编译目标，例如：x86_64-unknown-linux-gnu"
  echo "  --skip-changelog   跳过生成变更日志"
  echo "  --github-release   创建GitHub发布（需要设置GITHUB_TOKEN）"
  echo "  --help             显示此帮助信息"
  echo ""
  echo "示例:"
  echo "  $0                                              # 标准Linux构建和打包"
  echo "  $0 --windows                                    # 同时构建Linux和Windows版本"
  echo "  $0 --cross-build --target=aarch64-unknown-linux-gnu # 交叉编译到ARM64 Linux"
  echo "  $0 --skip-build                                 # 跳过构建，仅打包"
}

# ==================== 核心功能 ====================

# 获取版本号
get_version() {
  print_msg $BLUE "📋 获取版本信息..."
  VERSION=$(grep '^version = ' Cargo.toml | cut -d '"' -f2)
  print_msg $GREEN "✅ 当前版本: ${VERSION}"
}

# 构建Linux版本
build_linux() {
  print_separator
  print_msg $BLUE "🔨 开始为Linux构建open_quant..."

  # 更新依赖
  print_msg $BLUE "📦 更新依赖..."
  cargo update

  # 使用cargo进行构建
  print_msg $BLUE "🔄 使用cargo进行构建..."
  cargo build --release

  print_msg $GREEN "✅ Linux构建完成"
}

# 构建Windows版本（可选）
build_windows() {
  print_separator
  print_msg $BLUE "🔨 开始为Windows构建open_quant..."

  # 设置Windows构建环境变量
  print_msg $BLUE "⚙️ 设置Windows构建环境变量..."
  export RUSTFLAGS="-C relocation-model=dynamic-no-pic"
  export PYTHON_CONFIGURE_OPTS="--enable-shared"
  export PYO3_CROSS_PYTHON_VERSION="3.10"
  export PYO3_CROSS_LIB_DIR="/usr/x86_64-w64-mingw32/lib"

  # 使用cargo进行构建
  print_msg $BLUE "🔄 使用cargo进行构建..."
  cargo build --release --target x86_64-pc-windows-gnu

  print_msg $GREEN "✅ Windows构建完成"
}

# 交叉编译构建
cross_build() {
  print_separator
  print_msg $BLUE "🔨 开始交叉编译open_quant到目标: ${TARGET}..."

  # 检查是否安装了cross工具
  if ! command -v cross &> /dev/null; then
    print_msg $YELLOW "⚠️ 未找到cross工具，尝试安装..."
    cargo install cross
  fi

  # 使用cross进行交叉编译
  print_msg $BLUE "🔄 使用cross进行交叉编译..."
  cross build --release --target ${TARGET}

  print_msg $GREEN "✅ 交叉编译完成，目标: ${TARGET}"
}

# 生成变更日志
generate_changelog() {
  print_separator
  print_msg $BLUE "📝 生成CHANGELOG.md..."

  # 如果变更日志不存在，创建一个
  if [ ! -f "CHANGELOG.md" ]; then
    print_msg $BLUE "📄 创建新的变更日志文件..."
    echo "# 变更日志" > CHANGELOG.md
    echo "" >> CHANGELOG.md
    echo "## [${VERSION}] - $(date +%Y-%m-%d)" >> CHANGELOG.md
    echo "" >> CHANGELOG.md

    # 从git log提取最近的提交信息
    print_msg $BLUE "🔍 从Git历史提取最近的提交..."
    git log -10 --pretty=format:"- %s" >> CHANGELOG.md
  else
    print_msg $YELLOW "⚠️ 变更日志文件已存在，跳过创建"
  fi

  print_msg $GREEN "✅ 变更日志生成完成"
}

# 创建发布包
create_release_package() {
  print_separator
  print_msg $BLUE "📦 创建发布包..."

  # 清理并创建发布目录
  print_msg $BLUE "🧹 清理并准备发布目录..."
  # 确保 misc/release 目录存在
  mkdir -p misc/release
  rm -rf misc/release/temp
  mkdir -p misc/release/temp

  # 复制配置文件
  print_msg $BLUE "📋 复制配置文件..."
  if [ -d "misc" ]; then
    rsync -az --exclude="__pycache__" misc/examples misc/release/temp/
    rsync -az misc/base_strategy.py misc/release/temp/
    rsync -az misc/config.toml misc/release/temp/
    rsync -az misc/docs misc/release/temp/
  else
    print_msg $YELLOW "⚠️ misc目录不存在，跳过配置文件复制"
  fi

  # 复制构建产物
  print_msg $BLUE "📋 复制构建产物..."

  # 优先检查交叉编译的构建结果
  if [ "$DO_CROSS_BUILD" = true ] && [ -n "$TARGET" ]; then
    BUILD_PATH="target/${TARGET}/release/open_quant"
    if [ -f "$BUILD_PATH" ]; then
      print_msg $BLUE "📋 复制交叉编译构建产物 ($TARGET)..."
      rsync -az "$BUILD_PATH" misc/release/temp/
    else
      print_msg $RED "❌ 错误：找不到交叉编译构建产物: $BUILD_PATH"
      print_msg $RED "请确保已经成功完成交叉编译"
      exit 1
    fi
  # 然后检查Windows构建结果
  elif [ -f "target/x86_64-pc-windows-gnu/release/open_quant.exe" ]; then
    print_msg $BLUE "📋 复制Windows构建产物..."
    rsync -az target/x86_64-pc-windows-gnu/release/open_quant.exe misc/release/temp/
  # 最后检查本地构建结果
  elif [ -f "target/release/open_quant" ]; then
    print_msg $BLUE "📋 复制标准Linux构建产物..."
    rsync -az target/release/open_quant misc/release/temp/
  else
    print_msg $RED "❌ 错误：找不到构建产物"
    print_msg $RED "请确保已经成功构建项目或使用 --skip-build 选项时已有构建产物"
    exit 1
  fi

  # 复制Windows构建产物（如果有）
  if [ "$BUILD_WINDOWS" = true ] && [ -f "target/x86_64-pc-windows-gnu/release/open_quant.exe" ]; then
    print_msg $BLUE "📋 复制Windows构建产物..."
    rsync -az target/x86_64-pc-windows-gnu/release/open_quant.exe misc/release/temp/
  fi

  # 复制变更日志
  print_msg $BLUE "📋 复制变更日志..."
  rsync -az CHANGELOG.md misc/release/temp/

  # 创建压缩包
  print_msg $BLUE "🗜️ 创建压缩包..."

  # 根据目标平台调整压缩包名称
  PACKAGE_SUFFIX=""
  if [ "$DO_CROSS_BUILD" = true ] && [ -n "$TARGET" ]; then
    PACKAGE_SUFFIX="_${TARGET}"
  elif [ "$BUILD_WINDOWS" = true ]; then
    PACKAGE_SUFFIX="_windows"
  fi

  cd misc/release/temp
  tar -cvzf "../open_quant_${VERSION}${PACKAGE_SUFFIX}.tar.gz" ./*
  cd ../../..

  print_msg $GREEN "✅ 发布包创建完成: misc/release/open_quant_${VERSION}${PACKAGE_SUFFIX}.tar.gz"
}

# GitHub发布功能（可选）
github_release() {
  print_separator
  print_msg $BLUE "🚀 开始创建GitHub发布..."

  # 检查是否安装了gh工具
  if ! command -v gh &> /dev/null; then
    print_msg $YELLOW "⚠️ 未找到GitHub CLI，尝试安装..."
    mkdir -p $HOME/.local/bin
    curl -L https://github.com/cli/cli/releases/latest/download/gh_$(curl -s https://api.github.com/repos/cli/cli/releases/latest | grep -o '"tag_name": ".*"' | cut -d'"' -f4 | cut -c2-)_linux_amd64.tar.gz | tar xz
    mv gh_*/bin/gh $HOME/.local/bin/
    export PATH=$HOME/.local/bin:$PATH
  fi

  # 检查GitHub Token
  if [ -z "$GITHUB_TOKEN" ]; then
    print_msg $RED "❌ 错误：未设置GITHUB_TOKEN环境变量"
    print_msg $YELLOW "请设置GITHUB_TOKEN环境变量后再运行: export GITHUB_TOKEN=your_token"
    exit 1
  fi

  # 提取最新版本的变更记录
  print_msg $BLUE "📄 提取最新变更记录..."
  awk '/^## \[.*\]/{if(p)exit; p=1}p' CHANGELOG.md > LATEST_CHANGES.md

  # 创建标签（如果不存在）
  TAG="v${VERSION}"
  if ! git rev-parse "$TAG" >/dev/null 2>&1; then
    print_msg $BLUE "🏷️ 创建Git标签 $TAG..."
    git tag "$TAG"
    git push origin "$TAG"
  fi

  # 确定要上传的文件
  RELEASE_FILES=()

  # 根据目标平台添加对应的发布包
  if [ "$DO_CROSS_BUILD" = true ] && [ -n "$TARGET" ]; then
    RELEASE_FILES+=("misc/release/open_quant_${VERSION}_${TARGET}.tar.gz")
  else
    RELEASE_FILES+=("misc/release/open_quant_${VERSION}.tar.gz")
  fi

  if [ "$BUILD_WINDOWS" = true ]; then
    RELEASE_FILES+=("misc/release/open_quant_${VERSION}_windows.tar.gz")
  fi

  # 创建GitHub发布
  print_msg $BLUE "🚀 创建GitHub发布..."
  REPO="open-quant-hub/OpenQuant"  # 请替换为实际仓库

  UPLOAD_COMMAND="gh release create \"$TAG\" --repo \"$REPO\" --title \"Release $TAG\" --notes-file LATEST_CHANGES.md"

  for file in "${RELEASE_FILES[@]}"; do
    UPLOAD_COMMAND+=" \"$file\""
  done

  eval $UPLOAD_COMMAND

  print_msg $GREEN "✅ GitHub发布创建成功"
}

# ==================== 主函数 ====================
main() {
  print_msg $BLUE "🚀 OpenQuant构建脚本"
  print_separator

  # 解析命令行参数
  SKIP_BUILD=false
  BUILD_WINDOWS=false
  DO_CROSS_BUILD=false
  TARGET=""
  SKIP_CHANGELOG=false
  DO_GITHUB_RELEASE=false

  while [[ "$#" -gt 0 ]]; do
    case $1 in
      --skip-build) SKIP_BUILD=true ;;
      --windows) BUILD_WINDOWS=true ;;
      --cross-build) DO_CROSS_BUILD=true ;;
      --target=*) TARGET="${1#*=}" ;;
      --skip-changelog) SKIP_CHANGELOG=true ;;
      --github-release) DO_GITHUB_RELEASE=true ;;
      --help) print_help; exit 0 ;;
      *) print_msg $RED "❌ 未知参数: $1"; print_help; exit 1 ;;
    esac
    shift
  done

  # 获取版本信息
  get_version

  # 验证交叉编译设置
  if [ "$DO_CROSS_BUILD" = true ] && [ -z "$TARGET" ]; then
    print_msg $RED "❌ 错误：启用交叉编译时必须指定目标平台 (--target=<target>)"
    print_help
    exit 1
  fi

  # 执行构建步骤
  if [ "$SKIP_BUILD" = false ]; then
    if [ "$DO_CROSS_BUILD" = true ]; then
      cross_build
    else
      build_linux
      if [ "$BUILD_WINDOWS" = true ]; then
        build_windows
      fi
    fi
  else
    print_msg $YELLOW "⚠️ 跳过构建步骤"
  fi

  # 生成变更日志
  if [ "$SKIP_CHANGELOG" = false ]; then
    generate_changelog
  else
    print_msg $YELLOW "⚠️ 跳过变更日志生成"
  fi

  # 创建发布包
  create_release_package

  # GitHub发布（仅当显式启用时执行）
  if [ "$DO_GITHUB_RELEASE" = true ]; then
    github_release
  fi

  print_separator

  # 打印完成信息
  if [ "$DO_CROSS_BUILD" = true ] && [ -n "$TARGET" ]; then
    print_msg $GREEN "✅ 构建完成！"
    print_msg $GREEN "📦 发布包: misc/release/open_quant_${VERSION}_${TARGET}.tar.gz"
  else
    print_msg $GREEN "✅ 构建完成！"
    print_msg $GREEN "📦 发布包: misc/release/open_quant_${VERSION}.tar.gz"
  fi
}

# 运行主函数
main "$@"
