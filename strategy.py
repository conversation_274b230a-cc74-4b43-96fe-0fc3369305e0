import json
import time

import traderv2 # type: ignore
import base_strategy

class Strategy(base_strategy.BaseStrategy):
    def __init__(self, cex_configs, dex_configs, config, trader: traderv2.TraderV2):
        self.trader = trader
        self.send_order = False
        pass

    def name(self):
        return "Backtest"

    def start(self):
        pass

    def subscribes(self):
        subs = [
             {
                "account_id": 0,
                "sub": {
                    "SubscribeWs": [
                        {
                            "OrderAndFill": ["BTC_USDT"]
                        },
                        {
                            "Bbo": ["BTC_USDT"]
                        },
                        {
                            "Trade": ["BTC_USDT"]
                        }
                    ],
                }
            },
            {
                "account_id": 0,
                "sub": {
                    "SubscribeRest": {
                        "update_interval": {"secs": 5, "nanos": 0},
                        "rest_type": "Balance"
                    }
                }
            },
        ]
        return subs

    def on_bbo(self, exchange, bbo):
        self.trader.log(f"收到BBO数据: {bbo}", level="INFO", color="blue")
        if self.send_order == True:
            return
        self.send_order = True
        return {
            'cmds': [
                {
                    'account_id': 0,
                    'method': 'PlaceOrder',
                    'order': {
                        'symbol': 'BTC_USDT',
                        'order_type': 'Limit',
                        'side': 'Buy',
                        'price': 107291.991,
                        'amount': 0.003618410869409628,
                        'time_in_force': 'IOC',
                        'cid': 'BinanceSwap429705378',
                        'pos_side': None
                    },
                    'params': {
                        'is_dual_side': False,
                        'margin_mode': 'Cross',
                        'leverage': 50
                    }
                }
            ]
        }
        # self.trader.log(f"收到BBO数据: {bbo}", level="INFO", color="blue")

    def on_depth(self, exchange, depth):
        pass

    def on_order_submitted(self, account_id, order_id_result, order):
        pass

    def on_order_and_fill(self, account_id, order):
        pass

    def on_order(self, account_id, order):
        pass

    def on_trade(self, exchange, trade):
        self.trader.log(f"收到成交数据: {trade}", level="INFO", color="blue")

    def on_order_submitted(self, account_id, order_id_result, order):
        self.trader.log(f"收到订单提交回调: {order_id_result} {order}", level="INFO", color="blue")
        pass

    def on_order_and_fill(self, account_id, order):
        self.trader.log(f"订单/用户私有成交更新: {order}", level="INFO", color="blue")

    def on_balance(self, account_id, balances):
        self.trader.log(f"收到余额更新: {balances}", level="INFO", color="blue")
