#!/bin/bash

echo "=== OpenSSL兼容性修复 ==="

# 强制使用系统OpenSSL而不是Conda的
export PATH="/usr/bin:/bin:/usr/sbin:/sbin:$PATH"

# 设置OpenSSL兼容性环境变量
export OPENSSL_CONF=/dev/null
export SSL_CERT_FILE=/etc/ssl/certs/ca-certificates.crt
export SSL_CERT_DIR=/etc/ssl/certs

# 降低TLS安全级别以兼容更多服务器
export SECLEVEL=1

# 设置更宽松的密码套件
export OPENSSL_CIPHERS="HIGH:!aNULL:!eNULL:!EXPORT:!DES:!RC4:!MD5:!PSK:!SRP:!CAMELLIA"

# 允许代理证书
export OPENSSL_ALLOW_PROXY_CERTS=1

# 设置日志级别
export RUST_LOG=${RUST_LOG:-info}

echo "当前OpenSSL版本:"
/usr/bin/openssl version

echo "环境变量已设置，启动程序..."
cargo run --release "$@"
