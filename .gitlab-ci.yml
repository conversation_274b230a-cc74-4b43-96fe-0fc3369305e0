# 定义阶段
stages:
  - build-linux
  - generate-release-notes
  - create-release-package
  - feishu-notification
  # - github-release
  - release-feishu-manual

build-linux:
  stage: build-linux
  tags:
    - shell
  rules:
    - if: $CI_PIPELINE_SOURCE == "trigger" && $CI_COMMIT_BRANCH == "dev"
      when: always
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "dev"
      when: always
  script:
    - echo "Starting build open_quant for Linux"
    - |
      if [ -n "$TRIGGER_COMMIT_MESSAGE" ]; then
        echo "=== 触发信息 ==="
        echo "原始提交用户: ${TRIGGER_USER_NAME:-unknown}"
        echo "上游项目: ${TRIGGER_PROJECT:-unknown}" 
        echo "上游分支: ${TRIGGER_BRANCH:-unknown}"
        echo "上游提交消息: $TRIGGER_COMMIT_MESSAGE"
        echo "当前pipeline创建人: ${GITLAB_USER_NAME:-unknown}"
        echo "触发源: ${TRIGGER_SOURCE:-unknown}"
        echo "==============="
      else
        echo "本地构建"
        echo "提交用户: ${GITLAB_USER_NAME:-unknown}"
        echo "提交消息: $CI_COMMIT_MESSAGE"
      fi
    - cargo update
    - cargo build --release --target x86_64-unknown-linux-gnu

  artifacts:
    paths:
      - target/x86_64-unknown-linux-gnu/release/open_quant
    expire_in: 1 week

# # Windows 构建任务 (合并 open_quant 和 wheel)
# build-windows:
#   stage: build
#   tags:
#     - shell
#   variables:
#     RUSTFLAGS: "-C relocation-model=dynamic-no-pic"
#     PYTHON_CONFIGURE_OPTS: "--enable-shared"
#     PYO3_CROSS_PYTHON_VERSION: "3.10"
#     PYO3_CROSS_LIB_DIR: "/usr/x86_64-w64-mingw32/lib"
#   script:
#     # 构建 open_quant
#     - echo "Starting build open_quant for Windows..."
#     - cargo build --release --target x86_64-pc-windows-gnu

#   artifacts:
#     paths:
#       - target/x86_64-pc-windows-gnu/release/open_quant.exe
#     expire_in: 1 week

# 生成发布说明
generate-release-notes:
  stage: generate-release-notes
  tags:
    - shell
  script:
    - |
      echo "Generating RELEASE_NOTES.md..."
      
      # 优先使用上游传递的commit message和用户信息
      if [ -n "$TRIGGER_COMMIT_MESSAGE" ]; then
        COMMIT_MSG="$TRIGGER_COMMIT_MESSAGE"
        USER_NAME="$TRIGGER_USER_NAME"
        COMMIT_SOURCE="来自上游项目: ${TRIGGER_PROJECT:-unknown}"
        echo "使用上游提交消息: $COMMIT_MSG"
        echo "原始提交用户: $USER_NAME"
      else
        COMMIT_MSG="$CI_COMMIT_MESSAGE" 
        USER_NAME="$GITLAB_USER_NAME"
        COMMIT_SOURCE="本地构建"
        echo "使用当前项目提交消息: $COMMIT_MSG"
        echo "提交用户: $USER_NAME"
      fi
      
      # 获取当前版本
      VERSION=$(grep '^version = ' Cargo.toml | cut -d '"' -f2)
      CURRENT_DATE=$(date +"%Y-%m-%d")
      
      # 生成发布说明文件（不覆盖现有CHANGELOG.md）
      echo "# Release Notes" > RELEASE_NOTES.md
      echo "" >> RELEASE_NOTES.md
      echo "## OpenQuant v${VERSION} - ${CURRENT_DATE}" >> RELEASE_NOTES.md
      echo "" >> RELEASE_NOTES.md
      echo "### 🚀 构建信息" >> RELEASE_NOTES.md
      echo "- **版本号**: ${VERSION}" >> RELEASE_NOTES.md
      echo "- **提交用户**: ${USER_NAME:-unknown}" >> RELEASE_NOTES.md
      echo "- **构建来源**: ${COMMIT_SOURCE}" >> RELEASE_NOTES.md
      echo "- **构建时间**: ${CURRENT_DATE}" >> RELEASE_NOTES.md
      echo "" >> RELEASE_NOTES.md
      echo "### 📝 变更内容" >> RELEASE_NOTES.md
      echo "- ${COMMIT_MSG}" >> RELEASE_NOTES.md
      echo "" >> RELEASE_NOTES.md
      echo "### 📋 完整变更日志" >> RELEASE_NOTES.md
      echo "请查看项目根目录的 CHANGELOG.md 文件获取详细的历史变更记录。" >> RELEASE_NOTES.md
      
      echo "已生成 RELEASE_NOTES.md，提交消息: $COMMIT_MSG，用户: $USER_NAME"
  artifacts:
    paths:
      - RELEASE_NOTES.md
    expire_in: 1 week

# 打包任务
create-release-package:
  stage: create-release-package
  tags:
    - shell
  needs:
    - build-linux
    # - build-windows
    - generate-release-notes
  script:
    - VERSION=$(grep '^version = ' Cargo.toml | cut -d '"' -f2)
    - mkdir -p release_package
    # 复制配置文件
    - rsync -av misc/config.toml release_package/
    - rsync -av misc/base_strategy.py release_package/
    # 复制示例
    - rsync -avz --exclude="__pycache__" misc/examples release_package/
    - rsync -avz misc/docs release_package/
    # 复制构建产物
    - cp target/x86_64-unknown-linux-gnu/release/open_quant release_package/
    # - cp target/x86_64-pc-windows-gnu/release/open_quant.exe release_package/
    - cp CHANGELOG.md release_package/
    # 创建 zip 包
    - cd release_package
    - tar -cvzf "../open_quant_${VERSION}.tar.gz" ./*
  artifacts:
    paths:
      - open_quant_*.tar.gz
    expire_in: 1 week

# 飞书通知任务
feishu-notification:
  stage: feishu-notification
  tags:
    - shell
  needs:
    - create-release-package
    - generate-release-notes
  script:
    - |
      # 获取版本号和基本信息
      VERSION=$(grep '^version = ' Cargo.toml | cut -d '"' -f2)
      PACKAGE_SIZE=$(du -h "open_quant_${VERSION}.tar.gz" | cut -f1)
      
      # 检查必要文件
      if [ ! -f "open_quant_${VERSION}.tar.gz" ] || [ ! -f "RELEASE_NOTES.md" ]; then
        echo "❌ 缺少必要文件"
        exit 1
      fi
      
      # 构建commit信息（统一格式）
      if [ -n "$UPSTREAM_COMMIT_SHA" ] && [ -n "$UPSTREAM_PROJECT" ]; then
        if [ "$UPSTREAM_PROJECT" = "quant_common" ]; then
          COMMIT_URL="https://git.nb8.net/newquant/${UPSTREAM_PROJECT}/-/commit/${UPSTREAM_COMMIT_SHA}"
        else
          COMMIT_URL="https://git.nb8.net/exchanges/${UPSTREAM_PROJECT}/-/commit/${UPSTREAM_COMMIT_SHA}"
        fi
        COMMIT_INFO="🔗 Commit: [${UPSTREAM_COMMIT_SHA:0:8}](${COMMIT_URL})"
      else
        COMMIT_URL="https://git.nb8.net/${CI_PROJECT_PATH}/-/commit/${CI_COMMIT_SHA}"
        COMMIT_INFO="🔗 Commit: [${CI_COMMIT_SHA:0:8}](${COMMIT_URL})"
      fi

      # 优化TRIGGER_INFO结构
      if [ -n "$TRIGGER_COMMIT_MESSAGE" ]; then
        TRIGGER_INFO="原始用户: ${TRIGGER_USER_NAME:-unknown} 项目: ${TRIGGER_PROJECT:-unknown}\n💬 消息: $TRIGGER_COMMIT_MESSAGE"
      else
        TRIGGER_INFO="用户: ${GITLAB_USER_NAME:-unknown}\n💬 消息: $CI_COMMIT_MESSAGE"
      fi

      # 构建格式化的消息内容（去掉说明部分，结构更清晰）
      MSG_CONTENT="🚀 OpenQuant v${VERSION} 发布完成\n\n📦 版本: ${VERSION}\n📏 大小: ${PACKAGE_SIZE}\n\n👤 ${TRIGGER_INFO}\n\n${COMMIT_INFO}\n🔗 [流水线](${CI_PIPELINE_URL})"
      
      echo "发送飞书通知..."
      
      # 飞书应用方式
      if [ -n "$FEISHU_APP_ID" ] && [ -n "$FEISHU_APP_SECRET" ] && [ -n "$FEISHU_CHAT_ID" ]; then
        echo "使用飞书应用..."
        
        # 获取access_token
        ACCESS_TOKEN=$(curl -s -X POST "https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal" \
          -H "Content-Type: application/json" \
          -d "{\"app_id\":\"$FEISHU_APP_ID\",\"app_secret\":\"$FEISHU_APP_SECRET\"}" | \
          grep -o '"app_access_token":"[^"]*"' | cut -d'"' -f4)
        
        if [ -n "$ACCESS_TOKEN" ]; then
          # 发送文本消息
          if command -v jq &> /dev/null; then
            # 处理换行符，确保飞书能正确显示
            MSG_ESCAPED=$(echo "$MSG_CONTENT" | sed 's/\\n/\n/g')
            CONTENT_STR=$(jq -n --arg text "$MSG_ESCAPED" '{"text": $text}' | jq -c .)
            JSON_PAYLOAD=$(jq -n --arg chat_id "$FEISHU_CHAT_ID" --arg content "$CONTENT_STR" '{
              receive_id: $chat_id, msg_type: "text", content: $content
            }')
          else
            # 简化消息格式，避免换行符问题
            SIMPLE_MSG="🚀 OpenQuant v${VERSION} 发布完成 | 大小: ${PACKAGE_SIZE} | ${TRIGGER_INFO}"
            CONTENT_STR="{\\\"text\\\":\\\"$SIMPLE_MSG\\\"}"
            JSON_PAYLOAD="{\"receive_id\":\"$FEISHU_CHAT_ID\",\"msg_type\":\"text\",\"content\":\"$CONTENT_STR\"}"
          fi
          
          HTTP_CODE=$(curl -s -o response.json -w "%{http_code}" -X POST \
            "https://open.feishu.cn/open-apis/im/v1/messages?receive_id_type=chat_id" \
            -H "Authorization: Bearer $ACCESS_TOKEN" \
            -H "Content-Type: application/json" \
            -d "$JSON_PAYLOAD")
          
          if [ "$HTTP_CODE" = "200" ]; then
            echo "✅ 消息发送成功"
            
            # 上传文件
            UPLOAD_RESPONSE=$(curl -s -X POST "https://open.feishu.cn/open-apis/im/v1/files" \
              -H "Authorization: Bearer $ACCESS_TOKEN" \
              -F "file_type=stream" \
              -F "file_name=open_quant_${VERSION}.tar.gz" \
              -F "file=@open_quant_${VERSION}.tar.gz")
            
            FILE_KEY=$(echo "$UPLOAD_RESPONSE" | grep -o '"file_key":"[^"]*"' | cut -d'"' -f4)
            
            if [ -n "$FILE_KEY" ]; then
              # 发送文件消息
              if command -v jq &> /dev/null; then
                FILE_CONTENT_STR=$(jq -n --arg file_key "$FILE_KEY" '{"file_key": $file_key}' | jq -c .)
                FILE_JSON_PAYLOAD=$(jq -n --arg chat_id "$FEISHU_CHAT_ID" --arg content "$FILE_CONTENT_STR" '{
                  receive_id: $chat_id, msg_type: "file", content: $content
                }')
              else
                FILE_CONTENT_STR="{\\\"file_key\\\":\\\"$FILE_KEY\\\"}"
                FILE_JSON_PAYLOAD="{\"receive_id\":\"$FEISHU_CHAT_ID\",\"msg_type\":\"file\",\"content\":\"$FILE_CONTENT_STR\"}"
              fi
              
              FILE_HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" -X POST \
                "https://open.feishu.cn/open-apis/im/v1/messages?receive_id_type=chat_id" \
                -H "Authorization: Bearer $ACCESS_TOKEN" \
                -H "Content-Type: application/json" \
                -d "$FILE_JSON_PAYLOAD")
              
              if [ "$FILE_HTTP_CODE" = "200" ]; then
                echo "✅ 文件上传成功"
              else
                echo "⚠️  文件上传失败"
              fi
            fi
          else
            echo "❌ 消息发送失败 HTTP:$HTTP_CODE"
            [ -f response.json ] && cat response.json
            exit 1
          fi
        else
          echo "❌ 获取access_token失败"
          exit 1
        fi
        
      # 群机器人方式
      elif [ -n "$FEISHU_WEBHOOK_URL" ]; then
        echo "使用群机器人..."
        
        if command -v jq &> /dev/null; then
          # 处理换行符，确保飞书能正确显示
          MSG_ESCAPED=$(echo "$MSG_CONTENT" | sed 's/\\n/\n/g')
          CONTENT_STR=$(jq -n --arg text "$MSG_ESCAPED" '{"text": $text}' | jq -c .)
          WEBHOOK_PAYLOAD=$(jq -n --arg content "$CONTENT_STR" '{
            msg_type: "text", content: $content
          }')
        else
          # 简化消息格式，避免换行符问题
          SIMPLE_MSG="🚀 OpenQuant v${VERSION} 发布完成 | 大小: ${PACKAGE_SIZE} | ${TRIGGER_INFO}"
          CONTENT_STR="{\\\"text\\\":\\\"$SIMPLE_MSG\\\"}"
          WEBHOOK_PAYLOAD="{\"msg_type\":\"text\",\"content\":\"$CONTENT_STR\"}"
        fi
        
        HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" -X POST \
          -H "Content-Type: application/json" \
          -d "$WEBHOOK_PAYLOAD" \
          "$FEISHU_WEBHOOK_URL")
        
        if [ "$HTTP_CODE" = "200" ]; then
          echo "✅ 群机器人通知成功"
        else
          echo "❌ 群机器人通知失败 HTTP:$HTTP_CODE"
          exit 1
        fi
        
      else
        echo "❌ 缺少飞书配置"
        echo "请配置 FEISHU_APP_ID + FEISHU_APP_SECRET + FEISHU_CHAT_ID 或 FEISHU_WEBHOOK_URL"
        exit 1
      fi
      
      # 清理
      [ -f response.json ] && rm response.json

# # 发布任务
# github-release:
#   stage: github-release
#   tags:
#     - shell
#   cache:
#     key: github-cli
#     paths:
#       - $HOME/.local/bin/gh
#   before_script:
#     - export PATH=$HOME/.local/bin:$PATH  # 确保 gh 在 PATH 中
#   rules:
#     - if: $CI_COMMIT_TAG
#   variables:
#     GH_TOKEN: $GITHUB_TOKEN
#     GH_REPO: "open-quant-hub/OpenQuant"   # 替换为你的仓库名
#     CI_SERVER_URL: "https://git.nb8.net/"
#   script:
#     - VERSION=${CI_COMMIT_TAG#v}

#     # 检查必要的环境变量
#     - |
#       if [ -z "$GITHUB_TOKEN" ]; then
#         echo "Error: GITHUB_TOKEN is not set"
#         exit 1
#       fi

#     # 只在 gh 命令不存在时安装
#     - |
#       if ! command -v gh &> /dev/null; then
#         echo "Installing GitHub CLI..."
#         mkdir -p $HOME/.local/bin
#         curl -L https://github.com/cli/cli/releases/latest/download/gh_$(curl -s https://api.github.com/repos/cli/cli/releases/latest | grep -o '"tag_name": ".*"' | cut -d'"' -f4 | cut -c2-)_linux_amd64.tar.gz | tar xz
#         mv gh_*/bin/gh $HOME/.local/bin/
#         export PATH=$HOME/.local/bin:$PATH
#       fi

#     # 下载和处理构建产物
#     - |
#       # # 下载并解压 artifacts.zip
#       # curl --location --output artifacts.zip \
#       #   "${CI_SERVER_URL}/api/v4/projects/${CI_PROJECT_ID}/jobs/artifacts/dev/download?job=create-release-package" \
#       #   --header "JOB-TOKEN: ${CI_JOB_TOKEN}"

#       # echo "Listing current directory contents:"
#       # ls -la

#       echo "Listing current directory contents:"
#       ls -la

#       # 检查所需文件是否已存在
#       if [ ! -f "open_quant_${VERSION}.tar.gz" ] || [ ! -f "CHANGELOG.md" ]; then
#         echo "Required files not found, downloading and extracting artifacts..."
#         curl --location --output artifacts.zip \
#           "${CI_SERVER_URL}/api/v4/projects/${CI_PROJECT_ID}/jobs/artifacts/dev/download?job=create-release-package" \
#           --header "JOB-TOKEN: ${CI_JOB_TOKEN}"

#         echo "Unzipping artifacts.zip..."
#         unzip artifacts.zip
#       else
#         echo "Required files already exist, skipping download and extraction"
#       fi

#       # 检查 tar 文件
#       echo "Checking tar file:"
#       file "open_quant_${VERSION}.tar.gz"

#       # 使用 tar 命令而不是 tar xzf(去掉 z 选项)
#       echo "Extracting tar file..."
#       tar xf "open_quant_${VERSION}.tar.gz"

#       echo "Listing extracted contents:"
#       ls -la

#       if [ ! -f "CHANGELOG.md" ]; then
#         echo "Error: CHANGELOG.md not found"
#         exit 1
#       fi

#     # 提取最新版本的变更记录
#     - |
#       echo "Extracting latest version changes from CHANGELOG.md..."
#       awk '/^## \[.*\]/{if(p)exit; p=1}p' CHANGELOG.md > LATEST_CHANGES.md

#       if [ ! -s LATEST_CHANGES.md ]; then
#         echo "Error: Failed to extract latest changes"
#         exit 1
#       fi

#       echo "Latest changes:"
#       cat LATEST_CHANGES.md

#     # 使用 GitHub CLI 创建 release
#     - |
#       # 设置环境变量
#       export GITHUB_TOKEN="${GH_TOKEN}"

#       # 验证认证状态
#       if ! gh auth status; then
#         echo "Failed to authenticate with GitHub"
#         exit 1
#       fi

#       # 创建 release
#       gh release create "${CI_COMMIT_TAG}" \
#         --repo "${GH_REPO}" \
#         --title "Release ${CI_COMMIT_TAG}" \
#         --notes-file LATEST_CHANGES.md \
#         "open_quant_${VERSION}.tar.gz"

release-feishu-manual:
  stage: release-feishu-manual
  tags:
    - shell
  needs:
    - create-release-package
  when: manual
  script:
    - VERSION=$(grep '^version = ' Cargo.toml | cut -d '"' -f2)
    - PKG_FILE="open_quant_${VERSION}.tar.gz"
    - |
      # 提取CHANGELOG.md最新tag部分
      awk '/^## \[.*\]/{if(x)exit;x=1}x' CHANGELOG.md > LATEST_CHANGELOG.md
      # 只保留第一节内容
      awk '/^## \[.*\]/{if(NR!=1)exit} {print}' LATEST_CHANGELOG.md > LATEST_CHANGELOG_SECTION.md
      CHANGELOG_TEXT=$(cat LATEST_CHANGELOG_SECTION.md)
      MSG_CONTENT=$(echo -e "OpenQuant 发布包\n\n${CHANGELOG_TEXT}")
      # 获取access_token
      ACCESS_TOKEN=$(curl -s -X POST "https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal" \
        -H "Content-Type: application/json" \
        -d "{\"app_id\":\"$FEISHU_APP_ID\",\"app_secret\":\"$FEISHU_APP_SECRET\"}" | \
        grep -o '"app_access_token":"[^"]*"' | cut -d'"' -f4)
      # 发送文本消息
      if command -v jq &> /dev/null; then
        CONTENT_STR=$(jq -n --arg text "$MSG_CONTENT" '{"text": $text}' | jq -c .)
        JSON_PAYLOAD=$(jq -n --arg chat_id "$FEISHU_RELEASE_CHAT_ID" --arg content "$CONTENT_STR" '{
          receive_id: $chat_id, msg_type: "text", content: $content
        }')
      else
        CONTENT_STR="{\\\"text\\\":\\\"$MSG_CONTENT\\\"}"
        JSON_PAYLOAD="{\"receive_id\":\"$FEISHU_RELEASE_CHAT_ID\",\"msg_type\":\"text\",\"content\":\"$CONTENT_STR\"}"
      fi
      curl -s -X POST \
        "https://open.feishu.cn/open-apis/im/v1/messages?receive_id_type=chat_id" \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        -H "Content-Type: application/json" \
        -d "$JSON_PAYLOAD"
      # 上传文件
      UPLOAD_RESPONSE=$(curl -s -X POST "https://open.feishu.cn/open-apis/im/v1/files" \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        -F "file_type=stream" \
        -F "file_name=$PKG_FILE" \
        -F "file=@$PKG_FILE")
      FILE_KEY=$(echo "$UPLOAD_RESPONSE" | grep -o '"file_key":"[^"]*"' | cut -d'"' -f4)
      if [ -n "$FILE_KEY" ]; then
        if command -v jq &> /dev/null; then
          FILE_CONTENT_STR=$(jq -n --arg file_key "$FILE_KEY" '{"file_key": $file_key}' | jq -c .)
          FILE_JSON_PAYLOAD=$(jq -n --arg chat_id "$FEISHU_RELEASE_CHAT_ID" --arg content "$FILE_CONTENT_STR" '{
            receive_id: $chat_id, msg_type: "file", content: $content
          }')
        else
          FILE_CONTENT_STR="{\\\"file_key\\\":\\\"$FILE_KEY\\\"}"
          FILE_JSON_PAYLOAD="{\"receive_id\":\"$FEISHU_RELEASE_CHAT_ID\",\"msg_type\":\"file\",\"content\":\"$FILE_CONTENT_STR\"}"
        fi
        curl -s -X POST \
          "https://open.feishu.cn/open-apis/im/v1/messages?receive_id_type=chat_id" \
          -H "Authorization: Bearer $ACCESS_TOKEN" \
          -H "Content-Type: application/json" \
          -d "$FILE_JSON_PAYLOAD"
      fi
  variables:
    FEISHU_RELEASE_CHAT_ID: "请在CI/CD变量中配置你的release群聊ID"
