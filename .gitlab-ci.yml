stages:
  - trigger-open-quant

trigger-open-quant-build:
  stage: trigger-open-quant
  tags:
    - shell
  rules:
    - if: $CI_COMMIT_BRANCH == "dev" && $CI_PIPELINE_SOURCE == "trigger"
  script:
    - |
      curl --request POST \
        --form "token=$OPEN_QUANT_TRIGGER_TOKEN" \
        --form "ref=dev" \
        --form "variables[TRIGGER_SOURCE]=trader-dev" \
        --form "variables[TRIGGER_USER_NAME]=$GITLAB_USER_NAME" \
        --form "variables[TRIGGER_COMMIT_SHA]=$CI_COMMIT_SHA" \
        --form "variables[TRIGGER_COMMIT_MESSAGE]=$CI_COMMIT_MESSAGE" \
        --form "variables[TRIGGER_PROJECT]=trader" \
        --form "variables[TRIGGER_BRANCH]=$CI_COMMIT_BRANCH" \
        --form "variables[UPSTREAM_QUANT_API]=$TRIGGER_PROJECT" \
        --form "variables[UPSTREAM_EXCHANGE]=$UPSTREAM_EXCHANGE" \
        "https://git.nb8.net/api/v4/projects/108/trigger/pipeline"