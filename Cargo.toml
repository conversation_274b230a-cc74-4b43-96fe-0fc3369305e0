[package]
edition = "2024"
name = "trader"
version = "0.2.8"

[workspace]
members = ["."]

[workspace.dependencies]
quant_api = { git = "ssh://***************:8022/newquant/quant_api.git", branch = "dev" }
# quant_api = { path = "../quant_api" }
quant_common = { git = "ssh://***************:8022/newquant/quant_common.git", branch = "dev" }
algo_common = { git = "ssh://***************:8022/openquant/algo_common.git", branch = "main" }
# algo_common = {path = "../algo_common"}
downloader = { git = "ssh://***************:8022/openquant/downloader.git", branch = "main" }
# discount_coiner = { git = "ssh://***************:8022/quant/discountcoiner.git", branch = "main" }

serde_json = "1.0"
tokio = { version = "1.40.0", default-features = false, features = [
    "net",
    "io-util",
    "macros",
    "fs",
    "rt-multi-thread",
] }
tracing = "0.1"

[dependencies]
quant_api.workspace = true
quant_common.workspace = true
algo_common.workspace = true
downloader.workspace = true

serde_json.workspace = true
tokio.workspace = true
tracing.workspace = true

async-channel = "2.3.1"
crossbeam-channel = "0.5.13"
enum_dispatch = "0.3"
log = "0.4.22"
minstant = { git = "ssh://***************:8022/quant/minstant.git", version = "0.1" }
once_cell = "1.20"
reqwest = { version = "0.12", default-features = false, features = [
    "rustls-tls",
    "stream",
] }
rustc-hash = "2.0"
serde = "1.0"
serde_plain = "1.0"
sonic-rs = { version = "0.3", default-features = false }
time = { version = "0.3", features = ["macros"] }
toml = "0.8"
tracing-appender = "0.2"
tracing-log = "0.2.0"
tracing-subscriber = { version = "0.3", features = [
    "fmt",
    "time",
    "env-filter",
    "json",
] }
futures-util = "0.3"
zeromq = { version = "0.4", default-features = false, features = ["tokio-runtime", "all-transport"] }
bytes = "1.9"
async-trait = "0.1"
sha2 = "0.10"
uuid = { version = "1.4", features = ["v4"] }
dashmap = "6.1"
chrono = "0.4"
indicatif = "0.17"
notify = "7.0"
notify-debouncer-mini = "0.5"

[dev-dependencies]
tempfile = "3.13"

[[example]]
name = "test_dex"
path = "examples/test_dex.rs"

[target.'cfg(unix)'.dependencies]
nix = { version = "0.29", features = ["signal", "process"] }

[target.'cfg(windows)'.dependencies]
windows = { version = "0.58", features = ["Win32_Foundation", "Win32_System_Threading"] }

[profile.release]
codegen-units = 1
lto = "fat"
