#[macro_use]
extern crate tracing;

use bytes::Bytes;
use quant_common::{Result, qerror};
use serde::{Deserialize, Serialize};
use serde_json::{Value, json};
use std::time::{Duration, Instant};
use tokio::{sync::mpsc, time};
use zeromq::{
    DealerSocket, Socket, SocketOptions, SocketRecv, SocketSend, ZmqMessage, util::PeerIdentity,
};

#[derive(Serialize, Deserialize, Debug)]
struct Message {
    #[serde(rename = "type")]
    msg_type: String,
    data: Value,
}

// 定义消息类型
#[derive(Debug)]
enum SocketMessage {
    Send(Message),
    Stop,
}

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志
    tracing_subscriber::fmt()
        .with_max_level(tracing::Level::DEBUG)
        .with_thread_ids(true)
        .with_target(false)
        .with_file(true)
        .with_line_number(true)
        .with_ansi(false)
        .init();

    let ipc_path = std::env::var("IPC").expect("需要设置 IPC 环境变量");
    let id = std::env::var("ID")
        .expect("需要设置 ID 环境变量")
        .parse::<String>()
        .unwrap();

    let test_key = std::env::var("TEST_KEY").expect("需要设置 TEST_KEY 环境变量");
    let api_key = std::env::var("API_KEY").expect("需要设置 API_KEY 环境变量");

    info!("DEX 客户端配置:");
    info!("ID: {}", id);
    info!("IPC: {}", ipc_path);
    info!("TEST_KEY: {}", test_key);
    info!("API_KEY: {}", api_key);

    info!("启动 DEX 客户端 ID: {}", id);
    info!("连接到 IPC 地址: {}", ipc_path);

    // 创建并连接 socket
    let (stop_tx, mut stop_rx) = mpsc::channel(1);
    let (pong_tx, mut pong_rx) = mpsc::channel(100);
    let (socket_tx, mut socket_rx) = mpsc::channel::<SocketMessage>(100);

    // socket 处理循环
    tokio::spawn(async move {
        let mut socket = {
            let mut option = SocketOptions::default();
            option.peer_identity(PeerIdentity::try_from(Bytes::from(id.clone()))?);
            let mut socket = DealerSocket::with_options(option);
            socket.connect(&format!("ipc://{ipc_path}")).await?;
            socket
        };

        let mut send = false;

        loop {
            tokio::select! {
                Some(msg) = socket_rx.recv() => {
                    match msg {
                        SocketMessage::Send(msg) => {
                            if (send || msg.msg_type == "ping")
                                && let Err(e) = send_message(&mut socket, &msg).await {
                                    error!("发送消息失败: {}", e);
                                }
                        }
                        SocketMessage::Stop => break,
                    }
                }
                msg = socket.recv() => {
                    debug!("id: {} 收到消息: {:?}", id, msg);
                    match msg {
                        Ok(msg) => {
                            if let Some(payload) = msg.get(0) {
                                if payload.is_empty() {
                                    // 心跳响应
                                    let _ = pong_tx.send(()).await;
                                }
                                if let Ok(msg) = serde_json::from_slice::<Message>(payload) {
                                    match msg.msg_type.as_str() {
                                        "command" => {
                                            info!("收到命令: {}", msg.data);
                                            if let Err(e) = send_message(&mut socket, &msg).await {
                                                error!("发送消息失败: {}", e);
                                            }
                                        }
                                        "sync_cmd" => {
                                            send = true;
                                        }
                                        _ => {
                                            warn!("收到未知类型消息: {:?}", msg);
                                        }
                                    }
                                }
                            }
                        }
                        Err(e) => error!("接收消息失败: {}", e),
                    }
                }
            }
        }
        Ok::<_, quant_common::Error>(())
    });

    // 启动心跳检测
    info!("启动心跳检测任务");
    let stop_tx_clone = stop_tx.clone();
    tokio::spawn(async move {
        let timeout = Duration::from_secs(25);
        let mut last_pong = Instant::now();

        loop {
            tokio::select! {
                _ = time::sleep(Duration::from_secs(5)) => {
                    if Instant::now().duration_since(last_pong) > timeout {
                        error!("心跳超时 {}秒，准备退出", timeout.as_secs());
                        let _ = stop_tx_clone.send(()).await;
                        break;
                    }
                }
                Some(_) = pong_rx.recv() => {
                    debug!("收到 pong 响应");
                    last_pong = Instant::now();
                }
            }
        }
    });

    // 在主循环之前添加测试数据发送任务
    let socket_tx_test = socket_tx.clone();
    tokio::spawn(async move {
        let mut interval = time::interval(Duration::from_secs(3));
        loop {
            interval.tick().await;

            // 发送测试数据
            let test_data = serde_json::json!({
                "price": 100.5,
                "size": 1.5,
                "side": "buy",
            });

            let msg = Message {
                msg_type: "data".to_string(),
                data: test_data,
            };

            debug!("发送测试数据");
            if let Err(e) = socket_tx_test.send(SocketMessage::Send(msg)).await {
                error!("发送测试数据失败: {}", e);
                break;
            }

            // 发送日志
            let log_msg = Message {
                msg_type: "log".to_string(),
                data: json!({
                    "level": "DEBUG",
                    "msg": "测试",
                }),
            };
            if let Err(e) = socket_tx_test.send(SocketMessage::Send(log_msg)).await {
                error!("发送日志失败: {}", e);
                break;
            }
        }
    });

    // 主循环发送消息
    info!("开始主循环");
    let mut heartbeat_interval = time::interval(Duration::from_secs(5));
    loop {
        tokio::select! {
            _ = stop_rx.recv() => {
                info!("收到停止信号，准备退出");
                let _ = socket_tx.send(SocketMessage::Stop).await;
                break;
            }
            _ = heartbeat_interval.tick() => {
                // 发送心跳
                debug!("发送心跳");
                let msg = Message {
                    msg_type: "ping".to_string(),
                    data: json!({}),
                };
                let _ = socket_tx.send(SocketMessage::Send(msg)).await;
            }
        }
    }

    info!("客户端已退出");
    Ok(())
}

async fn send_message(socket: &mut DealerSocket, msg: &Message) -> Result<()> {
    let payload = Bytes::from(serde_json::to_vec(msg)?);
    let frames = vec![payload];

    trace!("发送消息: {:?}", msg);
    let msg = ZmqMessage::try_from(frames).map_err(|e| qerror!("创建消息失败: {}", e))?;
    socket.send(msg).await?;
    Ok(())
}
