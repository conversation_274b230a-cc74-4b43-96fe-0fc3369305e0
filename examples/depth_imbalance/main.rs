#[macro_use]
extern crate tracing;

use quant_common::{Result, load_toml_file};
use strategy::MyStrategy;
use trader::{launcher::Launcher, logo::print_logo};

mod strategy;

#[tokio::main]
async fn main() -> Result<()> {
    print_logo();
    let mut args = std::env::args();
    let path = args.nth(1).unwrap_or_else(|| "config.toml".to_string());
    let config = load_toml_file(&path)?;
    let mut launcher = Launcher::new(config, "depth_imbalance", false).await?;
    let execution_engine = launcher.execution_engine();
    let strategy = MyStrategy::new(execution_engine).await?;
    launcher.start(strategy).await?;
    Ok(())
}
