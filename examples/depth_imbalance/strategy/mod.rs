use std::{sync::Arc, time::Duration};

use async_trait::async_trait;
use quant_common::{
    Result,
    base::{
        Balance, BboTicker, Depth, DepthWsParams, Exchange, Instrument, Position, Rest,
        SubscribeChannel, Symbol, traits::instrument::Instrument<PERSON><PERSON><PERSON>,
    },
};
use trader::{
    execution::{ExecutionEngine, StaticExecutionEngine},
    model::{
        account::AccountId,
        context::Context,
        data_source::{RestSubscribe, RestType, StrategySubscribe, StrategySubscribeInner},
    },
    strategy::Strategy,
};

#[derive(Clone)]
pub struct MyStrategy {
    symbols: Arc<Vec<Symbol>>,
    _instruments: Vec<Instrument>,
    _execution_engine: Arc<StaticExecutionEngine>,
}

impl MyStrategy {
    pub async fn new(execution_engine: Arc<StaticExecutionEngine>) -> Result<Self> {
        let fetched_instruments = execution_engine
            .execution_rest(0, |rest| async move { rest.get_instruments().await })
            .await
            .unwrap()?;

        let symbols = Arc::new(vec![
            Symbol::new("BTC"),
            Symbol::new("ETH"),
            Symbol::new("SOL"),
        ]);

        let mut instruments = Vec::new();

        for symbol in symbols.iter() {
            let instrument = fetched_instruments
                .iter()
                .find(|instrument| &instrument.symbol == symbol)
                .unwrap();
            instruments.push(instrument.clone());
        }

        Ok(Self {
            symbols,
            _instruments: instruments,
            _execution_engine: execution_engine,
        })
    }
}

#[async_trait]
impl Strategy for MyStrategy {
    fn name(&self) -> &'static str {
        "simple_strategy"
    }

    async fn start(&self) -> Result<()> {
        Ok(())
    }

    async fn subscribes(&self) -> Result<Vec<StrategySubscribe>> {
        Ok(vec![
            StrategySubscribe {
                account_id: 0,
                sub: StrategySubscribeInner::SubscribeWs(vec![
                    SubscribeChannel::Bbo(self.symbols.clone()),
                    SubscribeChannel::Depth(DepthWsParams {
                        symbols: self.symbols.clone(),
                        levels: 20,
                    }),
                ]),
            },
            StrategySubscribe {
                account_id: 0,
                sub: StrategySubscribeInner::SubscribeWs(vec![
                    SubscribeChannel::Position(self.symbols.clone()),
                    SubscribeChannel::Order(self.symbols.clone()),
                ]),
            },
            StrategySubscribe {
                account_id: 0,
                sub: StrategySubscribeInner::SubscribeRest(RestSubscribe {
                    update_interval: Duration::from_secs(120),
                    rest_type: RestType::Balance,
                }),
            },
        ])
    }

    /// 全部持仓回调
    ///
    /// # 参数
    /// -exchange: 交易所类型
    /// -account_id: 账户ID
    /// -positions: 全部持仓
    async fn on_position(&self, account_id: AccountId, positions: Vec<Position>) -> Result<()> {
        info!("account_id: {:?}, on_position: {:?}", account_id, positions);
        Ok(())
    }

    /// 全部币种余额回调
    ///
    /// # 参数
    /// -exchange: 交易所类型
    /// -account_id: 账户ID
    /// -balances: 全部币种余额
    async fn on_balance(&self, account_id: AccountId, balances: Vec<Balance>) -> Result<()> {
        info!("account_id: {:?}, on_balance: {:?}", account_id, balances);
        Ok(())
    }

    /// BBO行情回调
    ///
    /// # 参数
    /// -exchange: 交易所类型
    /// -ticker: BBO行情
    async fn on_bbo(&self, exchange: Exchange, _context: Context, ticker: BboTicker) -> Result<()> {
        trace!("exchange: {:?}, bbo: {:?}", exchange, ticker);
        Ok(())
    }

    /// 深度行情回调
    ///
    /// # 参数
    /// -exchange: 交易所类型
    /// -depth: 深度行情
    async fn on_depth(&self, exchange: Exchange, _context: Context, depth: Depth) -> Result<()> {
        trace!("exchange: {:?}, depth: {:?}", exchange, depth);
        Ok(())
    }

    /// 停止程序
    async fn on_stop(&self) -> Result<()> {
        info!("stopping strategy");
        Ok(())
    }
}

#[async_trait]
impl InstrumentHandler for MyStrategy {
    async fn on_instrument_updated(
        &self,
        exchange: Exchange,
        instruments: Vec<Instrument>,
    ) -> Result<()> {
        info!("exchange: {:?}, instruments: {:?}", exchange, instruments);
        Ok(())
    }

    async fn on_instrument_removed(&self, exchange: Exchange, symbols: Vec<Symbol>) -> Result<()> {
        info!("exchange: {:?}, symbols: {:?}", exchange, symbols);
        Ok(())
    }

    async fn on_instrument_added(
        &self,
        exchange: Exchange,
        instruments: Vec<Instrument>,
    ) -> Result<()> {
        info!("exchange: {:?}, instruments: {:?}", exchange, instruments);
        Ok(())
    }
}
