FROM ghcr.io/cross-rs/x86_64-unknown-linux-gnu:main-centos

# 修复镜像源
RUN sed -i 's/mirrorlist/#mirrorlist/g' /etc/yum.repos.d/CentOS-* && \
    sed -i 's|#baseurl=http://mirror.centos.org|baseurl=http://vault.centos.org|g' /etc/yum.repos.d/CentOS-*

# 安装基础开发工具
RUN yum -y install epel-release && \
    yum -y install gcc gcc-c++ make openssl-devel \
    perl perl-core bzip2 wget

# 安装Miniconda
RUN wget https://repo.anaconda.com/miniconda/Miniconda3-py310_23.11.0-2-Linux-x86_64.sh -O /tmp/miniconda.sh && \
    bash /tmp/miniconda.sh -b -p /opt/conda && \
    rm /tmp/miniconda.sh && \
    ln -s /opt/conda/bin/python /usr/local/bin/python3 && \
    ln -s /opt/conda/bin/pip /usr/local/bin/pip3 && \
    ln -s /opt/conda/bin/pip /usr/local/bin/pip

# 设置Python库文件链接，解决链接器无法找到libpython3.10.so的问题
RUN find /opt/conda -name "libpython3.10*.so*" -exec ln -sf {} /usr/lib64/ \; && \
    find /opt/conda -name "libpython3.10*.so*" && \
    ls -la /usr/lib64/libpython3.10*

# 设置环境变量
ENV PATH="/opt/conda/bin:${PATH}"
ENV LD_LIBRARY_PATH="/opt/conda/lib:${LD_LIBRARY_PATH}"

# 设置OpenSSL环境变量
ENV OPENSSL_NO_VENDOR=1
ENV OPENSSL_DIR=/usr
ENV OPENSSL_INCLUDE_DIR=/usr/include
ENV OPENSSL_LIB_DIR=/usr/lib64
ENV X86_64_UNKNOWN_LINUX_GNU_OPENSSL_DIR=/usr
ENV X86_64_UNKNOWN_LINUX_GNU_OPENSSL_INCLUDE_DIR=/usr/include
ENV X86_64_UNKNOWN_LINUX_GNU_OPENSSL_LIB_DIR=/usr/lib64

# 设置Python库路径环境变量
ENV LIBRARY_PATH="/opt/conda/lib:${LIBRARY_PATH}"
ENV PYTHON_LIBRARY_PATH="/opt/conda/lib"
ENV PYO3_PYTHON="/opt/conda/bin/python3"

# 安装Python相关工具
RUN pip install --upgrade pip && \
    pip install setuptools wheel && \
    pip install pyo3-pack maturin

# 设置工作目录
WORKDIR /app

CMD ["/bin/bash"]
