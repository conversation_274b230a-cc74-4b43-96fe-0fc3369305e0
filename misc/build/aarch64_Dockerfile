FROM ghcr.io/cross-rs/aarch64-unknown-linux-gnu

# 设置非交互模式
ENV DEBIAN_FRONTEND=noninteractive

# 初始化环境变量
ENV LD_LIBRARY_PATH="/usr/lib/aarch64-linux-gnu:/usr/lib"
ENV LIBRARY_PATH="/usr/lib/aarch64-linux-gnu:/usr/lib"

# 更新软件源并安装必要工具
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    wget \
    perl \
    python3.10 \
    python3.10-pip \
    python3.10-dev \
    libpython3.10-dev

# # 升级 pip 并安装必要的 Python 包
# RUN python3 -m pip install --upgrade pip && \
#     python3 -m pip install setuptools wheel pyo3-pack maturin

# # 创建必要的符号链接
# RUN ln -sf $(which python3) /usr/local/bin/python && \
#     ln -sf $(which pip3) /usr/local/bin/pip

# 设置 OpenSSL 环境变量
# ENV OPENSSL_NO_VENDOR=1
# ENV OPENSSL_DIR=/usr
# ENV OPENSSL_INCLUDE_DIR=/usr/include
# ENV OPENSSL_LIB_DIR=/usr/lib/aarch64-linux-gnu
# ENV AARCH64_UNKNOWN_LINUX_GNU_OPENSSL_DIR=/usr
# ENV AARCH64_UNKNOWN_LINUX_GNU_OPENSSL_INCLUDE_DIR=/usr/include
# ENV AARCH64_UNKNOWN_LINUX_GNU_OPENSSL_LIB_DIR=/usr/lib/aarch64-linux-gnu

# 设置 Python 库路径环境变量
ENV PYTHON_LIBRARY_PATH="/usr/lib/python3/dist-packages"
ENV PYO3_PYTHON="/usr/bin/python3"

# 设置交叉编译环境变量
ENV CARGO_TARGET_AARCH64_UNKNOWN_LINUX_GNU_LINKER=aarch64-linux-gnu-gcc
ENV CC_aarch64_unknown_linux_gnu=aarch64-linux-gnu-gcc
ENV CXX_aarch64_unknown_linux_gnu=aarch64-linux-gnu-g++

# 设置工作目录
WORKDIR /app

CMD ["/bin/bash"]
