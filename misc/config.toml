strategy_path = "examples/depth_imbalance_v2/strategy.py"
strategy_config_path = "examples/depth_imbalance_v2/strategy.toml"
instruments_refresh_sec = 300
trader_version = "V2"

[web]
is_production = false
secret_id = "xxx"
secret_key = "xxxx"

# [log]
# # file: 文件路径，如果填入文件名，则写入文件
# file = "logs/depth_imbalance"
# # level: 日志级别 支持 trace, debug, info, warn, error
# level = "trace"
# 日志限频 interval设置为0则不限制
# max_tokens：令牌桶最大容量，同时决定初始令牌数
# interval：令牌桶令牌生成速率 单位：秒 默认0秒 不限流（实盘运行时建议设置为1秒）
# rate_limit = {max_tokens = 50, interval = 0}

# 市场数据源处理方式
# 支持 Latest: 只处理最新数据，如果有新数据会把上一个等待处理的数据顶掉避免顺序处理引入的延迟，不同symbol不同通道，避免互相阻塞
#         All: 全部数据顺序处理
# [data_source.market_mode]
# mark_price = "Latest"
# bbo = "Latest"
# depth = "Latest"
# funding = "All"
# trade = "All"

[[exchanges]]
exchange = "BinanceSwap"
is_colo = false
is_testnet = false
is_unified = false
key = ""
passphrase = ""
rebate_rate = 0
secret = ""

# 是否使用多ip轮询rest请求
multi_ip = false

# 用于http请求的host
# 一般置为None使用默认的域名，需要使用自定义域名的话覆盖即可
# host = "xxxx.com"
# 用于websocket请求的host
# 一般置为None使用默认的域名，需要使用自定义域名的话覆盖即可
# ws_host = "xxxx.com"
# 用于websocket api请求的host
# 一般置为None使用默认的域名，需要使用自定义域名的话覆盖即可
# ws_api_host = "xxxx.com"
# params用于指定非标参数
#     如 cookie: 网页cookie           bitget swap、coinex swap支持
#         code:   一般配合cookie使用   bitget swap、coinex swap支持
#         org_id: 机构id               bitget swap、spot、margin和coinex swap支持
#         discount: 平台币折扣信息
# params = {cookie = "",code = ""}
# 使用websocket api去下单，默认 BinanceSpot、OkxSwap、OkxSpot、OkxMargin为true BinanceSwap、GateSwap、GateSpot为false 其余暂不支持
use_ws_api = false

[[exchanges]]
exchange = "BinanceSpot"
is_colo = false
is_testnet = false
is_unified = false
key = ""
passphrase = ""
rebate_rate = 0
secret = ""

[[exchanges]]
exchange = "CoinexSwap"
is_colo = false
is_testnet = false
is_unified = false
key = ""
passphrase = ""
rebate_rate = 0
secret = ""

[[exchanges]]
exchange = "GateSwap"
is_colo = false
is_testnet = false
is_unified = false
key = ""
passphrase = ""
rebate_rate = 0
secret = ""

[[exchanges]]
exchange = "GateSpot"
is_colo = false
is_testnet = false
is_unified = false
key = ""
passphrase = ""
rebate_rate = 0
secret = ""

[[exchanges]]
exchange = "BitgetSwap"
is_colo = false
is_testnet = false
is_unified = false
key = ""
passphrase = ""
rebate_rate = 0
secret = ""
# params = {cookie = "",code = ""}

[[exchanges]]
exchange = "OkxSwap"
is_colo = false
is_testnet = false
is_unified = false
key = ""
passphrase = ""
rebate_rate = 0
secret = ""

[[exchanges]]
exchange = "OkxSpot"
is_colo = false
is_testnet = false
is_unified = false
key = ""
passphrase = ""
rebate_rate = 0
secret = ""

[[exchanges]]
exchange = "HuobiSwap"
is_colo = false
is_testnet = false
is_unified = false
key = ""
passphrase = ""
rebate_rate = 0
secret = ""

[[exchanges]]
exchange = "HuobiSpot"
is_colo = false
is_testnet = false
is_unified = false
key = ""
passphrase = ""
rebate_rate = 0
secret = ""

[[exchanges]]
exchange = "KucoinSpot"
is_colo = false
is_testnet = false
is_unified = false
key = ""
passphrase = ""
rebate_rate = 0
secret = ""

[[exchanges]]
exchange = "KucoinSwap"
is_colo = false
is_testnet = false
is_unified = false
key = ""
passphrase = ""
rebate_rate = 0
secret = ""

[[exchanges]]
exchange = "CryptoSwap"
is_colo = false
is_testnet = false
is_unified = false
key = ""
passphrase = ""
rebate_rate = 0
secret = ""

[[exchanges]]
exchange = "CryptoSpot"
is_colo = false
is_testnet = false
is_unified = false
key = ""
passphrase = ""
rebate_rate = 0
secret = ""

[[exchanges]]
exchange = "BingxSwap"
is_colo = false
is_testnet = false
is_unified = false
key = ""
passphrase = ""
rebate_rate = 0
secret = ""

[[exchanges]]
exchange = "BingxSpot"
is_colo = false
is_testnet = false
is_unified = false
key = ""
passphrase = ""
rebate_rate = 0
secret = ""

[[exchanges]]
exchange = "BitmexSwap"
is_colo = false
is_testnet = false
is_unified = false
key = ""
passphrase = ""
rebate_rate = 0
secret = ""

[[exchanges]]
exchange = "BitmexSpot"
is_colo = false
is_testnet = false
is_unified = false
key = ""
passphrase = ""
rebate_rate = 0
secret = ""

[[exchanges]]
exchange = "BybitSwap"
is_colo = false
is_testnet = false
is_unified = true
key = ""
passphrase = ""
rebate_rate = 0
secret = ""

[[exchanges]]
exchange = "BybitSpot"
is_colo = false
is_testnet = false
is_unified = true
key = ""
passphrase = ""
rebate_rate = 0
secret = ""

[[exchanges]]
exchange = "CoinbaseSpot"
is_colo = false
is_testnet = false
is_unified = false
key = ""
passphrase = ""
rebate_rate = 0
secret = ""

[[exchanges]]
exchange = "KrakenSpot"
is_colo = false
is_testnet = false
is_unified = false
key = ""
passphrase = ""
rebate_rate = 0
secret = ""
