# 📊 Trader API 文档

## 📌 概述

`Trader` 是一个高性能量化交易引擎，基于Rust开发并通过PyO3绑定为Python接口。它提供了执行交易指令、日志管理、缓存操作、外部通信以及与NB8 Web平台集成的全套功能。

---

## 📑 API分类索引

| 分类                             | 功能模块             | 主要用途          |
| ------------------------------ | ---------------- | ------------- |
| [一、核心交易功能](#一核心交易功能)           | 交易所api指令执行、标识符生成     | 执行交易所api、生成唯一ID |
| [二、日志管理](#二日志管理)               | 日志记录             | 记录系统和交易信息     |
| [三、缓存管理](#三缓存管理)               | 缓存操作、缓存系统说明      | 保存和恢复策略状态     |
| [四、外部通信](#四外部通信)               | HTTP请求           | 获取外部数据        |
| [五、NB8 Web平台集成](#五nb8-web平台集成) | Web客户端管理等六个模块    | 与Web平台交互和数据展示 |
| [六、常见指令格式](#六常见指令格式)           | 查询指令、下单指令、取消订单指令 | 常用指令的标准格式     |

---

## 一、核心交易功能

### 1.1 交易指令执行

#### ▶️ publish(cmd)

**功能**: 向交易引擎发送并执行单个交易指令

**参数**:

- `cmd`: 交易指令对象，符合ExecutionCommand结构

**返回值**:

- 指令执行结果，序列化为Python对象

**示例**:

```python
# 查询USDT余额的例子
cmd = {
    "account_id": 0,
    "cmd": {
        "Sync": "UsdtBalance"  # 查询USDT余额
    }
}
result = trader.publish(cmd)
print(f"指令执行结果: {result}")

# 下单指令示例
place_order_cmd = {
    "account_id": 0,
    "cmd": {
        "Sync": {
            "PlaceOrder": [
                {
                    "symbol": "BTC_USDT",
                    "order_type": "Limit",
                    "side": "Buy",
                    "price": 50000.0,
                    "amount": 0.01,
                    "time_in_force": "GTC"
                },
                {
                    "is_dual_side": False,
                    "market_order_mode": "Normal"
                }
            ]
        }
    }
}
result = trader.publish(place_order_cmd)
print(f"下单结果: {result}")
```

#### ▶️ batch_publish(cmds)

**功能**: 批量执行多个交易指令

**参数**:

- `cmds`: 交易指令对象列表

**返回值**:

- 多个指令执行结果的列表

**示例**:

```python
# 批量执行多个交易指令
cmds = [
    {
        "account_id": 0,
        "cmd": {"Sync": "UsdtBalance"}  # 查询USDT余额
    },
    {
        "account_id": 0,
        "cmd": {"Sync": {"BalanceByCoin": "BTC"}}  # 查询BTC余额
    }
]
results = trader.batch_publish(cmds)
for i, result in enumerate(results):
    print(f"指令{i+1}结果: {result}")
```

### 1.2 唯一标识符生成

#### 🆔 create_cid()

**功能**: 创建唯一的客户端标识符

**参数**: 无

**返回值**:

- 唯一的标识符字符串

**用途**:

- 用于标识和跟踪交易订单
- 在高频交易中避免重复订单
- 关联策略生成的订单与回报信息
- 作为交易请求的唯一引用

**示例**:

```python
# 创建唯一的客户端标识符
cid = trader.create_cid()
print(f"生成的订单ID: {cid}")

# 在下单时使用生成的CID
place_order_cmd = {
    "account_id": 0,
    "cmd": {
        "Sync": {
            "PlaceOrder": [
                {
                    "symbol": "BTC_USDT",
                    "order_type": "Limit",
                    "side": "Buy",
                    "price": 50000.0,
                    "amount": 0.01,
                    "cid": cid  # 使用生成的CID
                }
            ]
        }
    }
}
```

---

## 二、日志管理

### 2.1 日志记录

#### 📝 log(msg, level=None, color=None, web=True)

**功能**: 记录日志到控制台和Web平台

**参数**:

- `msg`: 日志消息内容
- `level`: 日志级别，可选值: "TRACE", "DEBUG", "INFO", "WARN", "ERROR"，默认为"INFO"
- `color`: 日志颜色，可选，默认为None
- `web`: 是否发送到Web平台，默认为True

**返回值**: 无

**示例**:

```python
# 无色日志，默认发送到web平台
trader.log("交易开始")
# 红色日志，发送到web平台
trader.log("价格异常", "WARN","red")
# 不发送到web平台
trader.log("调试信息", "DEBUG", web=False)
# 发送错误日志到平台
trader.log("错误", "ERROR")
# 多行日志使用\n分割
trader.log("多行日志\n第二行\n第三行")
```

#### 📝 tlog(tag, msg, color=None, interval=0, level=None, query=False)

**功能**: 限频日志记录，每N秒最多打印一次指定标签的日志

**参数**:

- `tag`: 日志标识
- `msg`: 日志内容
- `color`: 日志颜色，可选，默认为None
- `interval`: 日志最小间隔（秒），默认为0
- `level`: 日志级别，可选，默认为"INFO"
- `query`: 是否只查询不更新时间，默认为False

**返回值**:

- `bool` - True表示已发送日志，False表示未发送日志

**示例**:

```python
# 每60秒最多记录一次市场状态
trader.tlog("市场状态", "市场流动性正常", interval=60)

# 使用特定颜色记录警告信息
trader.tlog("价差警告", "价差超过阈值", color="yellow", level="WARN")

# 不更新时间戳
should_log = trader.tlog("检测点", "价格波动", interval=30, query=True)
if should_log:
    # 执行额外操作
    pass

# 多行日志记录
trader.tlog("深度分析",
           f"BTC-USDT 深度\n买: [(50000, 1.2), (49900, 2.5)]\n卖: [(50100, 1.5), (50200, 3.0)]\n不平衡: 0.8750",
           interval=5,
           level="DEBUG",
           color="royalblue")
```

#### 📝 logt(message, time, color=None, level=None)

**功能**: 使用指定时间戳记录日志

**参数**:

- `message`: 日志内容
- `time`: 日志时间戳（Unix时间戳）
- `color`: 日志颜色，可选，默认为None
- `level`: 日志级别，可选，默认为"INFO"

**返回值**: 无

**示例**:

```python
import time

# 记录1小时前的事件
past_time = int(time.time()) - 3600
trader.logt("历史事件记录", past_time)

# 使用特定颜色和级别记录日志
event_time = int(time.time())
trader.logt("重要事件", event_time, color="blue", level="INFO")
```

### 2.2 内置颜色表

日志相关函数（如`log`、`tlog`和`logt`）支持通过`color`参数设置文本颜色。系统支持多种颜色，按功能和使用场景分类如下：

#### 交易状态颜色

| 颜色名称   | 十六进制代码  | 交易应用场景               |
| ------ | ------- | -------------------- |
| green  | #32CD32 | 交易成功、盈利交易、多头信号、买入信号  |
| red    | #FF4500 | 交易失败、亏损交易、空头信号、卖出信号  |
| blue   | #0000FF | 交易执行、订单提交、中性交易状态     |
| yellow | #FFFF00 | 交易警告、风险提示、接近止损点      |

> **提示**: 系统还支持直接使用十六进制颜色代码，格式为`#RRGGBB`，例如`#FF7F50`（珊瑚红）。

---

## 三、缓存管理

### 3.1 缓存操作

#### 💾 cache_save(data)

**功能**: 将数据保存到缓存系统

**参数**:

- `data`: 要保存的数据字符串，通常为JSON格式

**返回值**: 无

**示例**:

```python
# 保存策略状态到缓存
import json
import time

# 准备要保存的策略状态
state = {
    "positions": [
        {"symbol": "BTC_USDT", "amount": 0.05, "entry_price": 49800, "unrealized_pnl": 120},
        {"symbol": "ETH_USDT", "amount": 0.8, "entry_price": 3200, "unrealized_pnl": 80}
    ],
    "orders": [
        {"symbol": "BTC_USDT", "order_id": "123456", "status": "open"},
        {"symbol": "ETH_USDT", "order_id": "789012", "status": "filled"}
    ],
    "statistics": {
        "total_profit": 1250.5,
        "trade_count": 48,
        "win_rate": 0.75
    },
    "last_update": int(time.time())
}

# 将状态转换为JSON字符串并保存
trader.cache_save(json.dumps(state))
```

#### 🔄 cache_update(data)

**功能**: 更新缓存系统中的数据

**参数**:

- `data`: 要更新的数据字符串，通常为JSON格式

**返回值**: 无

**示例**:

```python
# 更新缓存中的部分数据
import json
import time

# 准备更新的部分数据
update = {
    "statistics": {
        "total_profit": 1280.5,
        "trade_count": 49,
        "win_rate": 0.755
    },
    "last_update": int(time.time())
}

# 将更新内容转换为JSON字符串并更新缓存
trader.cache_update(json.dumps(update))
```

#### 📂 cache_load()

**功能**: 从缓存系统加载数据

**参数**: 无

**返回值**:

- 缓存数据字符串，如果没有则返回None

**示例**:

```python
# 从缓存加载策略状态
import json

# 从缓存加载数据
cached_data = trader.cache_load()
if cached_data:
    state = json.loads(cached_data)
    print(f"加载的策略状态: {state}")

    # 使用加载的状态恢复策略
    positions = state.get("positions", [])
    orders = state.get("orders", [])
    statistics = state.get("statistics", {})

    print(f"已恢复 {len(positions)} 个持仓, {len(orders)} 个订单")
    print(f"总盈利: {statistics.get('total_profit', 0)}, 胜率: {statistics.get('win_rate', 0)*100}%")
else:
    print("没有找到缓存数据，使用默认配置")
    # 初始化默认状态
    positions = []
    orders = []
    statistics = {"total_profit": 0, "trade_count": 0, "win_rate": 0}
```

### 3.2 缓存系统说明

Trader提供的缓存系统分为两部分，它们是互相独立的机制：

1. **策略缓存** 📊: 通过`cache_save`、`cache_update`和`cache_load`方法管理的缓存，主要用于保存策略状态，如交易记录、统计数据等。

2. **WebClient缓存** 🌐: WebClient有独立的缓存机制，**不使用**上述的`cache_save`、`cache_update`和`cache_load`方法。WebClient的配置数据和状态会自动缓存，不仅包括`primary_balance`和`secondary_balance`参数，还包括所有的统计数据，如总成交量、成交次数、失败次数、成功次数、胜率、利润等。这些数据在首次设置后会被自动缓存，后续启动时自动读取。另外，**当通过相关API更新数据时（如update_trade_stats、update_total_balance等），缓存文件也会自动更新**，确保数据持久性。

**WebClient缓存文件位置**:

- WebClient缓存文件以`{server_name}.json`命名
- 默认存储在当前工作目录中
- 不同策略应使用不同的`server_name`以避免缓存冲突
- 如果需要重置初始余额或统计数据，可以删除对应的缓存文件，或使用新的`server_name`

> **缓存机制说明**: WebClient的配置数据（包括初始余额、交易统计数据如总成交量、成交次数、失败次数、成功次数、胜率、利润等）会被缓存到本地文件系统。缓存文件以 `{server_name}.json` 的格式命名，存储在当前工作目录中。当调用统计和余额相关的API（如update_trade_stats、update_total_balance等）更新数据时，缓存文件会实时自动更新，无需手动操作。这就是为什么`server_name`参数非常重要，它不仅用于Web界面标识，还用于定位缓存文件。不同的策略应使用不同的`server_name`以避免缓存冲突。WebClient缓存是自动管理的，与`cache_save`等方法无关。

---

## 四、外部通信

### 4.1 HTTP请求

#### 🌐 request(url, method, body, headers=None)

**功能**: 发送HTTP请求获取外部数据

**参数**:

- `url`: 请求URL
- `method`: 请求方法，如"GET", "POST"等
- `body`: 请求体内容，可选
- `headers`: 请求头字典，可选

**返回值**:

- 响应数据，已转换为Python对象

**示例**:

```python
# 发送GET请求获取行情数据
url = "https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT"
response = trader.request(url, "GET", None)
print(f"BTC当前价格: {response.get('price')}")

# 发送POST请求与自定义头信息
import json

payload = json.dumps({"param1": "value1", "param2": "value2"})
headers = {
    "Content-Type": "application/json",
    "X-API-Key": "your_api_key_here"
}
response = trader.request(
    "https://your-api-endpoint.com/data",
    "POST",
    payload,
    headers
)
print(f"API响应: {response}")

# 获取资金费率信息
url = "https://api.exchange.com/funding_rate?symbol=BTC-USDT"
funding_rate = trader.request(url, "GET", None)
print(f"当前资金费率: {funding_rate.get('value')}%")
print(f"下次结算时间: {funding_rate.get('next_time')}")
```

---

## 五、NB8 Web平台集成

### 5.1 Web客户端管理

#### 🚀 init_web_client(config)

**功能**: 创建并初始化WebClient，用于与Web平台通信

**参数**:

- `config`: WebClient配置对象，包含以下可选字段：
  - `server_name`: 字符串，策略服务器名称，用于在Web平台上标识该策略
  - `primary_balance`: 浮点数，主账户初始余额，用于计算策略收益率。**仅在第一次实盘初始化时需要传递，后续启动会从缓存中读取**
  - `secondary_balance`: 浮点数，次账户初始余额，用于双账户策略。**仅在第一次实盘初始化时需要传递，后续启动会从缓存中读取**
  - `is_production`: 布尔值，是否为生产环境，影响风控级别
  - `open_threshold`: 浮点数，开仓阈值，例如0.14表示千1.4价差开仓
  - `max_position_ratio`: 浮点数，单个币种最大持仓比例，例如100表示100%，1x杠杆
  - `max_leverage`: 整数，最大可用杠杆倍数
  - `funding_rate_threshold`: 浮点数，资金费率阈值，例如0.1表示千1资金费率开仓
  - `cost`: 浮点数，成本，主所开平 副所开平，4次交易手续费，如0.0824 代表万8.24
  - `primary_maker_fee_rate`: 浮点数，主所maker手续费率
  - `primary_taker_fee_rate`: 浮点数，主所taker手续费率
  - `primary_rebate_rate`: 浮点数，主所返佣率
  - `secondary_maker_fee_rate`: 浮点数，次所maker手续费率
  - `secondary_taker_fee_rate`: 浮点数，次所taker手续费率
  - `secondary_rebate_rate`: 浮点数，次所返佣率

**返回值**: 无

**示例**:

```python
# 初始化Web客户端
web_config = {
    "server_name": "BTC-ETH套利策略",
    "primary_balance": 10000.0,       # 主账户初始余额
    "secondary_balance": 5000.0,      # 次账户初始余额
    "is_production": True,            # 生产环境
    "open_threshold": 0.14,           # 开仓阈值 (千1.4价差)
    "max_position_ratio": 100,        # 最大持仓比例 (100%)
    "max_leverage": 3,                # 最大杠杆倍数
    "funding_rate_threshold": 0.1,    # 资金费率阈值 (千1)
    "cost": 0.0824,                   # 交易成本 (万8.24)
    "primary_maker_fee_rate": 0.0002, # 主所maker费率
    "primary_taker_fee_rate": 0.0005, # 主所taker费率
    "primary_rebate_rate": 0.3,       # 主所返佣率
    "secondary_maker_fee_rate": 0.0002, # 次所maker费率
    "secondary_taker_fee_rate": 0.0005, # 次所taker费率
    "secondary_rebate_rate": 0.7,     # 次所返佣率
}

trader.init_web_client(web_config)
```

#### ▶️ start_web_client(upload_interval=None)

**功能**: 启动WebClient，开始定期向Web平台上传数据

**参数**:

- `upload_interval`: 上传间隔（秒，s），默认为5秒。此参数控制向Web平台发送数据的频率。

**返回值**: 无

**示例**:

```python
# 启动Web客户端，设置10秒上传间隔
trader.start_web_client(upload_interval=10)
```

#### ⏹️ stop_web_client()

**功能**: 停止WebClient，确保最后一批数据上传

**参数**: 无

**返回值**: 无

**示例**:

```python
# 策略结束时停止Web客户端
trader.stop_web_client()
```

### 5.2 策略状态检查

这些方法用于检查NB8 Web平台下发的策略控制指令，可用于策略程序中实现对平台实时指令的响应。

#### 🚫 is_web_soft_stopped()

**功能**: 检查平台是否下发了缓停指令

**参数**: 无

**返回值**:

- 布尔值，为true时表示平台要求策略缓慢停止交易

**说明**:

- 此状态表示平台要求策略在合适的时机停止交易
- 策略可以完成当前交易流程，但不应开始新的交易周期

#### 🔒 is_web_opening_stopped()

**功能**: 检查平台是否下发了停止开仓指令

**参数**: 无

**返回值**:

- 布尔值，为true时表示平台要求策略停止开新仓

**说明**:

- 此状态表示平台要求策略不再开设新的仓位
- 策略可以继续管理现有仓位，包括平仓操作

#### 🔓 is_web_force_closing()

**功能**: 检查平台是否下发了强平指令

**参数**: 无

**返回值**:

- 布尔值，为true时表示平台要求策略强制平仓

**说明**:

- 此状态表示平台要求策略立即平掉所有持仓
- 策略应在检测到此状态为true时执行平仓逻辑

**示例**:

```python
# 检查平台控制指令
def check_platform_control():
    # 检查是否需要缓停交易
    if trader.is_web_soft_stopped():
        print("平台已发出缓停指令，将不再开始新的交易周期")
        return "soft_stop"

    # 检查是否禁止开仓
    if trader.is_web_opening_stopped():
        print("平台已禁止开仓，仅允许管理现有仓位")
        return "no_open"

    # 检查是否需要强制平仓
    if trader.is_web_force_closing():
        print("平台已发出强平指令，执行紧急平仓流程")
        close_all_positions()
        return "force_close"

    return "normal"

# 交易主循环
def trading_loop():
    import time
    while True:
        # 检查平台状态
        status = check_platform_control()

        if status == "force_close":
            # 等待平仓完成后退出
            break
        elif status == "soft_stop":
            # 完成当前周期后退出
            time.sleep(60)
            break
        elif status == "no_open":
            # 只管理现有仓位，不开新仓
            manage_positions()
        else:
            # 正常交易
            scan_opportunities()

        time.sleep(5)
```

### 5.3 账户与余额数据

#### 💰 update_total_balance(primary_balance, secondary_balance=None, available_primary=None, available_secondary=None)

**功能**: 更新账户余额信息到Web平台

**参数**:

- `primary_balance`: 主账户总余额
- `secondary_balance`: 次账户总余额，可选
- `available_primary`: 主账户可用余额，可选
- `available_secondary`: 次账户可用余额，可选

**返回值**: 无

**示例**:

```python
# 更新账户余额信息
def update_balance_info():
    # 查询主账户余额
    primary_cmd = {
        "account_id": 0,
        "cmd": {"Sync": "UsdtBalance"}
    }
    primary_result = trader.publish(primary_cmd)
    primary_balance = primary_result.get("balance", 0)
    available_primary = primary_result.get("available", 0)

    # 查询次账户余额
    secondary_cmd = {
        "account_id": 1,
        "cmd": {"Sync": "UsdtBalance"}
    }
    secondary_result = trader.publish(secondary_cmd)
    secondary_balance = secondary_result.get("balance", 0)
    available_secondary = secondary_result.get("available", 0)

    # 更新到Web平台
    trader.update_total_balance(
        primary_balance,
        secondary_balance,
        available_primary,
        available_secondary
    )

    print(f"已更新余额信息 - 主账户: {primary_balance} (可用: {available_primary}), 次账户: {secondary_balance} (可用: {available_secondary})")
```

#### 💱 add_funding_fee(primary_fee=None, secondary_fee=None)

**功能**: 添加已结算的资金费用记录到Web平台

**参数**:

- `primary_fee`: 主账户已结算资金费，可选
- `secondary_fee`: 次账户已结算资金费，可选

**返回值**: 无

#### 💱 update_pred_funding(primary_fee=None, secondary_fee=None)

**功能**: 更新未结算的预测资金费用记录到Web平台

**参数**:

- `primary_fee`: 主账户未结算预测资金费，可选
- `secondary_fee`: 次账户未结算预测资金费，可选

**返回值**: 无

**示例**:

```python
# 记录资金费
def record_funding_fee():
    # 添加已结算的资金费
    trader.add_funding_fee(primary_fee=-1.25, secondary_fee=-0.85)

    # 更新未结算的预测资金费
    trader.update_pred_funding(primary_fee=-0.33, secondary_fee=-0.17)

    print("已记录资金费信息")

# 资金费率定时结算处理函数示例
def handle_funding_settlement():
    import time

    # 获取已结算的资金费数据（示例）
    # 实际使用时应从交易所API获取真实数据
    primary_settled_fee = -2.35
    secondary_settled_fee = -1.15

    # 记录已结算资金费
    trader.add_funding_fee(
        primary_fee=primary_settled_fee,
        secondary_fee=secondary_settled_fee
    )

    # 获取未结算的预测资金费（示例）
    primary_predicted_fee = -0.45
    secondary_predicted_fee = -0.21

    # 更新未结算预测资金费
    trader.update_pred_funding(
        primary_fee=primary_predicted_fee,
        secondary_fee=secondary_predicted_fee
    )

    # 记录结算时间
    settlement_time = int(time.time())
    print(f"在 {settlement_time} 处理了资金费结算")
```

### 5.4 持仓与盈亏数据

#### 📈 update_total_position_value(total_value, long_position_value, short_position_value)

**功能**: 更新所有节点持仓价值信息到Web平台

**参数**:

- `total_value`: 所有节点总持仓价值
- `long_position_value`: 所有节点多头持仓价值
- `short_position_value`: 所有节点空头持仓价值

**返回值**: 无

#### 📈 update_current_position_value(total_value, long_position_value, short_position_value)

**功能**: 更新当前节点持仓价值信息到Web平台

**参数**:

- `total_value`: 当前节点总持仓价值
- `long_position_value`: 当前节点多头持仓价值
- `short_position_value`: 当前节点空头持仓价值

**返回值**: 无

#### 📊 update_floating_profit(floating_profit)

**功能**: 更新浮动盈亏信息到Web平台

**参数**:

- `floating_profit`: 浮动盈亏金额

**返回值**: 无

#### 💹 log_profit(profit)

**功能**: 上传利润，前端实盘页用来展示利润曲线

**参数**:

- `profit`: 利润金额

**返回值**: 无

**示例**:

```python
# 更新持仓信息
def update_position_info():
    # 计算当前节点持仓价值
    current_total = 15000.0
    current_long = 10000.0
    current_short = 5000.0
    trader.update_current_position_value(current_total, current_long, current_short)

    # 更新所有节点总持仓价值
    total_value = 25000.0
    total_long = 15000.0
    total_short = 10000.0
    trader.update_total_position_value(total_value, total_long, total_short)

    # 更新浮动盈亏
    floating_profit = 320.5
    trader.update_floating_profit(floating_profit)

    # 记录策略利润曲线数据点
    profit = 1580.75
    trader.log_profit(profit)

    print("已更新持仓与盈亏信息")

# 从实际持仓数据计算持仓价值
def calculate_position_values(positions):
    total_value = 0
    long_value = 0
    short_value = 0

    # 获取当前市场价格（示例）
    # 实际使用时应从交易所API获取最新价格
    market_prices = {
        "BTC_USDT": 51200.5,
        "ETH_USDT": 3250.8,
        "SOL_USDT": 115.25
    }

    # 计算持仓价值
    for pos in positions:
        symbol = pos["symbol"]
        amount = pos["amount"]
        side = pos["side"]

        # 获取当前价格
        current_price = market_prices.get(symbol, 0)
        if current_price == 0:
            continue

        # 计算持仓价值
        position_value = amount * current_price
        total_value += position_value

        # 根据方向累加多空持仓价值
        if side == "long":
            long_value += position_value
        else:
            short_value += position_value

    # 更新到Web平台
    trader.update_current_position_value(total_value, long_value, short_value)

    # 返回计算结果
    return {
        "total": total_value,
        "long": long_value,
        "short": short_value
    }
```

### 5.5 交易统计数据

#### 📉 update_trade_stats(maker_volume, taker_volume, profit, is_single_close=False)

**功能**: 更新交易统计数据到Web平台

**参数**:

- `maker_volume`: 挂单量 不传默认0
- `taker_volume`: 吃单量 不传默认0
- `profit`: 总利润。**注意**: 未平仓时传入0，此时系统只会统计交易量，不会统计盈利次数
- `is_single_close`: 是否单腿平仓，默认False

> **💡 提示**: 当策略仅开仓但未平仓时，应调用此方法并将`profit`参数设为0。这样系统会记录交易量，但不会增加盈利/亏损次数统计。当交易完全结束（开仓并平仓）后，再传入实际的盈亏金额进行完整统计。

**返回值**:

- 总利润

**示例**:

```python
# 开仓时，只记录交易量，不计算盈亏
def record_opening_trade(maker_vol, taker_vol):
    total_profit = trader.update_trade_stats(maker_vol, taker_vol, 0)
    print(f"已记录开仓交易量，不计入盈亏，当前总利润: {total_profit}")
    return total_profit

# 平仓时，记录交易量并更新盈亏
def record_closing_trade(maker_vol, taker_vol, profit, is_single=False):
    total_profit = trader.update_trade_stats(maker_vol, taker_vol, profit, is_single)
    print(f"已记录平仓交易，利润: {profit}，当前总利润: {total_profit}")
    return total_profit

# 实际使用示例
def handle_trade_execution(trade_type, volumes, profit=None):
    maker_volume = volumes.get("maker", 0)
    taker_volume = volumes.get("taker", 0)

    if trade_type == "open":
        # 开仓只记录交易量
        record_opening_trade(maker_volume, taker_volume)
    elif trade_type == "close":
        # 平仓记录交易量和盈亏
        record_closing_trade(maker_volume, taker_volume, profit or 0)
    elif trade_type == "single_close":
        # 单腿平仓
        record_closing_trade(maker_volume, taker_volume, profit or 0, True)
```

#### 📊 get_stats()

**功能**: 获取当前完整的统计数据

**参数**: 无

**返回值**:

- 包含统计数据的Python字典，使用原始字段名（而非序列化别名）

**返回字段说明**:

- `time`: 起始时间
- `server_name`: 服务器名称
- **账户余额信息**
  - `initial_balance`: 主所起始余额
  - `current_balance`: 主所当前余额
  - `secondary_initial_balance`: 次所初始余额
  - `secondary_current_balance`: 次所实时余额
  - `available_balance`: 主所可用余额
  - `secondary_available_balance`: 次所可用余额

- **交易统计**
  - `volume`: 累计交易量
  - `maker_volume`: 累计maker交易量
  - `taker_volume`: 累计taker交易量
  - `count`: 总套利次数
  - `win_rate`: 胜率
  - `total_profit`: 总利润
  - `success_count`: 成功次数
  - `success_profit`: 成功利润
  - `failure_count`: 失败次数
  - `failure_loss`: 失败亏损
  - `unrealized_pnl`: 未实现盈亏
  - `single_close_count`: 单腿平仓次数
  - `single_close_profit`: 单腿平仓金额

- **持仓和杠杆信息**
  - `total_position_value`: 所有节点持仓杠杆
  - `total_long_position_value`: 所有节点多仓杠杆
  - `total_short_position_value`: 所有节点空仓杠杆
  - `current_position_value`: 当前节点持仓杠杆
  - `current_long_position_value`: 当前节点多仓杠杆
  - `current_short_position_value`: 当前节点空仓杠杆

- **资金费率与回报率**
  - `funding_fee`: 当日已结算的资金费总额
  - `primary_funding_fee`: 当日已结算的主所资金费
  - `secondary_funding_fee`: 当日已结算的次所资金费

- **交易参数**
  - `cost`: 成本
  - `open_threshold`: 开仓阈值，如0.14代表千1.4价差开仓
  - `funding_rate_threshold`: 资金费率阈值，如0.1代表千1资金费率开仓
  - `max_position_ratio`: 单个币种最大持仓比例，如100代表100%，1x杠杆
  - `fund_transfer`: 资金划转
  - `max_leverage`: 最大可开杠杆
  - `primary_maker_fee_rate`: 主所maker手续费率
  - `primary_taker_fee_rate`: 主所taker手续费率
  - `primary_rebate_rate`: 主所返佣率
  - `secondary_maker_fee_rate`: 次所maker手续费率
  - `secondary_taker_fee_rate`: 次所taker手续费率
  - `secondary_rebate_rate`: 次所返佣率

- **今日数据**
  - `today`: 今日统计数据对象，包含以下字段:
    - `time`: 当日起始时间戳
    - `initial_balance`: 当日初始主所余额
    - `current_balance`: 当日当前主所余额
    - `available_balance`: 当日主所可用余额
    - `secondary_initial_balance`: 当日初始次所余额
    - `secondary_current_balance`: 当日当前次所余额
    - `secondary_available_balance`: 当日次所可用余额
    - `volume`: 当日交易量
    - `maker_volume`: 当日maker交易量
    - `taker_volume`: 当日taker交易量
    - `count`: 当日套利次数
    - `win_rate`: 当日胜率
    - `total_profit`: 当日总利润
    - `success_count`: 当日成功次数
    - `success_profit`: 当日成功利润
    - `failure_count`: 当日失败次数
    - `failure_loss`: 当日失败亏损
    - `funding_fee`: 未结算的预测资金费总额
    - `primary_funding_fee`: 未结算的预测主所资金费
    - `secondary_funding_fee`: 未结算的预测次所资金费

**示例**:

```python
# 获取并分析统计数据
def analyze_trading_stats():
    # 获取完整统计数据
    stats = trader.get_stats()

    # 提取关键指标
    server_name = stats.get("server_name", "未命名策略")
    win_rate = stats.get("win_rate", 0) * 100  # 转换为百分比
    total_trades = stats.get("count", 0)
    success_trades = stats.get("success_count", 0)
    failure_trades = stats.get("failure_count", 0)
    total_profit = stats.get("total_profit", 0)
    total_volume = stats.get("volume", 0)

    # 分析统计数据
    print(f"策略 '{server_name}' 统计分析:")
    print(f"总交易次数: {total_trades} (成功: {success_trades}, 失败: {failure_trades})")
    print(f"胜率: {win_rate:.2f}%")
    print(f"总盈利: {total_profit:.2f} USDT")
    print(f"总交易量: {total_volume:.2f} USDT")

    # 计算平均每笔交易盈亏
    if total_trades > 0:
        avg_profit_per_trade = total_profit / total_trades
        print(f"平均每笔交易盈亏: {avg_profit_per_trade:.2f} USDT")

    # 获取当日数据
    today_stats = stats.get("today", {})
    today_profit = today_stats.get("total_profit", 0)
    today_trades = today_stats.get("count", 0)
    today_win_rate = today_stats.get("win_rate", 0) * 100

    print(f"\n今日统计:")
    print(f"今日交易次数: {today_trades}")
    print(f"今日胜率: {today_win_rate:.2f}%")
    print(f"今日盈利: {today_profit:.2f} USDT")

    # 返回关键指标
    return {
        "win_rate": win_rate,
        "total_profit": total_profit,
        "total_trades": total_trades,
        "today_profit": today_profit
    }
```

### 5.6 数据可视化

#### 📊 upload_tables(tables)

**功能**: 上传多个表格数据到Web平台显示

**参数**:

- `tables`: 表格数据列表，每个表格包含标题、列和行数据

**返回值**: 无

**示例**:

```python
# 上传表格数据到Web平台显示
def upload_market_data():
    # 价差表
    spread_table = {
        "title": "交易所价差表",
        "cols": ["交易对", "交易所A价格", "交易所B价格", "价差(‰)", "资金费率A", "资金费率B"],
        "rows": [
            ["BTC_USDT", "50120.5", "50080.2", "0.81", "0.01%", "-0.025%"],
            ["ETH_USDT", "3245.8", "3240.5", "1.63", "0.008%", "-0.01%"],
            ["SOL_USDT", "110.25", "109.85", "3.64", "0.015%", "-0.02%"],
            ["BNB_USDT", "415.8", "415.2", "1.44", "0.005%", "-0.015%"]
        ]
    }

    # 持仓表
    position_table = {
        "title": "当前持仓",
        "cols": ["交易对", "方向", "数量", "开仓价", "当前价", "浮动盈亏", "持仓时间"],
        "rows": [
            ["BTC_USDT", "多/空", "0.15", "49850", "50100", "+$37.5", "2小时15分"],
            ["ETH_USDT", "多/空", "1.2", "3210", "3245", "+$42", "1小时30分"]
        ]
    }

    # 上传表格数据到Web平台
    trader.upload_tables([spread_table, position_table])
    print("已更新Web平台数据表格")

# 创建交易机会表格
def create_opportunity_table(opportunities):
    table = {
        "title": "套利机会",
        "cols": ["交易对", "交易所A", "交易所B", "价差(‰)", "预期收益", "资金费差"],
        "rows": []
    }

    # 添加行数据
    for opp in opportunities:
        row = [
            opp["symbol"],
            opp["exchange_a"],
            opp["exchange_b"],
            f"{opp['spread']:.2f}",
            f"{opp['expected_profit']:.2f} USDT",
            f"{opp['funding_diff']:.4f}%"
        ]
        table["rows"].append(row)

    return table

# 示例：定期更新多个表格
def update_dashboard_tables():
    import time

    # 获取套利机会（示例数据）
    opportunities = [
        {"symbol": "BTC_USDT", "exchange_a": "Binance", "exchange_b": "OKX",
         "spread": 1.25, "expected_profit": 18.5, "funding_diff": 0.035},
        {"symbol": "ETH_USDT", "exchange_a": "Binance", "exchange_b": "OKX",
         "spread": 2.10, "expected_profit": 12.8, "funding_diff": 0.025}
    ]

    # 获取持仓情况（示例数据）
    positions = [
        {"symbol": "BTC_USDT", "side": "多/空", "amount": 0.15,
         "entry_price": 49850, "current_price": 50100, "pnl": 37.5, "duration": "2小时15分"},
        {"symbol": "ETH_USDT", "side": "多/空", "amount": 1.2,
         "entry_price": 3210, "current_price": 3245, "pnl": 42, "duration": "1小时30分"}
    ]

    # 创建机会表格
    opportunity_table = create_opportunity_table(opportunities)

    # 创建持仓表格
    position_table = {
        "title": "当前持仓",
        "cols": ["交易对", "方向", "数量", "开仓价", "当前价", "浮动盈亏", "持仓时间"],
        "rows": []
    }

    for pos in positions:
        row = [
            pos["symbol"],
            pos["side"],
            str(pos["amount"]),
            str(pos["entry_price"]),
            str(pos["current_price"]),
            f"+${pos['pnl']}" if pos['pnl'] >= 0 else f"-${abs(pos['pnl'])}",
            pos["duration"]
        ]
        position_table["rows"].append(row)

    # 上传到Web平台
    trader.upload_tables([opportunity_table, position_table])
    print(f"已在 {int(time.time())} 更新仪表盘数据")
```

### 5.7 账户强停功能

#### 🛑 set_force_stop(force_stop)

**功能**: 直接设置实盘强停状态

**参数**:
- `force_stop`: 布尔值，`True`表示启用强停状态，`False`表示解除强停状态

**返回值**: 无

**使用场景**:
- 策略亏损过多需要暂停开仓一段时间进行风控
- 发现异常行情，需要紧急暂停交易
- 系统检测到风险预警，需要暂停策略执行

**使用示例**:

```python
# 启用强停状态
def enable_force_stop():
    """设置实盘强停状态"""
    self.trader.set_force_stop(True)
    self.trader.log("已启用账户强停状态", level="WARN", color="red")

# 解除强停状态
def disable_force_stop():
    """解除实盘强停状态"""
    self.trader.set_force_stop(False)
    self.trader.log("已解除账户强停状态", level="INFO", color="green")

# 根据条件自动管理强停状态
def manage_force_stop():
    """根据策略状态自动管理强停状态"""
    # 获取统计数据
    stats = trader.get_stats()

    # 检查最大回撤是否超过阈值
    max_drawdown = stats.get('max_drawdown', 0.0)
    if max_drawdown < -15.0:  # 回撤超过15%
        self.trader.set_force_stop(True)
        self.trader.log(f"检测到回撤{max_drawdown:.2f}%超过阈值，已启用强停状态", level="WARN", color="red")
    else:
        # 如果回撤恢复正常，可以解除强停
        self.trader.set_force_stop(False)
        self.trader.log("回撤状态正常，已解除强停状态", level="INFO")
```

**注意事项**:
1. 此方法直接设置实盘强停状态，比通过表格内容中添加"账户强停"字段更加直接高效
2. 强停状态只影响Web平台上的显示，不会自动停止策略的实际执行，需要在策略代码中主动检测并处理强停状态
3. 建议在策略的主循环中定期检查并管理强停状态

**效果图**:

![alt text](images/image.png)

---

## 六、数据结构说明

### 表格数据格式

<details>
<summary>表格数据格式示例 📋</summary>

```json
{
    "title": "表格标题",
    "cols": ["列1", "列2", "列3", ...],
    "rows": [
        ["行1值1", "行1值2", "行1值3", ...],
        ["行2值1", "行2值2", "行2值3", ...],
        ...
    ]
}
```

</details>

### WebClient配置格式

<details>
<summary>WebClient配置示例 ⚙️</summary>

```json
{
    "server_name": "策略名称",          // 策略服务器名称，用于在Web平台上标识
    "primary_balance": 10000.0,       // 主账户初始余额，仅首次初始化需要
    "secondary_balance": 5000.0,      // 次账户初始余额，仅首次初始化需要
    "is_production": true,            // 是否为生产环境，影响风控级别
    "open_threshold": 0.14,           // 开仓阈值，如0.14代表千1.4价差开仓
    "max_position_ratio": 100,        // 单个币种最大持仓比例，如100代表100%，1x杠杆
    "max_leverage": 10,               // 最大可用杠杆倍数
    "funding_rate_threshold": 0.1,     // 资金费率阈值，如0.1代表千1资金费率开仓
    "cost": 0.0824                    // 成本 主所开平 副所开平，4次交易手续费，如0.0824 代表万8.24
    "primary_maker_fee_rate": 0.0002, // 主所maker手续费率
    "primary_taker_fee_rate": 0.0005, // 主所taker手续费率
    "primary_rebate_rate": 0.3, // 主所返佣率
    "secondary_maker_fee_rate": 0.0002, // 次所maker手续费率
    "secondary_taker_fee_rate": 0.0005, // 次所taker手续费率
    "secondary_rebate_rate": 0.7, // 次所返佣率
}
```

</details>

所有字段均为可选项，但推荐在首次初始化时设置`server_name`、`primary_balance`和`secondary_balance`。初始余额设置后会被缓存，后续启动策略时会自动从缓存中读取，无需再次设置。

> **缓存机制说明**: WebClient的配置数据（包括初始余额、交易统计数据如总成交量、成交次数、失败次数、成功次数、胜率、利润等）会被缓存到本地文件系统。缓存文件以 `{server_name}.json` 的格式命名，存储在当前工作目录中, 文件名为`server_name`.json。当调用统计和余额相关的API（如update_trade_stats、update_total_balance等）更新数据时，缓存文件会实时自动更新，无需手动操作。这就是为什么`server_name`参数非常重要，它不仅用于Web界面标识，还用于定位缓存文件。不同的策略应使用不同的`server_name`以避免缓存冲突。WebClient缓存是自动管理的，与`cache_save`等方法无关。

---

## 七、完整策略示例

以下是一个跨交易所套利策略示例，展示了如何使用Trader API的核心功能：

```python
"""
arbitrage_strategy.py - 跨交易所套利策略示例
"""

import json
import time
import base_strategy

class ArbitrageStrategy(base_strategy.BaseStrategy):
    def __init__(self, cex_configs, dex_configs, config, trader):
        """初始化策略"""
        self.cex_configs = cex_configs
        self.has_account = len(cex_configs) > 0
        self.dex_configs = dex_configs
        self.trader = trader
        self.config = config or {
            "send_to_web": True,
            "min_spread": 1.0,  # 最小开仓价差(‰)
            "symbols": ["BTC_USDT", "ETH_USDT", "SOL_USDT"]  # 监控的交易对
        }

        # 初始化数据
        self.positions = {}
        self.running = False
        self.primary_balance = 10000.0  # 默认值
        self.secondary_balance = 5000.0  # 默认值

        # 加载缓存
        self.load_state()

    def _init_fee_rates(self):
        """查询费率信息"""
        # 默认费率
        self.primary_maker_fee = 0.0002
        self.primary_taker_fee = 0.0005
        self.secondary_maker_fee = 0.0002
        self.secondary_taker_fee = 0.0005
        self.primary_rebate_rate = 0
        self.secondary_rebate_rate = 0

        if not self.has_account or not self.config.get("symbols"):
            return

        symbol = self.config["symbols"][0]

        # 查询主账户费率
        try:
            fee_rate = self.trader.publish({"account_id": 0, "cmd": {"Sync": {"FeeRate": symbol}}}).get('Ok')
            if fee_rate:
                self.trader.log(f"主账户费率: {fee_rate}", level="DEBUG", web=self.config.get("send_to_web", True))
                self.primary_maker_fee = fee_rate.get('maker_fee_rate', 0.0002)
                self.primary_taker_fee = fee_rate.get('taker_fee_rate', 0.0005)
                self.primary_rebate_rate = self.cex_configs[0].get('rebate_rate', 0)
        except Exception as e:
            self.trader.log(f"获取主账户费率失败: {e}", level="WARN", web=self.config.get("send_to_web", True))

        # 查询次账户费率
        if len(self.cex_configs) > 1:
            try:
                fee_rate = self.trader.publish({"account_id": 1, "cmd": {"Sync": {"FeeRate": symbol}}}).get('Ok')
                if fee_rate:
                    self.trader.log(f"次账户费率: {fee_rate}", level="DEBUG", web=self.config.get("send_to_web", True))
                    self.secondary_maker_fee = fee_rate.get('maker_fee_rate', 0.0002)
                    self.secondary_taker_fee = fee_rate.get('taker_fee_rate', 0.0005)
                    self.secondary_rebate_rate = self.cex_configs[1].get('rebate_rate', 0)
            except Exception as e:
                self.trader.log(f"获取次账户费率失败: {e}", level="WARN", web=self.config.get("send_to_web", True))

        # 计算成本，两边都用taker
        self.cost = (self.primary_taker_fee + self.secondary_taker_fee) * 2

        # 查询账户余额
        try:
            balance = self.trader.publish({"account_id": 0, "cmd": {"Sync": "UsdtBalance"}}).get('Ok')
            if balance:
                self.primary_balance = balance.get('balance', 10000.0)
        except Exception:
            self.trader.log("获取主账户余额失败", level="WARN", web=self.config.get("send_to_web", True))

        # 查询次账户余额
        if len(self.cex_configs) > 1:
            try:
                balance = self.trader.publish({"account_id": 1, "cmd": {"Sync": "UsdtBalance"}}).get('Ok')
                if balance:
                    self.secondary_balance = balance.get('balance', 5000.0)
            except Exception:
                self.trader.log("获取次账户余额失败", level="WARN", web=self.config.get("send_to_web", True))

    def _init_web_client(self):
        """初始化Web客户端"""
        web_config = {
            "server_name": "跨所套利策略",
            "primary_balance": self.primary_balance,
            "secondary_balance": self.secondary_balance,
            "is_production": True,
            "open_threshold": self.config.get("min_spread", 1.0) / 10,  # 转换为小数
            "max_position_ratio": 80,
            "max_leverage": 2,
            "funding_rate_threshold": 0.1,  # 资金费率阈值
            "cost": self.cost,  # 交易成本
            "primary_maker_fee_rate": self.primary_maker_fee,
            "primary_taker_fee_rate": self.primary_taker_fee,
            "primary_rebate_rate": self.primary_rebate_rate,
            "secondary_maker_fee_rate": self.secondary_maker_fee,
            "secondary_taker_fee_rate": self.secondary_taker_fee,
            "secondary_rebate_rate": self.secondary_rebate_rate
        }
        self.trader.init_web_client(web_config)

    def name(self):
        """返回策略名称"""
        return "跨所套利策略"

    def load_state(self):
        """从缓存加载策略状态"""
        cached_data = self.trader.cache_load()
        if cached_data:
            state = json.loads(cached_data)
            self.positions = state.get("positions", {})
            self.trader.log("已加载缓存状态", web=self.config.get("send_to_web", True))

    def save_state(self):
        """保存策略状态到缓存"""
        state = {"positions": self.positions, "last_update": int(time.time())}
        self.trader.cache_save(json.dumps(state))

    def start(self):
        """启动策略"""

        # 初始化费率信息
        self._init_fee_rates()

        # 初始化Web客户端
        if self.config.get("send_to_web", True):
            self._init_web_client()
            self.trader.start_web_client(upload_interval=5)

        self.trader.log("启动跨所套利策略", web=self.config.get("send_to_web", True))

        # 上传表格示例
        self.upload_tables()

        # tlog示例 频繁日志使用tlog限频
        self.trader.tlog("价差", "BTC_USDT: 1.25‰, ETH_USDT: 2.10‰, SOL_USDT: 3.45‰", color="green")

        # 初始化检查并启动交易
        if self.has_account:
            self.scan_opportunities()  # 扫描套利机会
        else:
            self.trader.log("没有配置账户，无法交易", level="WARN")

    def upload_tables(self):
        """上传表格示例方法"""

        self.spread_table = {
            "title": "价差",
            "cols": ["交易对", "价差(‰)"],
            "rows": [
                ["BTC_USDT", "1.25"],
                ["ETH_USDT", "2.10"],
                ["SOL_USDT", "3.45"]
            ]
        }

        self.position_table = {
            "title": "持仓",
            "cols": ["交易对", "持仓量", "持仓价值", "浮动盈亏", "持仓时间"],
            "rows": [
                ["BTC_USDT", "1.25", "10000", "100", "2小时15分"],
                ["ETH_USDT", "2.10", "20000", "200", "1小时30分"],
                ["SOL_USDT", "3.45", "30000", "300", "3小时45分"]
            ]
        }

        # 上传表格
        self.trader.upload_tables([self.spread_table, self.position_table])

    def on_stop(self):
        """停止策略"""
        self.save_state()
        if self.config.get("send_to_web", True):
            self.trader.stop_web_client()
        self.trader.log("策略已停止", web=self.config.get("send_to_web", True))

    def subscribes(self):
        """返回订阅列表"""
        subscriptions = []
        if self.has_account:
            symbols = self.config.get("symbols", ["BTC_USDT", "ETH_USDT"])
            # 订阅主账户订单和持仓
            subscriptions.append({
                "account_id": 0,
                "sub": {"SubscribeWs": [{"Order": symbols}, {"Position": symbols}]}
            })
            # 订阅次账户信息(如果有)
            if len(self.cex_configs) > 1:
                subscriptions.append({
                    "account_id": 1,
                    "sub": {"SubscribeWs": [{"Order": symbols}, {"Position": symbols}]}
                })
        return subscriptions

    # 以下是策略的核心方法(示例中省略详细实现)
    def on_bbo(self):
        """扫描套利机会"""
        # 实际根据价格、计算价差，并在满足条件时开仓
        pass

    def on_order(self, account_id, context, order):
        """订单回调处理"""
        # 处理订单状态更新
        pass

    def on_position(self, account_id, positions):
        """持仓回调处理"""
        # 更新持仓信息
        pass
```

---

*文档版本: 1.1.0*

*最后更新: 2025年4月18日*
