"""
binance.py

提供Binance现货和合约直接API下单功能，用于网络测试
"""

import traceback
import time
import hmac
import hashlib
import requests
from urllib.parse import urlencode

class BinanceClient:
    def __init__(self, api_key, api_secret, is_colo=False):
        """初始化Binance客户端"""
        self.api_key = api_key
        self.api_secret = api_secret

        # 现货API基础URL
        self.spot_base_url = 'https://api.binance.com' if not is_colo else 'https://api-mm.binance.us'

        # 合约API基础URL
        self.futures_base_url = 'https://fapi.binance.com' if not is_colo else 'https://fapi-mm.binance.com'

        # 创建一个Session对象用于复用
        self.session = requests.Session()
        self.session.headers.update({'X-MBX-APIKEY': self.api_key})

    def get_timestamp(self):
        """
        获取当前时间戳（毫秒）
        """
        return int(time.time() * 1000)

    def _generate_signature(self, params):
        """生成请求的签名，按照官方SDK方式实现"""
        # 对参数按键名排序
        query_string = urlencode(sorted(params.items()))

        # 特殊处理：将单引号替换为双引号
        query_string = query_string.replace('%27', '%22')

        # 使用HMAC SHA256算法生成签名
        signature = hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

        return signature

    def place_spot_order(self, symbol, side, order_type, quantity, price=None, time_in_force="GTC"):
        """
        下现货订单

        参数:
        - symbol: 交易对，例如 'BTCUSDT'
        - side: 买卖方向，'BUY' 或 'SELL'
        - order_type: 订单类型，例如 'LIMIT', 'MARKET'
        - quantity: 交易数量
        - price: 价格（对于LIMIT订单为必填）
        - time_in_force: 订单有效期，默认为 'GTC'

        返回:
        - 订单响应
        """
        # 确保交易对格式正确
        symbol = symbol.replace('/', '')

        # 准备订单参数
        params = {}
        params['symbol'] = symbol
        params['side'] = side
        params['type'] = order_type
        params['quantity'] = str(quantity)
        params['recvWindow'] = '5000'

        # 对于限价单，添加价格和有效期
        if order_type == 'LIMIT':
            params['price'] = str(price)
            params['timeInForce'] = time_in_force

        # 添加时间戳
        timestamp = self.get_timestamp()
        params['timestamp'] = str(timestamp)

        # 将参数转换为查询字符串并特殊处理
        query_string = urlencode(params)
        query_string = query_string.replace('%27', '%22')

        # 生成签名
        signature = hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

        # 构建完整的请求URL，包含查询字符串和签名
        base_url = f'{self.spot_base_url}/api/v3/order'
        url = f"{base_url}?{query_string}&signature={signature}"

        # 设置请求头，只包含API密钥
        headers = {
            'X-MBX-APIKEY': self.api_key
        }

        # 记录开始时间
        start_time = time.time() * 1000

        # 发送请求，注意不传递params参数
        try:
            response = self.session.post(url, headers=headers)

            # 计算延迟
            end_time = time.time() * 1000
            latency = end_time - start_time

            # 尝试解析响应
            try:
                response_data = response.json()
            except:
                response_data = response.text

            return {
                'response': response_data,
                'status_code': response.status_code,
                'latency': latency
            }
        except Exception as e:
            end_time = time.time() * 1000
            latency = end_time - start_time
            error_msg = f"现货下单请求异常: {str(e)}"
            print(error_msg)
            traceback.print_exc()

            return {
                'response': error_msg,
                'status_code': 500,
                'latency': latency,
                'error': str(e)
            }

    def place_futures_order(self, symbol, side, order_type, quantity, price=None, time_in_force="GTC"):
        """
        下合约订单

        参数:
        - symbol: 交易对，例如 'BTCUSDT'
        - side: 买卖方向，'BUY' 或 'SELL'
        - order_type: 订单类型，例如 'LIMIT', 'MARKET'
        - quantity: 交易数量
        - price: 价格（对于LIMIT订单为必填）
        - time_in_force: 订单有效期，默认为 'GTC'

        返回:
        - 订单响应
        """
        # 确保交易对格式正确
        symbol = symbol.replace('/', '')

        # 准备订单参数
        params = {}
        params['symbol'] = symbol
        params['side'] = side
        params['type'] = order_type
        params['quantity'] = str(quantity)
        params['recvWindow'] = '5000'

        # 对于限价单，添加价格和有效期
        if order_type == 'LIMIT':
            params['price'] = str(price)
            params['timeInForce'] = time_in_force

        # 添加时间戳
        timestamp = self.get_timestamp()
        params['timestamp'] = str(timestamp)

        # 将参数转换为查询字符串并特殊处理
        query_string = urlencode(params)
        query_string = query_string.replace('%27', '%22')

        # 生成签名
        signature = hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

        # 构建完整的请求URL，包含查询字符串和签名
        base_url = f'{self.futures_base_url}/fapi/v1/order'
        url = f"{base_url}?{query_string}&signature={signature}"

        # 设置请求头，只包含API密钥
        headers = {
            'X-MBX-APIKEY': self.api_key
        }

        # 记录开始时间
        start_time = time.time() * 1000

        # 发送请求，注意不传递params参数
        try:
            response = self.session.post(url, headers=headers)

            # 计算延迟
            end_time = time.time() * 1000
            latency = end_time - start_time

            # 尝试解析响应
            try:
                response_data = response.json()
            except:
                response_data = response.text

            return {
                'response': response_data,
                'status_code': response.status_code,
                'latency': latency
            }
        except Exception as e:
            end_time = time.time() * 1000
            latency = end_time - start_time
            error_msg = f"合约下单请求异常: {str(e)}"
            print(error_msg)
            traceback.print_exc()

            return {
                'response': error_msg,
                'status_code': 500,
                'latency': latency,
                'error': str(e)
            }

    def test_order(self, is_futures=False, symbol='BTCUSDT', price=40000, quantity=0.001):
        """
        执行测试订单，返回延迟信息

        参数:
        - is_futures: 是否为合约订单
        - symbol: 交易对
        - price: 下单价格
        - quantity: 下单数量

        返回:
        - 订单延迟和结果信息
        """
        try:
            # 先检查连接是否有效，注意合约和现货使用不同的时间端点
            # 根据是否是合约选择方法
            place_order = self.place_futures_order if is_futures else self.place_spot_order

            order_type = "合约" if is_futures else "现货"

            # 执行下单
            result = place_order(
                symbol=symbol,
                side='BUY',
                order_type='LIMIT',
                quantity=quantity,
                price=price
            )

            # 获取响应信息
            status_code = result['status_code']
            response_data = result['response']
            latency = result['latency']

            # 判断下单是否成功
            is_success = status_code == 200

            # 处理常见错误码
            if not is_success and isinstance(response_data, dict) and 'code' in response_data:
                error_code = response_data.get('code')
                error_msg = response_data.get('msg', '未知错误')

                if error_code == -2015:
                    print(f"API密钥错误，请检查API密钥、IP白名单和权限设置 - {error_msg}")
                elif error_code == -1021:
                    print(f"时间戳超出正常范围，请检查服务器时间同步 - {error_msg}")
                elif error_code == -1022:
                    print(f"API签名无效，请检查签名生成过程 - {error_msg}")
                elif error_code == -1100:
                    print(f"非法参数，请检查下单参数格式 - {error_msg}")
                elif error_code == -2019:
                    print(f"保证金不足，可能需要转入资金或减少下单金额 - {error_msg}")
                elif error_code == -4046:
                    print(f"合约下单数量精度问题，请调整数量 - {error_msg}")
                else:
                    print(f"下单失败，错误码: {error_code}, 消息: {error_msg}")

            elif not is_success:
                print(f"下单失败响应: {response_data}")

            return {
                'latency': latency,
                'is_success': is_success,
                'status_code': status_code,
                'response': response_data
            }
        except requests.exceptions.RequestException as e:
            error_msg = f"请求异常: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return {
                'latency': 999999,
                'is_success': False,
                'status_code': 0,
                'response': error_msg,
                'error': str(e)
            }
        except Exception as e:
            error_msg = f"测试订单错误: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return {
                'latency': 999999,
                'is_success': False,
                'status_code': 0,
                'response': error_msg,
                'error': str(e)
            }

    def test_python_method(self, is_futures=False, symbol='BTCUSDT', price=40000, quantity=0.001):
        """
        用Python方法测试API下单功能，测量延迟

        参数:
        - is_futures: 是否为合约订单
        - symbol: 交易对
        - price: 下单价格
        - quantity: 下单数量

        返回:
        - 订单延迟和结果信息
        """
        try:
            # 确保交易对格式正确
            symbol = symbol.replace('/', '')

            # 选择正确的基础URL和端点
            base_url = self.futures_base_url if is_futures else self.spot_base_url
            endpoint = '/fapi/v1/order' if is_futures else '/api/v3/order'

            # 准备参数字典
            params = {}
            params['symbol'] = symbol
            params['side'] = 'BUY'
            params['type'] = 'LIMIT'
            params['timeInForce'] = 'GTC'
            params['quantity'] = str(quantity)
            params['price'] = str(price)
            params['recvWindow'] = '5000'

            # 添加时间戳
            timestamp = self.get_timestamp()
            params['timestamp'] = str(timestamp)

            # 将参数转换为查询字符串并特殊处理
            query_string = urlencode(params)
            query_string = query_string.replace('%27', '%22')

            # 生成签名
            signature = hmac.new(
                self.api_secret.encode('utf-8'),
                query_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()

            # 构建完整的请求URL，包含查询字符串和签名
            api_url = f"{base_url}{endpoint}"
            url = f"{api_url}?{query_string}&signature={signature}"

            # 设置请求头，只包含API密钥
            headers = {
                'X-MBX-APIKEY': self.api_key
            }

            # 打印请求信息
            order_type = "合约" if is_futures else "现货"
            print(f"发送{order_type}下单请求...")

            # 记录开始时间
            start_time = time.time()

            # 发送请求，不传递params参数
            response = self.session.post(url, headers=headers)

            # 计算延迟（毫秒）
            latency = (time.time() - start_time) * 1000

            # 尝试解析JSON响应
            try:
                response_data = response.json()
            except:
                response_data = response.text

            is_success = response.status_code == 200

            return {
                'latency': latency,
                'is_success': is_success,
                'status_code': response.status_code,
                'response': response_data
            }
        except requests.exceptions.RequestException as e:
            error_msg = f"网络请求错误: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return {
                'latency': 999999,
                'is_success': False,
                'status_code': 0,
                'response': error_msg,
                'error': str(e)
            }
        except Exception as e:
            error_msg = f"Python方法测试错误: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return {
                'latency': 999999,
                'is_success': False,
                'status_code': 0,
                'response': error_msg,
                'error': str(e)
            }
