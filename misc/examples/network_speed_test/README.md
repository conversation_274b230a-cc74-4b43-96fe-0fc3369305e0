# 网络速度测试策略

这个策略用于测试交易系统中不同命令类型的网络延迟，帮助开发者了解系统性能和网络状况。

## 测试内容

策略会测试三种不同类型的命令延迟：

1. **同步命令延迟**：从 publish 命令发送前到接收响应后的时间（本地计算）
2. **异步命令延迟**：从 publish 异步命令发送前到 on_order_submitted 回调的时间（使用 cid 追踪）
3. **Return 命令延迟**：从 publish return 命令发送前到 on_order_submitted 回调的时间（使用 cid 追踪）

## 测试方法

策略使用定时器，按照配置的间隔依次测试各类命令，收集延迟数据并生成统计结果。每种命令类型都会测试配置的次数，完成后输出汇总信息。

- 同步命令测试使用 Balance 查询（不会实际发生交易）
- 异步和 Return 命令测试使用限价单下单（价格设置为不会实际成交的价格）
- 所有测试订单都会在测试后自动取消，不会产生实际交易

## 配置说明

在 `strategy.toml` 中可以配置以下参数：

```toml
test_symbol = "BTC_USDT"       # 测试使用的交易对
test_interval = 10             # 测试间隔（秒）
test_price = 40000.0           # 测试基准价格（实际会调整为不会成交的价格）
test_amount = 0.001            # 测试订单数量
test_times = 10                # 每种类型测试的次数
```

## 结果输出

测试结果会以两种方式输出：

1. 实时日志：在Web界面和日志中显示每次测试的延迟
2. 结果文件：在`results`目录中保存JSON格式的详细测试结果，包括每次测试的延迟和统计数据

## 使用说明

1. 确保已配置好交易账户
2. 确保该交易对可交易且有足够余额（异步和Return测试需要）
3. 调整配置参数适应你的测试需求
4. 启动策略，等待测试完成
5. 查看输出日志和结果文件分析网络延迟情况
