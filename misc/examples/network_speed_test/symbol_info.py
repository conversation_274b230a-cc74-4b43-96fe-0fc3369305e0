import time
import math


class SymbolInfo:
    """
    交易对信息管理类
    用于存储和管理单个交易对的相关信息
    """
    def __init__(self, symbol: str):
        self.symbol = symbol                # 交易对名称

        # 合约信息相关字段
        self.instrument = {
            'amount_multiplier': 1.0,       # 数量乘数
            'amount_precision': 3,          # 数量精度
            'amount_tick': 0.001,           # 数量最小变动单位
            'min_notional': 20.0,          # 最小名义价值
            'min_qty': 0.001,              # 最小交易数量
            'price_multiplier': 1.0,        # 价格乘数
            'price_precision': 2,           # 价格精度
            'price_tick': 0.01,            # 价格最小变动单位
            'state': 'Normal',              # 交易对状态
            'symbol': ''                    # 交易对名称
        }

        # BBO相关字段
        self.bbo = {
            'ask_price': 0.0,              # 卖一价
            'ask_qty': 0.0,                # 卖一量
            'bid_price': 0.0,              # 买一价
            'bid_qty': 0.0,                # 买一量
            'symbol': '',                   # 交易对
            'timestamp': 0                  # 时间戳
        }

        # 手续费相关字段
        self.fee_rate = {
            'buyer': 0.0,                  # 买方手续费
            'maker': 0.0002,               # maker手续费
            'seller': 0.0,                 # 卖方手续费
            'taker': 0.0004                # taker手续费
        }

        # 最大持仓相关字段
        self.max_position = {
            'long': {'Notional': 100000000.0},     # 多头最大持仓(按名义价值)
            'short': {'Notional': 100000000.0}     # 空头最大持仓(按名义价值)
        }

        self.max_leverage = 0              # 最大杠杆倍数

        # 从instrument中获取的字段
        self.price_tick = 0.0             # 最小价格变动单位
        self.min_qty = 0.0                # 最小交易数量

    def set_instrument(self, instrument: dict):
        """更新合约信息"""
        self.instrument = instrument
        # 从instrument中提取关键信息
        self.price_tick = float(instrument.get('price_tick', 0.0))
        self.min_qty = float(instrument.get('min_qty', 0.0))

    def set_max_position(self, max_position: dict):
        """更新最大持仓数量"""
        self.max_position = max_position

    def set_max_leverage(self, max_leverage: int):
        """更新最大杠杆倍数"""
        self.max_leverage = max_leverage

    def set_fee_rate(self, fee_rate: dict):
        """更新手续费率"""
        self.fee_rate = fee_rate

    def update_bbo(self, bbo: dict):
        """更新最优买卖价"""
        self.bbo = bbo

    def get_maker_fee(self) -> float:
        """获取maker手续费率"""
        return self.fee_rate.get('maker', 0.0)

    def get_taker_fee(self) -> float:
        """获取taker手续费率"""
        return self.fee_rate.get('taker', 0.0)

    def get_max_long_position(self) -> float:
        """获取最大多头持仓（按名义价值）"""
        return self.max_position.get('long', {}).get('Notional', 0.0)

    def get_max_short_position(self) -> float:
        """获取最大空头持仓（按名义价值）"""
        return self.max_position.get('short', {}).get('Notional', 0.0)

    def round_price(self, price: float) -> float:
        """将价格四舍五入到最小价格单位"""
        if self.price_tick <= 0:
            return price
        return round(price / self.price_tick) * self.price_tick

    def round_quantity(self, quantity: float) -> float:
        """将数量四舍五入到最小数量单位"""
        if self.min_qty <= 0:
            return quantity
        return round(quantity / self.min_qty) * self.min_qty

    def get_mid_price(self) -> float:
        """获取中间价"""
        bid = self.bbo['bid_price']
        ask = self.bbo['ask_price']
        if bid <= 0 or ask <= 0:
            return 0
        return (bid + ask) / 2


    def is_bbo_valid(self) -> bool:
        """检查BBO是否有效"""
        return (self.bbo['bid_price'] > 0 and
                self.bbo['ask_price'] > 0 and
                self.bbo['bid_price'] < self.bbo['ask_price'] and
                time.time() * 1000 -  self.bbo['timestamp'] < 1000)

    def get_amount_precision(self) -> int:
        """获取数量精度"""
        return self.instrument.get('amount_precision', 0)

    def get_price_precision(self) -> int:
        """获取价格精度"""
        return self.instrument.get('price_precision', 0)

    def get_min_notional(self) -> float:
        """获取最小名义价值"""
        return float(self.instrument.get('min_notional', 0.0))

    def get_amount_tick(self) -> float:
        """获取数量最小变动单位"""
        return float(self.instrument.get('amount_tick', 0.0))

    def get_price_tick(self) -> float:
        """获取价格最小变动单位"""
        return float(self.instrument.get('price_tick', 0.0))

    def get_amount_multiplier(self) -> float:
        """获取数量乘数"""
        return float(self.instrument.get('amount_multiplier', 1.0))

    def get_price_multiplier(self) -> float:
        """获取价格乘数"""
        return float(self.instrument.get('price_multiplier', 1.0))

    def is_trading_enabled(self) -> bool:
        """检查交易对是否可交易"""
        return self.instrument.get('state', '') == 'Normal'

    def format_price(self, price: float) -> str:
        """格式化价格，按照价格精度"""
        precision = self.get_price_precision()
        return f"{price:.{precision}f}"

    def format_amount(self, amount: float) -> str:
        """格式化数量，按照数量精度"""
        precision = self.get_amount_precision()
        return f"{amount:.{precision}f}"

    def check_order_valid(self, price: float, amount: float) -> tuple[bool, str]:
        """
        检查订单是否有效

        Args:
            price: 价格
            amount: 数量

        Returns:
            (是否有效, 错误信息)
        """
        if not self.is_trading_enabled():
            return False, "交易对当前不可交易"

        if amount < self.get_min_qty():
            return False, f"数量 {amount} 小于最小交易数量 {self.get_min_qty()}"

        notional = price * amount
        if notional < self.get_min_notional():
            return False, f"名义价值 {notional} 小于最小名义价值 {self.get_min_notional()}"

        return True, ""

    def process_amount(self, amount: float) -> float:
        """
        处理数量：
        1. 应用数量乘数
        2. 使用数量步长round
        3. 保留精度

        Args:
            amount: 原始数量

        Returns:
            处理后的数量
        """
        # 应用数量乘数
        processed = amount * self.get_amount_multiplier()

        # 使用数量步长round
        amount_tick = self.get_amount_tick()
        if amount_tick > 0:
            processed = round(processed / amount_tick) * amount_tick

        # 保留精度
        precision = self.get_amount_precision()
        if precision >= 0:
            processed = round(processed, precision)

        return processed

    def process_price(self, price: float, side: str = None) -> float:
        """
        处理价格：
        1. 应用价格乘数
        2. 根据买卖方向使用价格步长向上或向下取整
        3. 保留精度

        Args:
            price: 原始价格
            side: 买卖方向，"Buy"或"Sell"，如果不提供则使用round四舍五入

        Returns:
            处理后的价格
        """
        # 应用价格乘数
        processed = price * self.get_price_multiplier()

        # 根据买卖方向使用价格步长取整
        price_tick = self.get_price_tick()
        if price_tick > 0:
            if side == "Buy":
                # 买单向下取整
                processed = math.floor(processed / price_tick) * price_tick
            elif side == "Sell":
                # 卖单向上取整
                processed = math.ceil(processed / price_tick) * price_tick
            else:
                # 如果没有提供方向，使用四舍五入
                processed = round(processed / price_tick) * price_tick

        # 保留精度
        precision = self.get_price_precision()
        if precision >= 0:
            processed = round(processed, precision)

        return processed

    def process_order_params(self, price: float, amount: float, side: str = None) -> tuple[float, float]:
        """
        同时处理订单的价格和数量

        Args:
            price: 原始价格
            amount: 原始数量
            side: 买卖方向，"Buy"或"Sell"

        Returns:
            (处理后的价格, 处理后的数量)
        """
        return self.process_price(price, side), self.process_amount(amount)

    def get_min_qty(self) -> float:
        """获取最小交易数量"""
        return float(self.instrument['min_qty'])
