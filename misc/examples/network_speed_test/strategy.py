"""
network_test_strategy.py

用于测试下单网络速度的策略模块 (v2版本)
测试三种网络延迟：
1. 同步命令延迟：从API调用前到API调用后的时间
2. 异步命令延迟：从API调用前到on_order_submitted回调的时间(使用cid追踪)
3. return命令延迟：从return前到on_order_submitted回调的时间(使用cid追踪)
"""

import copy
import json
import traceback
import time
import datetime
import traderv2 as trader # type: ignore
import os
import sys
import numpy as np
from binance import BinanceClient  # 引入修改后的BinanceClient

# 添加depth_imbalance目录到系统路径，以便导入其中的模块
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'depth_imbalance'))

import base_strategy
from symbol_info import SymbolInfo

class Strategy(base_strategy.BaseStrategy):
    def __init__(self, cex_configs, dex_configs, config, trader_instance: trader.TraderV2):

        self.cexs = cex_configs

        # 记录trader实例
        self.trader = trader_instance
        self.has_account = len(cex_configs) > 0

        # 获取交易所信息
        self.exchange = self.cexs[0]['exchange'] if self.cexs and 'exchange' in self.cexs[0] else "unknown"

        # 配置参数
        self.test_symbol = config.get('test_symbol', 'BTC_USDT')
        self.test_interval = config.get('test_interval', 1000)  # 测试间隔（毫秒）
        self.test_price = config.get('test_price', 40000.0)  # 测试价格
        self.test_amount = config.get('test_amount', 0.001)  # 测试数量
        self.test_times = config.get('test_times', 10)  # 每种类型的测试次数
        self.enable_python_test = config.get('enable_python_test', False)  # 是否启用Python测试

        # Binance API配置
        self.binance_api_key = config.get('binance_api_key', '') if 'binance_api_key' in config else (self.cexs[0]['key'] if self.cexs and 'key' in self.cexs[0] else '')
        self.binance_api_secret = config.get('binance_api_secret', '') if 'binance_api_secret' in config else (self.cexs[0]['secret'] if self.cexs and 'secret' in self.cexs[0] else '')
        # 将 BTC_USDT 格式转换为 BTCUSDT 格式
        self.binance_test_symbol = self.test_symbol.replace('_', '')
        self.binance_test_is_futures = self.cexs[0]['exchange'] == "BinanceSwap" if self.cexs else False

        # 初始化Binance客户端（如果提供了API凭证）
        self.binance_client = None
        if self.binance_api_key and self.binance_api_secret:
            try:
                self.binance_client = BinanceClient(self.binance_api_key, self.binance_api_secret, is_colo=self.cexs[0]['is_colo'])
                self.trader.log(f"Binance客户端初始化成功，API Key: {self.binance_api_key[:4]}...{self.binance_api_key[-4:]}", level="INFO", web=True)
                self.trader.log(f"测试交易对: {self.binance_test_symbol}, 是否合约: {self.binance_test_is_futures}", level="INFO", web=True)
            except Exception as e:
                self.trader.log(f"Binance客户端初始化失败: {str(e)}", level="ERROR", web=True)

        # 测试结果存储
        self.sync_delays = []
        self.async_delays = []
        self.return_delays = []
        self.python_delays = []  # 添加Python方法测试的延迟存储

        # 跟踪测试状态
        self.current_test_type = None  # 'sync', 'async', 'return', 'python'
        self.current_test_index = 0
        self.test_start_time = 0
        self.test_cid_map = {}  # 用于追踪命令的cid到开始时间的映射
        self.symbol_info = None

        # 上次测试时间
        self.last_test_time = 0

        # 轮流测试的计数器
        self.test_round = 0  # 当前测试轮次
        self.total_rounds = self.test_times  # 总测试轮次数
        self.current_command_index = 0  # 当前命令类型索引 (0=sync, 1=async, 2=return, 3=python)
        self.command_types = ['sync', 'async', 'return']  # 基础命令类型列表
        if self.enable_python_test:  # 如果启用Python测试，添加到命令类型列表
            self.command_types.append('python')

        self.trader.log(f"网络速度测试策略初始化完成", level="INFO", web=True)

    def name(self):
        """返回策略名称"""
        return "Network Speed Test Strategy"

    def subscribes(self):
        """返回策略需要订阅的数据类型"""
        return [
            {
                "sub": {
                    "SubscribeTimer": {
                        "update_interval": {
                            "secs": self.test_interval // 1000,
                            "nanos": (self.test_interval % 1000) * 1000000
                        },
                        "name": "test_timer"
                    }
                },
            },
            {
                "account_id": 0,
                "sub": {
                    "SubscribeWs": [
                        {
                            "Order": [self.test_symbol]
                        }
                    ]
                }
            }
        ]

    def start(self):
        """策略启动时执行"""
        self.trader.log(f"网络速度测试策略启动", level="INFO", web=True)

        try:
            # 启动时先撤销所有订单
            self.trader.log("策略启动，准备撤销所有现有订单...", level="INFO", web=True)

            # 使用v2 API撤销所有订单
            result = self.trader.batch_cancel_order(0, self.test_symbol, sync=True)

            if 'Ok' in result:
                self.trader.log(f"成功撤销所有{self.test_symbol}订单", level="INFO", web=True)
            else:
                self.trader.log(f"撤销所有订单失败，错误: {result}", level="WARN", web=True)

            # 查询交易对信息
            self.symbol_info = SymbolInfo(self.test_symbol)

            # 查询合约信息
            instrument_result = self.trader.get_instrument(0, self.test_symbol)
            time.sleep(0.1)

            if 'Ok' in instrument_result:
                instrument = instrument_result['Ok']
                self.symbol_info.set_instrument(instrument)
                self.trader.log(f"获取交易对信息成功: {self.test_symbol}", level="INFO", web=True)
            else:
                self.trader.log(f"无法获取交易对信息: {self.test_symbol}", level="ERROR", web=True)
                return

            # 设置10倍杠杆
            self.trader.set_leverage(0, self.test_symbol, 20)

            # 获取当前市场价格
            try:
                bbo_result = self.trader.get_bbo(0, self.test_symbol)
                if 'Ok' in bbo_result:
                    bbo = bbo_result['Ok']
                    if bbo and bbo.get('bid_price') > 0 and bbo.get('ask_price') > 0:
                        # 使用中间价作为测试基准价格
                        self.test_price = (bbo['bid_price'] + bbo['ask_price']) / 3
                        self.test_amount = self.test_amount
                        self.trader.log(f"已更新测试基准价格为当前市场中间价: {self.test_price}", level="INFO", web=True)
            except Exception as e:
                self.trader.log(f"获取市场价格时发生错误: {str(e)}", level="WARN", web=True)
                # 继续使用配置的价格

            # 确保参数合理性
            self.test_price = float(self.test_price)
            self.test_amount = float(self.test_amount)

            # 初始化测试
            self.test_round = 0
            self.current_command_index = 0  # 从同步命令开始

            # 清理之前可能残留的状态
            self.test_cid_map.clear()

            # 构建下单参数
            self.base_order = {
                "cid": trader.create_cid(self.cexs[0]['exchange']),
                "symbol": self.test_symbol,
                "order_type": "Limit",
                "side": "Buy",
                "pos_side": "Long",
                "time_in_force": "PostOnly",
                "price": self.test_price,
                "amount": self.test_amount,
            }

            self.order_params = {
                "is_dual_side": False
            }

        except Exception as e:
            self.trader.log(f"策略启动时发生错误: {str(e)}\n{traceback.format_exc()}", level="ERROR", web=True)

    def on_timer_subscribe(self, timer_name):
        """定时器回调，触发测试"""
        if timer_name != "test_timer":
            return

        try:
            current_time = time.perf_counter()

            # 确保两次测试之间有足够的间隔
            if current_time - self.last_test_time < self.test_interval / 1000 * 0.8:
                return

            self.last_test_time = current_time

            # 计算已完成的测试数量和进度百分比
            total_tests = self.total_rounds * len(self.command_types)  # 四种类型总共的测试次数
            completed_tests = self.test_round * len(self.command_types) + self.current_command_index  # 已完成的测试次数
            progress_percent = (completed_tests / total_tests) * 100

            self.trader.log(f"测试进度: {completed_tests+1}/{total_tests} ({progress_percent:.1f}%)", level="INFO", web=True)

            # 检查是否所有测试都已完成
            if self.test_round >= self.total_rounds:
                self.trader.log("所有测试已完成，生成测试报告...", level="INFO", web=True)
                self.print_test_results()

                # 重置测试状态，准备下一轮测试
                self.sync_delays = []
                self.async_delays = []
                self.return_delays = []
                self.python_delays = []  # 清空Python测试延迟
                self.test_round = 0
                self.current_command_index = 0

                # 清理测试映射
                self.test_cid_map.clear()

                # 延长下一轮测试的间隔时间
                self.last_test_time = current_time + self.test_interval / 1000 * 5
                self.trader.log("测试完全结束，准备下一轮测试...", level="INFO", web=True)
                return

            # 获取当前命令类型
            current_type = self.command_types[self.current_command_index]

            # 执行当前类型的测试
            cmd_result = None
            if current_type == 'sync':
                self.trader.log(f"执行同步命令测试 (轮次 {self.test_round+1}/{self.total_rounds})", level="INFO", web=True)
                self.test_sync_command()
            elif current_type == 'async':
                self.trader.log(f"执行异步命令测试 (轮次 {self.test_round+1}/{self.total_rounds})", level="INFO", web=True)
                self.test_async_command()
            elif current_type == 'return':
                self.trader.log(f"执行return命令测试 (轮次 {self.test_round+1}/{self.total_rounds})", level="INFO", web=True)
                cmd_result = self.test_return_command()
            elif current_type == 'python':
                if self.enable_python_test:  # 只在启用Python测试时执行
                    self.trader.log(f"执行Python方法测试 (轮次 {self.test_round+1}/{self.total_rounds})", level="INFO", web=True)
                    self.test_python_method()
                else:
                    self.trader.log("Python测试未启用，跳过", level="INFO", web=True)
                    self.python_delays.append(0)  # 添加一个占位值

            # 更新命令类型索引
            self.current_command_index += 1

            # 如果已经测试了所有类型，则进入下一轮
            if self.current_command_index >= len(self.command_types):
                self.current_command_index = 0
                self.test_round += 1
                self.trader.log(f"完成第 {self.test_round}/{self.total_rounds} 轮测试", level="INFO", web=True)

            # 如果是return命令测试，需要返回命令结果
            if current_type == 'return':
                return cmd_result

        except Exception as e:
            self.trader.log(f"定时器回调发生错误: {str(e)}\n{traceback.format_exc()}", level="ERROR", web=True)

    def test_sync_command(self):
        """测试同步命令延迟 - 从API调用前到API调用后"""
        try:
            self.trader.log(f"执行同步命令测试 #{self.current_test_index+1}", level="INFO", web=True)

            # 生成唯一的clientID，用于标识此次测试
            client_id = f"t-sync_test_{int(time.perf_counter()*1000)}_{self.current_test_index}"

            # 准备订单
            order = copy.deepcopy(self.base_order)
            order['cid'] = client_id

            # 记录开始时间
            start_time = time.perf_counter() * 1000  # 毫秒

            # 执行同步下单命令
            result = self.trader.place_order(0, order, self.order_params, sync=True)

            # 记录结束时间
            end_time = time.perf_counter() * 1000  # 毫秒

            if 'Ok' not in result:
                self.trader.log(f"同步命令测试 #{self.current_test_index+1} 失败，错误: {result}", level="ERROR", web=True)
                return
            order_id = result['Ok']

            # 计算延迟
            delay = end_time - start_time
            self.sync_delays.append(delay)
            self.trader.log(f"同步命令测试 #{self.current_test_index+1} 完成，延迟: {delay:.2f}ms, 订单ID: {order_id}", level="INFO", web=True)

        except Exception as e:
            self.trader.log(f"同步命令测试发生错误: {str(e)}\n{traceback.format_exc()}", level="ERROR", web=True)
            # 记录一个非常高的延迟值表示错误
            self.sync_delays.append(999999)
            # 继续下一个测试

    def test_async_command(self):
        """测试异步命令延迟 - 从API调用前到on_order_submitted"""
        try:

            self.trader.log(f"执行异步命令测试 #{self.current_test_index+1}", level="INFO", web=True)

            # 生成唯一的clientID，用于标识此次测试
            client_id = f"t-async_test_{int(time.perf_counter()*1000)}_{self.current_test_index}"

            # 准备订单
            order = copy.deepcopy(self.base_order)
            order['cid'] = client_id

            # 记录开始时间
            start_time = time.perf_counter() * 1000  # 毫秒

            # 执行异步下单命令
            self.trader.place_order(0, order, self.order_params, sync=False)

            self.test_cid_map[client_id] = start_time
            # self.pending_async_orders.add(client_id)  # 添加到待处理列表

        except Exception as e:
            self.trader.log(f"异步命令测试发生错误: {str(e)}", level="ERROR", web=True)

    def test_return_command(self):
        """测试return命令延迟 - 从return前到on_order_submitted，一次返回7个订单"""
        try:
            self.trader.log(f"执行return命令测试 #{self.current_test_index+1}，准备返回7个订单", level="INFO", web=True)

            # 记录开始时间
            start_time = time.perf_counter() * 1000  # 毫秒

            # 准备7个不同的订单
            cmds = []
            for i in range(7):
                # 生成唯一的clientID，用于标识此次测试
                client_id = trader.create_cid(self.cexs[0]['exchange'])

                # 准备订单
                order = copy.deepcopy(self.base_order)
                order['cid'] = client_id

                # 存储开始时间，用于在on_order回调中计算延迟
                self.test_cid_map[client_id] = start_time

                # 生成异步下单命令
                cmd = self.trader.place_order(0, order, self.order_params, sync=False, generate=True)['Ok']
                cmds.append(cmd)

            # self.trader.log(f"Return命令测试准备完成，将返回{cmds}订单命令", level="INFO", web=True)

            return {
                "cmds": cmds,
            }

        except Exception as e:
            self.trader.log(f"Return命令测试发生错误: {str(e)}", level="ERROR", web=True)

    def test_python_method(self):
        """测试Python方法下单延迟 - 使用Binance客户端"""
        try:
            self.trader.log(f"执行Python方法测试 #{self.current_test_index+1}", level="INFO", web=True)

            # 检查Binance客户端是否已初始化
            if not self.binance_client:
                self.trader.log("Binance客户端未初始化，无法执行测试", level="ERROR", web=True)
                self.python_delays.append(999999)  # 添加一个高延迟值表示错误
                return

            # 执行Binance下单测试并获取延迟
            # 将测试价格和数量转换为适合Binance API的格式
            # 将BTC_USDT格式转换为BTCUSDT格式
            binance_symbol = self.binance_test_symbol.replace('_', '')
            price = self.test_price
            quantity = self.test_amount

            self.trader.log(f"准备Python{'合约' if self.binance_test_is_futures else '现货'}下单: {binance_symbol}, 价格: {price}, 数量: {quantity}", level="INFO", web=True)

            # 调用Binance客户端的test_order方法
            result = self.binance_client.test_order(
                is_futures=self.binance_test_is_futures,
                symbol=binance_symbol,
                price=price,
                quantity=quantity
            )

            # 从结果中获取延迟
            delay = result['latency']
            is_success = result.get('is_success', False)
            status_code = result.get('status_code', 0)
            response = result.get('response', {})

            # 存储延迟结果
            self.python_delays.append(delay)

            # 记录详细的下单结果
            if not is_success:
                self.trader.log(f"Python方法下单失败 - 状态码: {status_code}, 响应: {response}", level="ERROR", web=True)

            self.trader.log(f"Python方法测试 #{self.current_test_index+1} 完成，延迟: {delay:.2f}ms", level="INFO", web=True)

        except Exception as e:
            self.trader.log(f"Python方法测试发生错误: {str(e)}\n{traceback.format_exc()}", level="ERROR", web=True)
            # 记录一个非常高的延迟值表示错误
            self.python_delays.append(999999)
            # 继续下一个测试

    def on_order_submitted(self, account_id, order_id_result, order):
        # self.trader.log(f"订单提交回调: {order_id_result} {order}", level="TRACE", web=True)
        """订单提交回调 - 用于计算异步和return命令的完整延迟"""
        try:
            end_time = time.perf_counter() * 1000  # 毫秒

            if 'Ok' not in order_id_result:
                self.trader.log(f"订单提交回调失败，错误: {order_id_result}", level="ERROR", web=True)
                return

            # 获取订单的cid
            cid = order.get('cid', '')
            # 从待处理列表中移除
            id = order_id_result['Ok']

            # 检查是否是我们的测试订单
            if cid in self.test_cid_map:
                start_time = self.test_cid_map[cid]
                delay = end_time - start_time

                if cid.startswith('t-async_test_'):
                    self.async_delays.append(delay)
                    self.trader.log(f"异步命令回调收到，CID: {cid}，延迟: {delay:.2f}ms，订单ID: {id}", level="INFO", web=True)
                else:  # return_test
                    self.return_delays.append(delay)
                    self.trader.log(f"Return命令回调收到，CID: {cid}，延迟: {delay:.2f}ms，订单ID: {id}", level="INFO", web=True)

                # 从映射中移除已处理的CID
                del self.test_cid_map[cid]

        except Exception as e:
            self.trader.log(f"订单提交回调处理发生错误: {str(e)}", level="ERROR", web=True)

    def on_order(self, account_id, order):
        """订单状态回调 - 用于撤单操作"""
        # self.trader.log(f"订单状态回调: {order}", level="INFO", web=True)
        try:
            # 处理撤单逻辑
            if order.get('status') == 'Open':
                # 撤单
                start = time.perf_counter() * 1000
                result = self.trader.cancel_order(0, self.test_symbol, cid=order['cid'], sync=True)
                end = time.perf_counter() * 1000
                if 'Ok' not in result:
                    self.trader.log(f"撤单失败，错误: {result}", level="ERROR", web=True)
                else:
                    self.trader.log(f"撤单成功，延迟: {end - start:.2f}ms", level="INFO", web=True)
        except Exception as e:
            self.trader.log(f"订单状态回调处理发生错误: {str(e)}", level="ERROR", web=True)

    def print_test_results(self):
        """打印所有测试结果的统计信息"""
        self.trader.log("====== 网络速度测试结果汇总 ======", level="INFO", web=True)

        # 生成当前日期时间字符串
        now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.trader.log(f"测试时间: {now}", level="INFO", web=True)
        self.trader.log(f"测试交易所: {self.exchange}", level="INFO", web=True)
        self.trader.log(f"测试符号: {self.test_symbol}", level="INFO", web=True)
        self.trader.log(f"每种类型测试次数: {self.test_times}", level="INFO", web=True)
        self.trader.log(f"统计方式: 去除每种类型的最大值后计算", level="INFO", web=True)


        # 同步命令结果 - 去除最大值后计算
        if len(self.sync_delays) > 1:
            # 复制列表并排序，以便去除最大值
            sorted_sync = sorted(self.sync_delays)
            # 去除最大值
            filtered_sync = sorted_sync[:-1]

            avg_sync = sum(filtered_sync) / len(filtered_sync)
            min_sync = min(filtered_sync)
            max_sync = max(filtered_sync)  # 这是去除全局最大值后的最大值
            self.trader.log(f"同步命令延迟 (ms) - 平均: {avg_sync:.2f}, 最小: {min_sync:.2f}, 最大: {max_sync:.2f} (已去除最大值)", level="INFO", web=True)
        else:
            self.trader.log("同步命令延迟: 数据不足，无法去除最大值", level="INFO", web=True)

        # 异步命令结果 - 去除最大值后计算
        if len(self.async_delays) > 1:
            # 复制列表并排序，以便去除最大值
            sorted_async = sorted(self.async_delays)
            # 去除最大值
            filtered_async = sorted_async[:-1]

            avg_async = sum(filtered_async) / len(filtered_async)
            min_async = min(filtered_async)
            max_async = max(filtered_async)  # 这是去除全局最大值后的最大值
            self.trader.log(f"异步命令延迟 (ms) - 平均: {avg_async:.2f}, 最小: {min_async:.2f}, 最大: {max_async:.2f} (已去除最大值)", level="INFO", web=True)
        else:
            self.trader.log("异步命令延迟: 数据不足，无法去除最大值", level="INFO", web=True)

        # Return命令结果 - 去除最大值后计算
        if len(self.return_delays) > 1:
            # 复制列表并排序，以便去除最大值
            sorted_return = sorted(self.return_delays)
            # 去除最大值
            filtered_return = sorted_return[:-1]

            avg_return = sum(filtered_return) / len(filtered_return)
            min_return = min(filtered_return)
            max_return = max(filtered_return)  # 这是去除全局最大值后的最大值
            self.trader.log(f"Return命令延迟 (ms) - 平均: {avg_return:.2f}, 最小: {min_return:.2f}, 最大: {max_return:.2f} (已去除最大值)", level="INFO", web=True)
        else:
            self.trader.log("Return命令延迟: 数据不足，无法去除最大值", level="INFO", web=True)

        # Python方法结果 - 去除最大值后计算
        if len(self.python_delays) > 1:
            # 复制列表并排序，以便去除最大值
            sorted_python = sorted(self.python_delays)
            # 去除最大值
            filtered_python = sorted_python[:-1]

            avg_python = sum(filtered_python) / len(filtered_python)
            min_python = min(filtered_python)
            max_python = max(filtered_python)  # 这是去除全局最大值后的最大值
            self.trader.log(f"Python方法延迟 (ms) - 平均: {avg_python:.2f}, 最小: {min_python:.2f}, 最大: {max_python:.2f} (已去除最大值)", level="INFO", web=True)
        else:
            self.trader.log("Python方法延迟: 数据不足，无法去除最大值", level="INFO", web=True)

        # 计算标准差 - 去除最大值后计算
        if len(self.sync_delays) > 2:  # 需要至少3个数据点（去除一个后至少有2个）
            std_sync = np.std(sorted(self.sync_delays)[:-1])
            self.trader.log(f"同步命令标准差: {std_sync:.2f} ms (已去除最大值)", level="INFO", web=True)

        if len(self.async_delays) > 2:
            std_async = np.std(sorted(self.async_delays)[:-1])
            self.trader.log(f"异步命令标准差: {std_async:.2f} ms (已去除最大值)", level="INFO", web=True)

        if len(self.return_delays) > 2:
            std_return = np.std(sorted(self.return_delays)[:-1])
            self.trader.log(f"Return命令标准差: {std_return:.2f} ms (已去除最大值)", level="INFO", web=True)

        if len(self.python_delays) > 2:
            std_python = np.std(sorted(self.python_delays)[:-1])
            self.trader.log(f"Python方法标准差: {std_python:.2f} ms (已去除最大值)", level="INFO", web=True)

        self.trader.log("================================", level="INFO", web=True)

        # 将结果保存到文件
        self.save_results_to_file()

        # 重要：通知用户测试完成
        self.trader.log(f"第{self._get_test_round_number()}轮测试完成，结果已保存", level="INFO", web=True)

    def _get_test_round_number(self):
        """获取当前测试轮次编号"""
        results_dir = os.path.join(os.path.dirname(__file__), 'results')
        if not os.path.exists(results_dir):
            return 1

        json_files = [f for f in os.listdir(results_dir) if f.endswith('.json')]
        return len(json_files) + 1

    def save_results_to_file(self):
        """将测试结果保存到文件"""
        try:
            # 创建结果目录
            results_dir = os.path.join(os.path.dirname(__file__), 'results')
            os.makedirs(results_dir, exist_ok=True)

            # 生成文件名
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.join(results_dir, f"network_test_{self.exchange}_{timestamp}.json")

            # 处理数据 - 去除每种类型的最大值后计算统计信息
            # 同步命令 - 去除最大值
            sync_filtered = sorted(self.sync_delays)[:-1] if len(self.sync_delays) > 1 else self.sync_delays
            sync_avg = sum(sync_filtered) / len(sync_filtered) if sync_filtered else 0
            sync_min = min(sync_filtered) if sync_filtered else 0
            sync_max = max(sync_filtered) if sync_filtered else 0
            sync_std = float(np.std(sync_filtered)) if len(sync_filtered) > 1 else 0

            # 异步命令 - 去除最大值
            async_filtered = sorted(self.async_delays)[:-1] if len(self.async_delays) > 1 else self.async_delays
            async_avg = sum(async_filtered) / len(async_filtered) if async_filtered else 0
            async_min = min(async_filtered) if async_filtered else 0
            async_max = max(async_filtered) if async_filtered else 0
            async_std = float(np.std(async_filtered)) if len(async_filtered) > 1 else 0

            # Return命令 - 去除最大值
            return_filtered = sorted(self.return_delays)[:-1] if len(self.return_delays) > 1 else self.return_delays
            return_avg = sum(return_filtered) / len(return_filtered) if return_filtered else 0
            return_min = min(return_filtered) if return_filtered else 0
            return_max = max(return_filtered) if return_filtered else 0
            return_std = float(np.std(return_filtered)) if len(return_filtered) > 1 else 0

            # Python方法 - 去除最大值
            python_filtered = sorted(self.python_delays)[:-1] if len(self.python_delays) > 1 else self.python_delays
            python_avg = sum(python_filtered) / len(python_filtered) if python_filtered else 0
            python_min = min(python_filtered) if python_filtered else 0
            python_max = max(python_filtered) if python_filtered else 0
            python_std = float(np.std(python_filtered)) if len(python_filtered) > 1 else 0

            # 计算分位数数据
            # 同步命令分位数
            sync_quantiles = {}
            if len(sync_filtered) > 0:
                for q in [0, 0.25, 0.5, 0.75, 0.9, 0.99, 1.0]:
                    if q == 0:
                        sync_quantiles[str(int(q*100))] = sync_min
                    elif q == 1.0:
                        sync_quantiles[str(int(q*100))] = sync_max
                    else:
                        # 使用numpy的percentile计算分位数
                        sync_quantiles[str(int(q*100))] = float(np.percentile(sync_filtered, q*100))

            # 异步命令分位数
            async_quantiles = {}
            if len(async_filtered) > 0:
                for q in [0, 0.25, 0.5, 0.75, 0.9, 0.99, 1.0]:
                    if q == 0:
                        async_quantiles[str(int(q*100))] = async_min
                    elif q == 1.0:
                        async_quantiles[str(int(q*100))] = async_max
                    else:
                        async_quantiles[str(int(q*100))] = float(np.percentile(async_filtered, q*100))

            # Return命令分位数
            return_quantiles = {}
            if len(return_filtered) > 0:
                for q in [0, 0.25, 0.5, 0.75, 0.9, 0.99, 1.0]:
                    if q == 0:
                        return_quantiles[str(int(q*100))] = return_min
                    elif q == 1.0:
                        return_quantiles[str(int(q*100))] = return_max
                    else:
                        return_quantiles[str(int(q*100))] = float(np.percentile(return_filtered, q*100))

            # Python方法分位数
            python_quantiles = {}
            if len(python_filtered) > 0:
                for q in [0, 0.25, 0.5, 0.75, 0.9, 0.99, 1.0]:
                    if q == 0:
                        python_quantiles[str(int(q*100))] = python_min
                    elif q == 1.0:
                        python_quantiles[str(int(q*100))] = python_max
                    else:
                        python_quantiles[str(int(q*100))] = float(np.percentile(python_filtered, q*100))

            # 准备结果数据
            result_data = {
                "test_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "exchange": self.exchange,
                "test_symbol": self.test_symbol,
                "test_times": self.test_times,
                "note": "统计信息已去除每种类型的最大值",
                # 原始数据 - 保留完整数据集
                "sync_delays": self.sync_delays,
                "async_delays": self.async_delays,
                "return_delays": self.return_delays,
                "python_delays": self.python_delays,  # 添加Python方法数据
                # 过滤后的数据集 - 去除最大值
                "sync_filtered": sync_filtered,
                "async_filtered": async_filtered,
                "return_filtered": return_filtered,
                "python_filtered": python_filtered,  # 添加过滤后的Python方法数据
                # 统计信息 - 使用过滤后的数据
                "sync_stats": {
                    "avg": sync_avg,
                    "min": sync_min,
                    "max": sync_max,
                    "std": sync_std,
                    "removed_max": max(self.sync_delays) if self.sync_delays else 0,
                    "quantiles": sync_quantiles
                },
                "async_stats": {
                    "avg": async_avg,
                    "min": async_min,
                    "max": async_max,
                    "std": async_std,
                    "removed_max": max(self.async_delays) if self.async_delays else 0,
                    "quantiles": async_quantiles
                },
                "return_stats": {
                    "avg": return_avg,
                    "min": return_min,
                    "max": return_max,
                    "std": return_std,
                    "removed_max": max(self.return_delays) if self.return_delays else 0,
                    "quantiles": return_quantiles
                },
                "python_stats": {  # 添加Python方法统计信息
                    "avg": python_avg,
                    "min": python_min,
                    "max": python_max,
                    "std": python_std,
                    "removed_max": max(self.python_delays) if self.python_delays else 0,
                    "quantiles": python_quantiles
                }
            }

            # 保存到文件
            with open(filename, 'w') as f:
                json.dump(result_data, f, indent=2)

            self.trader.log(f"测试结果已保存到文件: {filename}", level="INFO", web=True)

            # 生成HTML报告
            html_file = os.path.join(results_dir, f"network_test_{self.exchange}_{timestamp}.html")
            self._generate_html_report(html_file, result_data)

            self.trader.log(f"测试HTML报告已生成: {html_file}", level="INFO", web=True)

        except Exception as e:
            self.trader.log(f"保存测试结果到文件时发生错误: {str(e)}\n{traceback.format_exc()}", level="ERROR", web=True)

    def _generate_html_report(self, html_file, result_data):
        """生成HTML测试报告"""
        try:
            # 提取分位数
            quantile_labels = list(result_data['sync_stats']['quantiles'].keys())
            sync_quantile_values = [result_data['sync_stats']['quantiles'].get(q, 0) for q in quantile_labels]
            async_quantile_values = [result_data['async_stats']['quantiles'].get(q, 0) for q in quantile_labels]
            return_quantile_values = [result_data['return_stats']['quantiles'].get(q, 0) for q in quantile_labels]
            python_quantile_values = [result_data['python_stats']['quantiles'].get(q, 0) for q in quantile_labels]

            # 找出每种测试类型的最大值索引
            syncMaxIndex = result_data['sync_delays'].index(result_data['sync_stats']['removed_max']) if result_data['sync_delays'] else -1
            asyncMaxIndex = result_data['async_delays'].index(result_data['async_stats']['removed_max']) if result_data['async_delays'] else -1
            returnMaxIndex = result_data['return_delays'].index(result_data['return_stats']['removed_max']) if result_data['return_delays'] else -1
            pythonMaxIndex = result_data['python_delays'].index(result_data['python_stats']['removed_max']) if result_data['python_delays'] else -1

            html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络速度测试报告 - {self.exchange} - {result_data['test_time']}</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {{
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }}
        body {{
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }}
        .container {{
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }}
        h1, h2 {{
            color: #2c3e50;
            margin-bottom: 15px;
        }}
        h1 {{
            text-align: center;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
            margin-bottom: 25px;
        }}
        .test-info {{
            background-color: #f1f8ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }}
        .note {{
            background-color: #fff8e1;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #ffcc80;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            overflow-x: auto;
            display: block;
        }}
        th, td {{
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }}
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        th {{
            background-color: #f1f1f1;
            font-weight: bold;
        }}
        .chart-container {{
            position: relative;
            margin: 20px 0;
            height: 400px;
            width: 100%;
        }}
        footer {{
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #777;
            font-size: 0.9em;
        }}
        .section-title {{
            font-weight: bold;
            margin: 20px 0 10px 0;
            color: #333;
            font-size: 1.1em;
        }}
        @media (max-width: 768px) {{
            .container {{
                padding: 15px;
            }}
            th, td {{
                padding: 8px 10px;
                font-size: 0.9em;
            }}
            .chart-container {{
                height: 300px;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>网络速度测试报告</h1>

        <div class="test-info">
            <p><strong>测试时间:</strong> {result_data['test_time']}</p>
            <p><strong>测试交易所:</strong> {self.exchange}</p>
            <p><strong>测试交易对:</strong> {result_data['test_symbol']}</p>
            <p><strong>每种类型测试次数:</strong> {result_data['test_times']}</p>
            <p><strong>测试方式:</strong> 四种命令类型（同步、异步、返回、Python方法）交替测试</p>
        </div>

        <div class="note">
            <strong>注意:</strong> 本报告的统计数据已去除每种命令类型中的最大延迟值，以减少极端值对结果的影响。在图表中，被移除的最大值点使用红色三角形标记。
        </div>

        <h2>测试结果统计（已去除最大值）</h2>
        <table>
            <tr>
                <th>命令类型</th>
                <th>平均延迟 (ms)</th>
                <th>最小延迟 (ms)</th>
                <th>中位数 (ms)</th>
                <th>最大延迟 (ms)</th>
                <th>标准差 (ms)</th>
                <th>被去除的最大值 (ms)</th>
            </tr>
            <tr>
                <td><strong>同步命令</strong></td>
                <td>{result_data['sync_stats']['avg']:.2f}</td>
                <td>{result_data['sync_stats']['min']:.2f}</td>
                <td>{result_data['sync_stats']['quantiles'].get('50', 0):.2f}</td>
                <td>{result_data['sync_stats']['max']:.2f}</td>
                <td>{result_data['sync_stats']['std']:.2f}</td>
                <td><span style="color: #e74c3c;">{result_data['sync_stats']['removed_max']:.2f}</span></td>
            </tr>
            <tr>
                <td><strong>异步命令</strong></td>
                <td>{result_data['async_stats']['avg']:.2f}</td>
                <td>{result_data['async_stats']['min']:.2f}</td>
                <td>{result_data['async_stats']['quantiles'].get('50', 0):.2f}</td>
                <td>{result_data['async_stats']['max']:.2f}</td>
                <td>{result_data['async_stats']['std']:.2f}</td>
                <td><span style="color: #e74c3c;">{result_data['async_stats']['removed_max']:.2f}</span></td>
            </tr>
            <tr>
                <td><strong>Return命令</strong></td>
                <td>{result_data['return_stats']['avg']:.2f}</td>
                <td>{result_data['return_stats']['min']:.2f}</td>
                <td>{result_data['return_stats']['quantiles'].get('50', 0):.2f}</td>
                <td>{result_data['return_stats']['max']:.2f}</td>
                <td>{result_data['return_stats']['std']:.2f}</td>
                <td><span style="color: #e74c3c;">{result_data['return_stats']['removed_max']:.2f}</span></td>
            </tr>
            <tr>
                <td><strong>Python方法</strong></td>
                <td>{result_data['python_stats']['avg']:.2f}</td>
                <td>{result_data['python_stats']['min']:.2f}</td>
                <td>{result_data['python_stats']['quantiles'].get('50', 0):.2f}</td>
                <td>{result_data['python_stats']['max']:.2f}</td>
                <td>{result_data['python_stats']['std']:.2f}</td>
                <td><span style="color: #e74c3c;">{result_data['python_stats']['removed_max']:.2f}</span></td>
            </tr>
        </table>

        <h2>延迟统计比较</h2>
        <div class="section-title">平均延迟与中位数对比</div>
        <div class="chart-container">
            <canvas id="delayBarChart"></canvas>
        </div>

        <div class="section-title">延迟分位数分布</div>
        <div class="chart-container">
            <canvas id="quantileChart"></canvas>
        </div>

        <h2>命令延迟分布对比</h2>
        <div class="chart-container">
            <canvas id="combinedDelayChart"></canvas>
        </div>

        <footer>
            <p>报告生成时间: {result_data['test_time']} | 交易所: {self.exchange} | 测试总数: {result_data['test_times'] * len(self.command_types)} 次</p>
        </footer>

        <script>
            // 设置全局样式
            Chart.defaults.font.family = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
            Chart.defaults.font.size = 12;
            Chart.defaults.color = '#666';

            // 延迟统计条形图
            const ctxBar = document.getElementById('delayBarChart').getContext('2d');
            new Chart(ctxBar, {{
                type: 'bar',
                data: {{
                    labels: ['同步命令', '异步命令', 'Return命令', 'Python方法'],
                    datasets: [
                        {{
                            label: '平均延迟 (ms)',
                            data: [{result_data['sync_stats']['avg']:.2f},
                                   {result_data['async_stats']['avg']:.2f},
                                   {result_data['return_stats']['avg']:.2f},
                                   {result_data['python_stats']['avg']:.2f}],
                            backgroundColor: 'rgba(52, 152, 219, 0.7)',
                            borderColor: 'rgba(52, 152, 219, 1)',
                            borderWidth: 1
                        }},
                        {{
                            label: '中位数延迟 (ms)',
                            data: [{result_data['sync_stats']['quantiles'].get('50', 0):.2f},
                                   {result_data['async_stats']['quantiles'].get('50', 0):.2f},
                                   {result_data['return_stats']['quantiles'].get('50', 0):.2f},
                                   {result_data['python_stats']['quantiles'].get('50', 0):.2f}],
                            backgroundColor: 'rgba(46, 204, 113, 0.7)',
                            borderColor: 'rgba(46, 204, 113, 1)',
                            borderWidth: 1
                        }}
                    ]
                }},
                options: {{
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {{
                        y: {{
                            beginAtZero: true,
                            title: {{
                                display: true,
                                text: '延迟 (ms)'
                            }}
                        }}
                    }},
                    plugins: {{
                        legend: {{
                            position: 'bottom'
                        }}
                    }}
                }}
            }});

            // 分位图 - 展示各种命令在不同分位点的延迟值
            const ctxQuantile = document.getElementById('quantileChart').getContext('2d');
            new Chart(ctxQuantile, {{
                type: 'line',
                data: {{
                    labels: {json.dumps(quantile_labels)},
                    datasets: [
                        {{
                            label: '同步命令',
                            data: {json.dumps(sync_quantile_values)},
                            borderColor: 'rgba(52, 152, 219, 1)',
                            backgroundColor: 'rgba(52, 152, 219, 0.1)',
                            fill: true,
                            tension: 0.4,
                            borderWidth: 2,
                            pointRadius: 4,
                            pointBackgroundColor: 'rgba(52, 152, 219, 1)'
                        }},
                        {{
                            label: '异步命令',
                            data: {json.dumps(async_quantile_values)},
                            borderColor: 'rgba(46, 204, 113, 1)',
                            backgroundColor: 'rgba(46, 204, 113, 0.1)',
                            fill: true,
                            tension: 0.4,
                            borderWidth: 2,
                            pointRadius: 4,
                            pointBackgroundColor: 'rgba(46, 204, 113, 1)'
                        }},
                        {{
                            label: 'Return命令',
                            data: {json.dumps(return_quantile_values)},
                            borderColor: 'rgba(231, 76, 60, 1)',
                            backgroundColor: 'rgba(231, 76, 60, 0.1)',
                            fill: true,
                            tension: 0.4,
                            borderWidth: 2,
                            pointRadius: 4,
                            pointBackgroundColor: 'rgba(231, 76, 60, 1)'
                        }},
                        {{
                            label: 'Python方法',
                            data: {json.dumps(python_quantile_values)},
                            borderColor: 'rgba(155, 89, 182, 1)',
                            backgroundColor: 'rgba(155, 89, 182, 0.1)',
                            fill: true,
                            tension: 0.4,
                            borderWidth: 2,
                            pointRadius: 4,
                            pointBackgroundColor: 'rgba(155, 89, 182, 1)'
                        }}
                    ]
                }},
                options: {{
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {{
                        y: {{
                            beginAtZero: true,
                            title: {{
                                display: true,
                                text: '延迟 (ms)'
                            }}
                        }},
                        x: {{
                            title: {{
                                display: true,
                                text: '分位数'
                            }}
                        }}
                    }},
                    plugins: {{
                        legend: {{
                            position: 'bottom'
                        }},
                        tooltip: {{
                            callbacks: {{
                                label: function(context) {{
                                    return context.dataset.label + ': ' + context.raw.toFixed(2) + ' ms';
                                }}
                            }}
                        }}
                    }}
                }}
            }});

            // 合并四条折线到一个图表 - 显示所有测试点
            const combinedCtx = document.getElementById('combinedDelayChart').getContext('2d');

            // 同步命令最大索引
            const syncMaxIndex = {syncMaxIndex};
            // 异步命令最大索引
            const asyncMaxIndex = {asyncMaxIndex};
            // return命令最大索引
            const returnMaxIndex = {returnMaxIndex};
            // python方法最大索引
            const pythonMaxIndex = {pythonMaxIndex};

            new Chart(combinedCtx, {{
                type: 'line',
                data: {{
                    datasets: [
                        {{
                            label: '同步命令延迟 (ms)',
                            data: {json.dumps([{"x": i+1, "y": v} for i, v in enumerate(result_data['sync_delays'])])},
                            fill: false,
                            borderColor: 'rgba(52, 152, 219, 1)',
                            backgroundColor: 'rgba(52, 152, 219, 0.5)',
                            tension: 0.1,
                            borderWidth: 2,
                            pointRadius: function(context) {{
                                const index = context.dataIndex;
                                if (index === syncMaxIndex) {{
                                    return 7;
                                }}
                                return 4;
                            }},
                            pointStyle: function(context) {{
                                const index = context.dataIndex;
                                if (index === syncMaxIndex) {{
                                    return 'triangle';
                                }}
                                return 'circle';
                            }},
                            pointBackgroundColor: function(context) {{
                                const index = context.dataIndex;
                                if (index === syncMaxIndex) {{
                                    return 'rgba(231, 76, 60, 0.9)';
                                }}
                                return 'rgba(52, 152, 219, 0.8)';
                            }},
                            pointBorderColor: function(context) {{
                                const index = context.dataIndex;
                                if (index === syncMaxIndex) {{
                                    return 'rgba(231, 76, 60, 1)';
                                }}
                                return 'rgba(52, 152, 219, 1)';
                            }},
                            pointHoverRadius: 6
                        }},
                        {{
                            label: '异步命令延迟 (ms)',
                            data: {json.dumps([{"x": i+1, "y": v} for i, v in enumerate(result_data['async_delays'])])},
                            fill: false,
                            borderColor: 'rgba(46, 204, 113, 1)',
                            backgroundColor: 'rgba(46, 204, 113, 0.5)',
                            tension: 0.1,
                            borderWidth: 2,
                            pointRadius: function(context) {{
                                const index = context.dataIndex;
                                if (index === asyncMaxIndex) {{
                                    return 7;
                                }}
                                return 4;
                            }},
                            pointStyle: function(context) {{
                                const index = context.dataIndex;
                                if (index === asyncMaxIndex) {{
                                    return 'triangle';
                                }}
                                return 'circle';
                            }},
                            pointBackgroundColor: function(context) {{
                                const index = context.dataIndex;
                                if (index === asyncMaxIndex) {{
                                    return 'rgba(231, 76, 60, 0.9)';
                                }}
                                return 'rgba(46, 204, 113, 0.8)';
                            }},
                            pointBorderColor: function(context) {{
                                const index = context.dataIndex;
                                if (index === asyncMaxIndex) {{
                                    return 'rgba(231, 76, 60, 1)';
                                }}
                                return 'rgba(46, 204, 113, 1)';
                            }},
                            pointHoverRadius: 6
                        }},
                        {{
                            label: 'Return命令延迟 (ms)',
                            data: {json.dumps([{"x": i+1, "y": v} for i, v in enumerate(result_data['return_delays'])])},
                            fill: false,
                            borderColor: 'rgba(231, 76, 60, 1)',
                            backgroundColor: 'rgba(231, 76, 60, 0.5)',
                            tension: 0.1,
                            borderWidth: 2,
                            pointRadius: function(context) {{
                                const index = context.dataIndex;
                                if (index === returnMaxIndex) {{
                                    return 7;
                                }}
                                return 4;
                            }},
                            pointStyle: function(context) {{
                                const index = context.dataIndex;
                                if (index === returnMaxIndex) {{
                                    return 'triangle';
                                }}
                                return 'circle';
                            }},
                            pointBackgroundColor: function(context) {{
                                const index = context.dataIndex;
                                if (index === returnMaxIndex) {{
                                    return 'rgba(231, 76, 60, 0.9)';
                                }}
                                return 'rgba(231, 76, 60, 1)';
                            }},
                            pointBorderColor: function(context) {{
                                const index = context.dataIndex;
                                if (index === returnMaxIndex) {{
                                    return 'rgba(231, 76, 60, 1)';
                                }}
                                return 'rgba(231, 76, 60, 1)';
                            }},
                            pointHoverRadius: 6
                        }},
                        {{
                            label: 'Python方法延迟 (ms)',
                            data: {json.dumps([{"x": i+1, "y": v} for i, v in enumerate(result_data['python_delays'])])},
                            fill: false,
                            borderColor: 'rgba(155, 89, 182, 1)',
                            backgroundColor: 'rgba(155, 89, 182, 0.5)',
                            tension: 0.1,
                            borderWidth: 2,
                            pointRadius: function(context) {{
                                const index = context.dataIndex;
                                if (index === pythonMaxIndex) {{
                                    return 7;
                                }}
                                return 4;
                            }},
                            pointStyle: function(context) {{
                                const index = context.dataIndex;
                                if (index === pythonMaxIndex) {{
                                    return 'triangle';
                                }}
                                return 'circle';
                            }},
                            pointBackgroundColor: function(context) {{
                                const index = context.dataIndex;
                                if (index === pythonMaxIndex) {{
                                    return 'rgba(231, 76, 60, 0.9)';
                                }}
                                return 'rgba(155, 89, 182, 0.8)';
                            }},
                            pointBorderColor: function(context) {{
                                const index = context.dataIndex;
                                if (index === pythonMaxIndex) {{
                                    return 'rgba(231, 76, 60, 1)';
                                }}
                                return 'rgba(155, 89, 182, 1)';
                            }},
                            pointHoverRadius: 6
                        }}
                    ]
                }},
                options: {{
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {{
                        y: {{
                            beginAtZero: true,
                            title: {{
                                display: true,
                                text: '延迟 (ms)'
                            }}
                        }},
                        x: {{
                            type: 'linear',
                            position: 'bottom',
                            title: {{
                                display: true,
                                text: '测试序号'
                            }},
                            ticks: {{
                                stepSize: 1
                            }}
                        }},
                    }},
                    interaction: {{
                        mode: 'index',
                        intersect: false,
                    }},
                    plugins: {{
                        legend: {{
                            position: 'bottom'
                        }},
                        title: {{
                            display: true,
                            text: '四种命令延迟分布对比 (红色三角形标记为被移除的最大值)',
                            font: {{
                                size: 16,
                                weight: 'bold'
                            }}
                        }},
                        tooltip: {{
                            callbacks: {{
                                label: function(context) {{
                                    let label = context.dataset.label || '';
                                    if (label) {{
                                        label += ': ';
                                    }}
                                    label += context.parsed.y.toFixed(2) + ' ms';

                                    // 为最大值添加标记
                                    if ((context.datasetIndex === 0 && context.dataIndex === syncMaxIndex) ||
                                        (context.datasetIndex === 1 && context.dataIndex === asyncMaxIndex) ||
                                        (context.datasetIndex === 2 && context.dataIndex === returnMaxIndex) ||
                                        (context.datasetIndex === 3 && context.dataIndex === pythonMaxIndex)) {{
                                        label += ' (被移除的最大值)';
                                    }}

                                    return label;
                                }}
                            }}
                        }}
                    }}
                }}
            }});
        </script>
    </div>
</body>
</html>
"""

            # 保存HTML文件
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)

            self.trader.log(f"HTML报告已生成: {html_file}", level="INFO", web=True)

        except Exception as e:
            self.trader.log(f"生成HTML报告时发生错误: {str(e)}\n{traceback.format_exc()}", level="ERROR", web=True)

    def on_stop(self):
        """策略停止时执行"""
        self.trader.log("网络速度测试策略停止，准备撤销所有订单...", level="INFO", web=True)

        try:
            self.test_cid_map.clear()

            # 使用v2 API撤销所有订单
            result = self.trader.batch_cancel_order(0, self.test_symbol, sync=True)

            if 'Ok' in result:
                self.trader.log(f"成功撤销所有{self.test_symbol}订单", level="INFO", web=True)
            else:
                self.trader.log(f"撤销所有订单失败，错误: {result}", level="ERROR", web=True)

        except Exception as e:
            self.trader.log(f"撤销所有订单时发生错误: {str(e)}\n{traceback.format_exc()}", level="ERROR", web=True)

    def on_config_update(self, config):
        """配置更新回调"""
        # 更新配置
        self.test_symbol = config.get('test_symbol', self.test_symbol)
        self.test_interval = config.get('test_interval', self.test_interval)
        self.test_price = config.get('test_price', self.test_price)
        self.test_amount = config.get('test_amount', self.test_amount)
        self.test_times = config.get('test_times', self.test_times)
        self.enable_python_test = config.get('enable_python_test', self.enable_python_test)  # 更新Python测试开关

        # 更新命令类型列表
        self.command_types = ['sync', 'async', 'return']
        if self.enable_python_test:
            self.command_types.append('python')

        # Binance API配置
        if 'binance_api_key' in config:
            self.binance_api_key = config.get('binance_api_key', self.binance_api_key)
        if 'binance_api_secret' in config:
            self.binance_api_secret = config.get('binance_api_secret', self.binance_api_secret)

        # 将 BTC_USDT 格式转换为 BTCUSDT 格式
        self.binance_test_symbol = self.test_symbol.replace('_', '')

        # 更新合约标志
        if self.cexs:
            self.binance_test_is_futures = self.cexs[0]['exchange'].lower() in ['binanceswap', 'binanceusdtswap', 'binance_swap', 'binance_usdtswap']

        # 如果API凭证发生变更，重新初始化客户端
        if 'binance_api_key' in config or 'binance_api_secret' in config:
            if self.binance_api_key and self.binance_api_secret:
                try:
                    self.binance_client = BinanceClient(self.binance_api_key, self.binance_api_secret, is_colo=self.cexs[0]['is_colo'])
                    self.trader.log(f"Binance客户端重新初始化成功，API Key: {self.binance_api_key[:4]}...{self.binance_api_key[-4:]}", level="INFO", web=True)
                except Exception as e:
                    self.trader.log(f"Binance客户端重新初始化失败: {str(e)}", level="ERROR", web=True)

        self.trader.log(f"网络速度测试策略配置已更新，交易对: {self.test_symbol}, Binance交易对: {self.binance_test_symbol}", level="INFO", web=True)
