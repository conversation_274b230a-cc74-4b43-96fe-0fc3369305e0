pub mod stand;

use crate::{
    config::DataSourceConfig,
    model::{
        account::AccountId,
        data_source::{RestSubscribe, TimerSubscribe},
    },
    strategy::Strategy,
};
use enum_dispatch::enum_dispatch;
use quant_common::Result;
use quant_common::base::{ExConfig, Subscribes, WsHandler};
use serde::{Deserialize, Serialize};
use stand::StandDataSource;
use tokio::task::Jo<PERSON><PERSON><PERSON><PERSON>;

pub mod sub;

#[enum_dispatch(StaticDataSource)]
#[allow(async_fn_in_trait)]
pub trait DataSource {
    async fn subscribe_ws<H: WsHandler + Send + Sync + 'static>(
        &self,
        account_id: AccountId,
        subs: Subscribes<H>,
    ) -> Result<JoinHandle<()>>;

    /// 根据account id进行Rest轮询
    async fn subscribe_rest<H: Strategy + Send + Sync + 'static>(
        &self,
        account_id: AccountId,
        subs: RestSubscribe,
        handler: H,
    ) -> Result<JoinHandle<()>>;

    async fn subscribe_timer<H: Strategy + Send + Sync + 'static>(
        &self,
        subs: TimerSubscribe,
        handler: H,
    ) -> Result<JoinHandle<()>>;
}

#[enum_dispatch]
pub enum StaticDataSource {
    Stand(StandDataSource),
}

#[derive(Serialize, Deserialize, Copy, Clone, Debug, Default)]
pub enum DataSourceType {
    #[default]
    Stand,
}

pub struct DataSourceFactory;

impl DataSourceFactory {
    pub fn create(config: DataSourceConfig, exchanges: Vec<ExConfig>) -> StaticDataSource {
        let typ = config.typ;
        match typ {
            DataSourceType::Stand => StaticDataSource::Stand(StandDataSource::new(exchanges)),
        }
    }
}

#[cfg(test)]
mod tests {

    use crate::config::MarketConfig;

    use super::*;

    #[tokio::test]
    async fn test_data_source_factory() {
        let config = DataSourceConfig {
            typ: DataSourceType::Stand,
            market_mode: MarketConfig::default(),
        };

        let exchanges = vec![ExConfig::default()];

        let _data_source = DataSourceFactory::create(config, exchanges);
    }
}
