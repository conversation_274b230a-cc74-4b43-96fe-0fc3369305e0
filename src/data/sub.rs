use dashmap::DashMap;
use quant_common::Result;
use quant_common::base::{
    Balance, BboTicker, Depth, Exchange, Funding, FundingFee, Kline, MarkPrice, Order, Position,
    Subscribes, Symbol, Trade, WsHandler,
};
use std::sync::Arc;

use crate::config::{MarketConfig, MarketModeType};
use crate::data::DataSource;
use crate::model::context::Context;
use crate::model::event::Event;
use crate::model::event::account::{AccountEvent, AccountEventInner};
use crate::model::event::market::{MarketEvent, MarketEventInner};
use crate::model::event::net::{NetEvent, NetEventInner};
use crate::{
    dex::DexManager,
    model::data_source::{StrategySubscribe, StrategySubscribeInner},
};

use super::{StaticDataSource, Strategy};

#[derive(<PERSON>fa<PERSON>, <PERSON>lone)]
pub struct SymbolChannelManager {
    channels: DashMap<Symbol, (async_channel::Sender<Event>, async_channel::Receiver<Event>)>,
}

impl SymbolChannelManager {
    async fn get_or_create_channel<H: Strategy + Clone + Send + Sync + 'static>(
        &self,
        symbol: &Symbol,
        handler: H,
    ) -> (async_channel::Sender<Event>, async_channel::Receiver<Event>) {
        match self.channels.get(symbol) {
            Some(channel) => channel.to_owned(),
            None => {
                let (tx, rx) = async_channel::bounded(1);
                let rx_clone = rx.clone();
                tokio::spawn(async move {
                    while let Ok(event) = rx_clone.recv().await {
                        if let Err(e) = handler.handle_event(event).await {
                            error!("handle event error: {}", e);
                        }
                    }
                });
                self.channels
                    .insert(symbol.clone(), (tx.clone(), rx.clone()));
                (tx, rx)
            }
        }
    }
}

#[derive(Clone)]
pub struct ConfigurableStrategyWrapper<H: Strategy + Clone + Send + Sync + 'static> {
    handler: H,
    market_config: MarketConfig,
    symbol_channels: SymbolChannelManager,
}

impl<H: Strategy + Clone + Send + Sync + 'static> ConfigurableStrategyWrapper<H> {
    pub async fn new(handler: H, market_config: MarketConfig) -> Self {
        Self {
            handler,
            market_config,
            symbol_channels: SymbolChannelManager::default(),
        }
    }

    #[inline(always)]
    async fn handle_market_event(
        &self,
        event: MarketEvent,
        mode: MarketModeType,
        symbol: &Symbol,
    ) -> Result<()> {
        let event = Event::Market(event);

        match mode {
            MarketModeType::Latest => {
                let (tx, rx) = self
                    .symbol_channels
                    .get_or_create_channel(symbol, self.handler.clone())
                    .await;
                let _ = rx.try_recv();
                if tx.send(event).await.is_ok() {
                    tokio::task::yield_now().await;
                }
            }
            MarketModeType::All => {
                self.handler.handle_event(event).await?;
            }
        }

        Ok(())
    }
}

impl<H: Strategy + Clone + Send + Sync + 'static> WsHandler for ConfigurableStrategyWrapper<H> {
    #[inline(always)]
    async fn on_connected(&self, exchange: Exchange, id: usize) -> quant_common::Result<()> {
        let event = Event::Net(NetEvent {
            exchange,
            account_id: id,
            event: NetEventInner::WsConnected,
        });
        self.handler.handle_event(event).await
    }

    #[inline(always)]
    async fn on_disconnected(&self, exchange: Exchange, id: usize) -> quant_common::Result<()> {
        let event = Event::Net(NetEvent {
            exchange,
            account_id: id,
            event: NetEventInner::WsDisconnected,
        });
        self.handler.handle_event(event).await
    }

    #[inline(always)]
    async fn on_mark_price(
        &self,
        mark_price: MarkPrice,
        exchange: Exchange,
    ) -> quant_common::Result<()> {
        let event = MarketEvent {
            exchange,
            context: Context::default(),
            event: MarketEventInner::MarkPrice(mark_price.clone()),
        };
        self.handle_market_event(event, self.market_config.mark_price, &mark_price.symbol)
            .await
    }

    #[inline(always)]
    async fn on_bbo(&self, bbo: BboTicker, exchange: Exchange) -> quant_common::Result<()> {
        let event = MarketEvent {
            exchange,
            context: Context::default(),
            event: MarketEventInner::BboTicker(bbo.clone()),
        };
        self.handle_market_event(event, self.market_config.bbo, &bbo.symbol)
            .await
    }

    #[inline(always)]
    async fn on_depth(&self, depth: Depth, exchange: Exchange) -> quant_common::Result<()> {
        let event = MarketEvent {
            exchange,
            context: Context::default(),
            event: MarketEventInner::Depth(depth.clone()),
        };
        self.handle_market_event(event, self.market_config.depth, &depth.symbol)
            .await
    }

    #[inline(always)]
    async fn on_funding(&self, funding: Funding, exchange: Exchange) -> quant_common::Result<()> {
        let event = MarketEvent {
            exchange,
            context: Context::default(),
            event: MarketEventInner::Funding(vec![funding.clone()]),
        };
        self.handle_market_event(event, self.market_config.funding, &funding.symbol)
            .await
    }

    #[inline(always)]
    async fn on_trade(&self, trade: Trade, exchange: Exchange) -> quant_common::Result<()> {
        let event = MarketEvent {
            exchange,
            context: Context::default(),
            event: MarketEventInner::Trade(trade.clone()),
        };
        self.handle_market_event(event, self.market_config.trade, &trade.symbol)
            .await
    }

    #[inline(always)]
    async fn on_kline(&self, kline: Kline, exchange: Exchange) -> quant_common::Result<()> {
        let event = MarketEvent {
            exchange,
            context: Context::default(),
            event: MarketEventInner::Kline(kline.clone()),
        };
        self.handle_market_event(event, MarketModeType::All, &kline.symbol)
            .await
    }

    #[inline(always)]
    async fn on_order(&self, order: Order, id: usize) -> quant_common::Result<()> {
        let event = Event::Account(AccountEvent {
            account_id: id,
            context: Context::default(),
            event: AccountEventInner::Order(order),
        });
        self.handler.handle_event(event).await
    }

    #[inline(always)]
    async fn on_position(&self, position: Position, id: usize) -> quant_common::Result<()> {
        let event = Event::Account(AccountEvent {
            account_id: id,
            context: Context::default(),
            event: AccountEventInner::Position(vec![position]),
        });
        self.handler.handle_event(event).await
    }

    #[inline(always)]
    async fn on_balance(&self, balance: Balance, id: usize) -> quant_common::Result<()> {
        let event = Event::Account(AccountEvent {
            account_id: id,
            context: Context::default(),
            event: AccountEventInner::Balance(vec![balance]),
        });
        self.handler.handle_event(event).await
    }

    #[inline(always)]
    async fn on_funding_fee(&self, fuding_fee: FundingFee, id: usize) -> quant_common::Result<()> {
        let event = Event::Account(AccountEvent {
            account_id: id,
            context: Context::default(),
            event: AccountEventInner::FudingFee(fuding_fee),
        });
        self.handler.handle_event(event).await
    }

    #[inline(always)]
    async fn on_order_and_fill(&self, order: Order, id: usize) -> quant_common::Result<()> {
        let event = Event::Account(AccountEvent {
            account_id: id,
            context: Context::default(),
            event: AccountEventInner::OrderAndFill(order),
        });
        self.handler.handle_event(event).await
    }
}

pub async fn sub<H: Strategy + Clone + Send + Sync + 'static>(
    subs: Vec<StrategySubscribe>,
    handler: H,
    dex_manager: Arc<DexManager>,
    market_config: MarketConfig,
    data_source: Arc<StaticDataSource>,
) -> Result<()> {
    debug!("Subscribes: {:?}", subs);
    for sub in subs {
        match sub.sub {
            StrategySubscribeInner::SubscribeWs(ws_sub) => {
                if ws_sub.is_empty() {
                    continue;
                }

                let ws_subscribes = Subscribes::new(
                    sub.account_id,
                    ConfigurableStrategyWrapper::new(handler.clone(), market_config.clone()).await,
                    ws_sub,
                );
                data_source
                    .subscribe_ws(sub.account_id, ws_subscribes)
                    .await?;
            }
            StrategySubscribeInner::SubscribeRest(rest_subscribe) => {
                data_source
                    .subscribe_rest(sub.account_id, rest_subscribe, handler.clone())
                    .await?;
            }
            StrategySubscribeInner::SubscribeTimer(timer_subscribe) => {
                data_source
                    .subscribe_timer(timer_subscribe, handler.clone())
                    .await?;
            }
            StrategySubscribeInner::SubscribeDex(value) => {
                dex_manager.subscribe(sub.account_id, value).await?;
            }
        }
    }
    Ok(())
}
