use std::time::Duration;

use notify::PollWatcher;
use notify_debouncer_mini::{
    Config, Deboun<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Debounced<PERSON><PERSON>, Debouncer, new_debouncer_opt,
};
use quant_common::Result;
use tokio::sync::mpsc::{Receiver, Sender, channel};

pub fn async_watcher() -> Result<(Debouncer<PollWatcher>, Receiver<DebouncedEvent>)> {
    let (tx, rx) = channel(100);

    let backend_config = notify::Config::default().with_poll_interval(Duration::from_secs(5));
    // debouncer configuration
    let debouncer_config = Config::default()
        .with_timeout(Duration::from_millis(1000))
        .with_notify_config(backend_config);
    // select backend via fish operator, here PollWatcher backend
    let handler = MyHandler { tx };
    let debouncer = new_debouncer_opt(debouncer_config, handler)?;

    Ok((debouncer, rx))
}

struct MyHandler {
    tx: Sender<DebouncedEvent>,
}

impl DebounceEventHandler for MyHandler {
    fn handle_event(&mut self, event: DebounceEventResult) {
        match event {
            Ok(events) => {
                for event in events {
                    if let Err(e) = self.tx.try_send(event) {
                        error!("发送文件变更事件失败: {}", e);
                    }
                }
            }
            Err(e) => {
                error!("处理文件变更失败: {}", e);
            }
        }
    }
}
