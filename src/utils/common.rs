use quant_common::base::Exchange;

pub fn get_bin_name() -> String {
    std::env::current_exe()
        .ok()
        .and_then(|pb| pb.file_stem().map(|s| s.to_string_lossy().into_owned()))
        .unwrap_or_else(|| "unknown".to_string())
}

pub fn default_use_ws_api(exchange: Exchange) -> bool {
    matches!(
        exchange,
        Exchange::BinanceSpot | Exchange::OkxSwap | Exchange::OkxSpot | Exchange::OkxMargin // | Exchange::BitgetSwap
                                                                                            // | Exchange::BitgetMargin
                                                                                            // | Exchange::GateSwap
                                                                                            // | Exchange::GateSpot
                                                                                            //| Exchange::KucoinMargin
    )
}
