use fs2::statvfs;
use serde::{Deserialize, Serialize};
use std::env;
use std::io;
use std::thread;
use std::time::Duration;
use sysinfo::{Networks, System};

/// 网络使用情况
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkUsage {
    pub up: String,   // 上行速率(Kb/s)
    pub down: String, // 下行速率(Kb/s)
}

/// 静态系统信息（不会改变的信息）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StaticSystemInfo {
    pub cpu_count: String,    // CPU核心数
    pub total_memory: String, // 总内存大小(GB)
    pub disk_size: String,    // 磁盘总大小
    pub os_info: String,      // 操作系统信息
}

/// 动态系统信息（需要实时获取的信息）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DynamicSystemInfo {
    pub cpu_usage: String,           // CPU使用率(%)
    pub memory_usage: String,        // 内存使用率(%)
    pub disk_usage: String,          // 磁盘使用率(%)
    pub network_usage: NetworkUsage, // 网络使用情况
}

/// 完整的系统信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemInfo {
    pub cpu: String,                 // CPU配置
    pub cpu_usage: String,           // CPU使用率(%)
    pub memory: String,              // 内存大小(GB)
    pub memory_usage: String,        // 内存使用率(%)
    pub disk: String,                // 磁盘大小
    pub disk_usage: String,          // 磁盘使用率(%)
    pub network_usage: NetworkUsage, // 网络使用情况
    pub os_info: String,             // 操作系统信息
}

/// 系统信息管理器
pub struct SystemInfoManager {
    system: System,
    static_info: StaticSystemInfo,
}

impl SystemInfoManager {
    /// 创建新的系统信息管理器
    pub fn new() -> io::Result<Self> {
        let mut system = System::new_all();
        system.refresh_all();

        // 等待一小段时间后再次刷新CPU信息以获得准确的使用率
        thread::sleep(Duration::from_millis(200));
        system.refresh_cpu_all();

        // 获取静态信息（只需要获取一次）
        let static_info = StaticSystemInfo {
            cpu_count: Self::get_cpu_count(&system),
            total_memory: Self::get_total_memory(&system),
            disk_size: Self::get_disk_size()?,
            os_info: Self::get_os_info(),
        };

        Ok(SystemInfoManager {
            system,
            static_info,
        })
    }

    /// 获取完整的系统信息
    pub fn get_system_info(&mut self) -> io::Result<SystemInfo> {
        // 刷新动态信息
        self.system.refresh_cpu_all();
        self.system.refresh_memory();

        let dynamic_info = self.get_dynamic_info()?;

        Ok(SystemInfo {
            cpu: self.static_info.cpu_count.clone(),
            cpu_usage: dynamic_info.cpu_usage,
            memory: self.static_info.total_memory.clone(),
            memory_usage: dynamic_info.memory_usage,
            disk: self.static_info.disk_size.clone(),
            disk_usage: dynamic_info.disk_usage,
            network_usage: dynamic_info.network_usage,
            os_info: self.static_info.os_info.clone(),
        })
    }

    /// 获取静态系统信息
    pub fn get_static_info(&self) -> &StaticSystemInfo {
        &self.static_info
    }

    /// 获取动态系统信息
    pub fn get_dynamic_info(&mut self) -> io::Result<DynamicSystemInfo> {
        // 刷新系统信息
        self.system.refresh_cpu_all();
        self.system.refresh_memory();

        Ok(DynamicSystemInfo {
            cpu_usage: Self::get_cpu_usage(&self.system),
            memory_usage: Self::get_memory_usage(&self.system),
            disk_usage: Self::get_disk_usage()?,
            network_usage: Self::get_network_usage(),
        })
    }

    /// 获取当前目录所在的根目录
    fn get_root_mount_point() -> io::Result<String> {
        let current_dir = env::current_dir()?;

        if cfg!(windows) {
            // Windows: 获取驱动器盘符
            if let Some(root) = current_dir.ancestors().last()
                && let Some(root_str) = root.to_str()
            {
                return Ok(root_str.to_string());
            }
            // 降级到默认C盘
            Ok("C:\\".to_string())
        } else {
            // Unix-like系统: 从当前目录开始向上查找最合适的挂载点
            // 先尝试当前路径的所有父目录
            for ancestor in current_dir.ancestors() {
                if statvfs(ancestor).is_ok()
                    && let Some(path_str) = ancestor.to_str()
                    && !path_str.is_empty()
                {
                    return Ok(path_str.to_string());
                }
            }

            // 降级到根目录
            Ok("/".to_string())
        }
    }

    /// 获取CPU核心数
    fn get_cpu_count(sys: &System) -> String {
        sys.cpus().len().to_string()
    }

    /// 获取CPU使用率
    fn get_cpu_usage(sys: &System) -> String {
        let cpus = sys.cpus();
        if cpus.is_empty() {
            return "0.00".to_string();
        }

        let total_usage: f32 = cpus.iter().map(|cpu| cpu.cpu_usage()).sum();
        let average_usage = total_usage / cpus.len() as f32;
        format!("{average_usage:.2}")
    }

    /// 获取总内存大小
    fn get_total_memory(sys: &System) -> String {
        let total_memory_gb = sys.total_memory() as f64 / 1024.0 / 1024.0 / 1024.0;
        format!("{total_memory_gb:.2}")
    }

    /// 获取内存使用率
    fn get_memory_usage(sys: &System) -> String {
        let total_memory = sys.total_memory();
        let used_memory = sys.used_memory();

        if total_memory > 0 {
            let usage_percent = (used_memory as f64 / total_memory as f64) * 100.0;
            format!("{usage_percent:.2}")
        } else {
            "0.00".to_string()
        }
    }

    /// 获取磁盘总大小（使用fs2库，根据当前目录自动获取根目录）
    fn get_disk_size() -> io::Result<String> {
        let mount_point = Self::get_root_mount_point()?;

        match statvfs(&mount_point) {
            Ok(stat) => {
                let total_space = stat.total_space();
                let total_space_gb = total_space as f64 / 1024.0 / 1024.0 / 1024.0;

                if total_space_gb >= 1.0 {
                    Ok(format!("{total_space_gb:.0}GB"))
                } else {
                    let total_space_mb = total_space as f64 / 1024.0 / 1024.0;
                    Ok(format!("{total_space_mb:.0}MB"))
                }
            }
            Err(e) => {
                tracing::warn!(
                    web = false,
                    "无法获取磁盘大小信息，挂载点: {}, 错误: {}",
                    mount_point,
                    e
                );
                Ok("0GB".to_string())
            }
        }
    }

    /// 获取磁盘使用率（使用fs2库，根据当前目录自动获取根目录）
    fn get_disk_usage() -> io::Result<String> {
        let mount_point = Self::get_root_mount_point()?;

        match statvfs(&mount_point) {
            Ok(stat) => {
                let total_space = stat.total_space();
                let available_space = stat.available_space();

                if total_space > 0 {
                    let used_space = total_space - available_space;
                    let usage_percent = (used_space as f64 / total_space as f64) * 100.0;
                    Ok(format!("{usage_percent:.2}"))
                } else {
                    Ok("0.00".to_string())
                }
            }
            Err(e) => {
                tracing::warn!(
                    web = false,
                    "无法获取磁盘使用率信息，挂载点: {}, 错误: {}",
                    mount_point,
                    e
                );
                Ok("0.00".to_string())
            }
        }
    }

    /// 获取网络使用情况
    fn get_network_usage() -> NetworkUsage {
        let networks = Networks::new_with_refreshed_list();

        // 第一次读取
        let mut total_received_1 = 0u64;
        let mut total_transmitted_1 = 0u64;

        for (interface_name, network) in &networks {
            // 跳过回环接口
            if interface_name.contains("lo") || interface_name.contains("Loopback") {
                continue;
            }
            total_received_1 += network.total_received();
            total_transmitted_1 += network.total_transmitted();
        }

        // 等待较短时间
        thread::sleep(Duration::from_millis(100));

        // 刷新网络信息
        let networks = Networks::new_with_refreshed_list();

        // 第二次读取
        let mut total_received_2 = 0u64;
        let mut total_transmitted_2 = 0u64;

        for (interface_name, network) in &networks {
            // 跳过回环接口
            if interface_name.contains("lo") || interface_name.contains("Loopback") {
                continue;
            }
            total_received_2 += network.total_received();
            total_transmitted_2 += network.total_transmitted();
        }

        // 计算速率 (bytes/100ms to Kb/s)
        let received_diff = total_received_2.saturating_sub(total_received_1);
        let transmitted_diff = total_transmitted_2.saturating_sub(total_transmitted_1);

        // 转换为每秒速率，然后转换为Kb/s
        let down_kbps = (received_diff as f64 * 10.0 * 8.0) / 1024.0; // *10 因为是100ms的数据
        let up_kbps = (transmitted_diff as f64 * 10.0 * 8.0) / 1024.0;

        NetworkUsage {
            up: format!("{up_kbps:.2}"),
            down: format!("{down_kbps:.2}"),
        }
    }

    /// 获取操作系统信息
    fn get_os_info() -> String {
        let os_name = System::name().unwrap_or_else(|| "Unknown".to_string());
        let os_version = System::os_version().unwrap_or_else(|| "Unknown".to_string());
        let kernel_version = System::kernel_version().unwrap_or_else(|| "Unknown".to_string());

        // 根据操作系统类型格式化信息
        if os_name.to_lowercase().contains("windows") {
            format!("{os_name} {os_version}")
        } else if os_name.to_lowercase().contains("macos")
            || os_name.to_lowercase().contains("darwin")
        {
            format!("macOS {os_version}")
        } else {
            // Linux 和其他 Unix-like 系统
            if !os_version.is_empty() && os_version != "Unknown" {
                format!("{os_name} {os_version}")
            } else {
                format!("{os_name} (Kernel {kernel_version})")
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_system_info_manager() {
        match SystemInfoManager::new() {
            Ok(mut manager) => {
                println!("系统信息管理器创建成功");

                // 测试获取静态信息
                let static_info = manager.get_static_info();
                println!("静态信息:");
                println!("  CPU核心数: {}", static_info.cpu_count);
                println!("  内存总量: {}GB", static_info.total_memory);
                println!("  磁盘总量: {}", static_info.disk_size);
                println!("  操作系统: {}", static_info.os_info);

                // 测试获取动态信息
                match manager.get_dynamic_info() {
                    Ok(dynamic_info) => {
                        println!("动态信息:");
                        println!("  CPU使用率: {}%", dynamic_info.cpu_usage);
                        println!("  内存使用率: {}%", dynamic_info.memory_usage);
                        println!("  磁盘使用率: {}%", dynamic_info.disk_usage);
                        println!("  网络上行: {} Kb/s", dynamic_info.network_usage.up);
                        println!("  网络下行: {} Kb/s", dynamic_info.network_usage.down);
                    }
                    Err(e) => {
                        println!("获取动态信息失败: {e}");
                    }
                }

                // 测试获取完整信息
                match manager.get_system_info() {
                    Ok(system_info) => {
                        println!("完整系统信息获取成功");

                        // 验证数据格式
                        assert!(!system_info.cpu.is_empty());
                        assert!(!system_info.cpu_usage.is_empty());
                        assert!(!system_info.memory.is_empty());
                        assert!(!system_info.memory_usage.is_empty());
                        assert!(!system_info.disk.is_empty());
                        assert!(!system_info.disk_usage.is_empty());
                        assert!(!system_info.network_usage.up.is_empty());
                        assert!(!system_info.network_usage.down.is_empty());
                        assert!(!system_info.os_info.is_empty());
                    }
                    Err(e) => {
                        println!("获取完整系统信息失败: {e}");
                    }
                }
            }
            Err(e) => {
                println!("系统信息管理器创建失败: {e}");
            }
        }
    }

    #[test]
    fn test_system_info_creation() {
        // 测试系统信息管理器
        match SystemInfoManager::new() {
            Ok(mut manager) => {
                match manager.get_system_info() {
                    Ok(info) => {
                        println!("系统信息获取成功:");
                        println!("  CPU核心数: {}", info.cpu);
                        println!("  CPU使用率: {}%", info.cpu_usage);
                        println!("  内存总量: {}GB", info.memory);
                        println!("  内存使用率: {}%", info.memory_usage);
                        println!("  磁盘总量: {}", info.disk);
                        println!("  磁盘使用率: {}%", info.disk_usage);
                        println!("  网络上行: {} Kb/s", info.network_usage.up);
                        println!("  网络下行: {} Kb/s", info.network_usage.down);
                        println!("  操作系统: {}", info.os_info);

                        // 验证数据格式
                        assert!(!info.cpu.is_empty());
                        assert!(!info.cpu_usage.is_empty());
                        assert!(!info.memory.is_empty());
                        assert!(!info.memory_usage.is_empty());
                        assert!(!info.disk.is_empty());
                        assert!(!info.disk_usage.is_empty());
                        assert!(!info.network_usage.up.is_empty());
                        assert!(!info.network_usage.down.is_empty());
                        assert!(!info.os_info.is_empty());
                    }
                    Err(e) => {
                        println!("系统信息获取失败: {e}");
                    }
                }
            }
            Err(e) => {
                println!("系统信息管理器创建失败: {e}");
                // 在某些受限环境中可能会失败，但不应该panic
            }
        }
    }

    #[test]
    fn test_network_usage_struct() {
        let network = NetworkUsage {
            up: "100.5".to_string(),
            down: "200.7".to_string(),
        };
        assert_eq!(network.up, "100.5");
        assert_eq!(network.down, "200.7");
    }

    #[test]
    fn test_root_mount_point_detection() {
        // 测试根目录检测功能
        match SystemInfoManager::get_root_mount_point() {
            Ok(mount_point) => {
                println!("检测到的根挂载点: {mount_point}");
                assert!(!mount_point.is_empty(), "挂载点不应为空");

                // 验证挂载点格式
                if cfg!(windows) {
                    // Windows系统应该返回驱动器盘符格式 (如: C:\)
                    assert!(
                        mount_point.len() >= 2 && mount_point.contains(":\\"),
                        "Windows挂载点格式应为驱动器盘符"
                    );
                } else {
                    // Unix-like系统应该返回有效路径
                    assert!(mount_point.starts_with("/"), "Unix-like系统挂载点应以/开头");
                }

                // 尝试访问该挂载点
                if let Ok(metadata) = std::fs::metadata(&mount_point) {
                    assert!(metadata.is_dir(), "挂载点应该是一个目录");
                    println!("  ✅ 挂载点可访问且为目录");
                }

                // 测试fs2能否正常访问该挂载点
                if let Ok(stat) = fs2::statvfs(&mount_point) {
                    let total_gb = stat.total_space() as f64 / 1024.0 / 1024.0 / 1024.0;
                    println!("  📊 磁盘总空间: {total_gb:.2}GB");
                    assert!(total_gb > 0.0, "磁盘总空间应大于0");
                } else {
                    println!("  ⚠️ 无法通过fs2访问挂载点，但这可能是正常的");
                }
            }
            Err(e) => {
                println!("根目录检测失败: {e}");
                // 在某些受限环境中可能会失败，但不应该panic
            }
        }
    }

    #[test]
    fn test_current_directory_context() {
        // 测试在不同目录上下文中的行为
        let original_dir = std::env::current_dir().unwrap();
        println!("原始工作目录: {original_dir:?}");

        // 测试当前目录
        if let Ok(mount_point) = SystemInfoManager::get_root_mount_point() {
            println!("当前目录的根挂载点: {mount_point}");
        }

        // 如果可能，尝试切换到不同的目录进行测试
        if cfg!(unix) {
            // 尝试切换到/tmp目录
            if std::env::set_current_dir("/tmp").is_ok() {
                if let Ok(tmp_mount_point) = SystemInfoManager::get_root_mount_point() {
                    println!("/tmp目录的根挂载点: {tmp_mount_point}");
                }
                // 恢复原始目录
                let _ = std::env::set_current_dir(&original_dir);
            }
        }
    }

    #[test]
    fn test_system_info_fields() {
        // 测试各个字段的数据类型和格式
        if let Ok(mut manager) = SystemInfoManager::new()
            && let Ok(info) = manager.get_system_info()
        {
            // CPU核心数应该是数字
            assert!(info.cpu.parse::<u32>().is_ok(), "CPU核心数应该是有效数字");

            // CPU使用率应该是浮点数
            assert!(
                info.cpu_usage.parse::<f64>().is_ok(),
                "CPU使用率应该是有效浮点数"
            );

            // 内存大小应该是浮点数
            assert!(
                info.memory.parse::<f64>().is_ok(),
                "内存大小应该是有效浮点数"
            );

            // 内存使用率应该是浮点数
            assert!(
                info.memory_usage.parse::<f64>().is_ok(),
                "内存使用率应该是有效浮点数"
            );

            // 磁盘使用率应该是浮点数
            assert!(
                info.disk_usage.parse::<f64>().is_ok(),
                "磁盘使用率应该是有效浮点数"
            );

            // 网络速率应该是浮点数
            assert!(
                info.network_usage.up.parse::<f64>().is_ok(),
                "网络上行速率应该是有效浮点数"
            );
            assert!(
                info.network_usage.down.parse::<f64>().is_ok(),
                "网络下行速率应该是有效浮点数"
            );
        }
    }

    #[test]
    fn test_cross_platform_compatibility() {
        // 测试跨平台兼容性
        let result = SystemInfoManager::new();

        // 在所有支持的平台上都应该能成功创建管理器
        match result {
            Ok(mut manager) => {
                match manager.get_system_info() {
                    Ok(info) => {
                        // 验证关键信息不为空
                        assert!(!info.os_info.is_empty(), "操作系统信息不应为空");
                        assert!(!info.cpu.is_empty(), "CPU信息不应为空");

                        // 验证数值范围合理
                        if let Ok(cpu_usage) = info.cpu_usage.parse::<f64>() {
                            assert!(
                                (0.0..=100.0).contains(&cpu_usage),
                                "CPU使用率应该在0-100之间"
                            );
                        }

                        if let Ok(memory_usage) = info.memory_usage.parse::<f64>() {
                            assert!(
                                (0.0..=100.0).contains(&memory_usage),
                                "内存使用率应该在0-100之间"
                            );
                        }

                        if let Ok(disk_usage) = info.disk_usage.parse::<f64>() {
                            assert!(
                                (0.0..=100.0).contains(&disk_usage),
                                "磁盘使用率应该在0-100之间"
                            );
                        }
                    }
                    Err(e) => {
                        println!("跨平台测试警告: {e}");
                        // 在某些受限环境中可能会失败，记录但不中断测试
                    }
                }
            }
            Err(e) => {
                println!("跨平台测试警告: {e}");
                // 在某些受限环境中可能会失败，记录但不中断测试
            }
        }
    }
}
