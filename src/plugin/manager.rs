use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tokio::time::timeout;

use crate::plugin::{
    Plugin, PluginConfig, Plugin<PERSON>ontext, PluginError, PluginStatus,
    traits::{PluginLifecycleCallback, SystemEvent},
};

/// 插件实例信息
#[derive(Debug)]
struct PluginInstance {
    /// 插件实例
    plugin: Box<dyn Plugin>,
    /// 插件配置
    config: PluginConfig,
    /// 插件上下文
    context: PluginContext,
    /// 启动时间
    started_at: Option<Instant>,
    /// 重启次数
    restart_count: u32,
    /// 最后错误
    last_error: Option<PluginError>,
}

/// 插件管理器
///
/// 负责管理所有插件的生命周期
pub struct PluginManager {
    /// 插件实例集合
    plugins: HashMap<String, PluginInstance>,
    /// 生命周期回调
    lifecycle_callbacks: Vec<Arc<dyn PluginLifecycleCallback>>,
    /// 事件发送器
    event_sender: tokio::sync::mpsc::UnboundedSender<SystemEvent>,
    /// 事件接收器
    event_receiver: Arc<RwLock<Option<tokio::sync::mpsc::UnboundedReceiver<SystemEvent>>>>,
}

impl std::fmt::Debug for PluginManager {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("PluginManager")
            .field("plugins", &self.plugins.keys().collect::<Vec<_>>())
            .field("lifecycle_callbacks_count", &self.lifecycle_callbacks.len())
            .finish()
    }
}

impl Default for PluginManager {
    fn default() -> Self {
        Self::new()
    }
}

impl PluginManager {
    /// 创建新的插件管理器
    pub fn new() -> Self {
        let (event_sender, event_receiver) = tokio::sync::mpsc::unbounded_channel();

        Self {
            plugins: HashMap::new(),
            lifecycle_callbacks: Vec::new(),
            event_sender,
            event_receiver: Arc::new(RwLock::new(Some(event_receiver))),
        }
    }

    /// 添加插件
    pub async fn add_plugin(
        &mut self,
        name: String,
        mut plugin: Box<dyn Plugin>,
        config: PluginConfig,
    ) -> Result<(), PluginError> {
        info!("正在加载插件: {}", name);

        // 验证配置
        config.validate().map_err(PluginError::ConfigurationError)?;

        // 执行生命周期回调
        for callback in &self.lifecycle_callbacks {
            callback.before_load(&name).await?;
        }

        // 创建插件上下文
        let mut context = PluginContext::new(name.clone());
        context.event_sender = self.event_sender.clone();

        // 设置插件配置到上下文
        for (key, value) in &config.config {
            match context.set_config(key, value.clone()).await {
                Ok(()) => {}
                Err(e) => return Err(PluginError::ConfigurationError(e.to_string())),
            }
        }

        // 初始化插件
        let init_timeout = Duration::from_secs(30);
        timeout(init_timeout, plugin.initialize(context.clone()))
            .await
            .map_err(|_| PluginError::Timeout("插件初始化超时".to_string()))??;

        // 创建插件实例
        let instance = PluginInstance {
            plugin,
            config,
            context,
            started_at: None,
            restart_count: 0,
            last_error: None,
        };

        // 添加到管理器
        self.plugins.insert(name.clone(), instance);

        // 执行生命周期回调
        for callback in &self.lifecycle_callbacks {
            callback.after_load(&name).await?;
        }

        // 发送事件
        let _ = self
            .event_sender
            .send(SystemEvent::PluginLoaded { name: name.clone() });

        info!("插件 {} 加载成功", name);
        Ok(())
    }

    /// 启动插件
    pub async fn start_plugin(&mut self, name: &str) -> Result<(), PluginError> {
        let instance = self
            .plugins
            .get_mut(name)
            .ok_or_else(|| PluginError::Internal(format!("插件未找到: {name}")))?;

        if matches!(instance.plugin.status(), PluginStatus::Running) {
            return Ok(());
        }

        info!("正在启动插件: {}", name);

        // 执行生命周期回调
        for callback in &self.lifecycle_callbacks {
            callback.before_start(name).await?;
        }

        // 启动插件
        let start_timeout = Duration::from_secs(60);
        let result = timeout(start_timeout, instance.plugin.start())
            .await
            .map_err(|_| PluginError::Timeout("插件启动超时".to_string()));

        match result {
            Ok(Ok(())) => {
                instance.started_at = Some(Instant::now());
                instance.last_error = None;

                // 执行生命周期回调
                for callback in &self.lifecycle_callbacks {
                    callback.after_start(name).await?;
                }

                info!("插件 {} 启动成功", name);
                Ok(())
            }
            Ok(Err(e)) => {
                instance.last_error = Some(e.clone());
                error!("插件 {} 启动失败: {}", name, e);
                Err(e)
            }
            Err(e) => {
                instance.last_error = Some(e.clone());
                error!("插件 {} 启动超时", name);
                Err(e)
            }
        }
    }

    /// 停止插件
    pub async fn stop_plugin(&mut self, name: &str) -> Result<(), PluginError> {
        let instance = self
            .plugins
            .get_mut(name)
            .ok_or_else(|| PluginError::Internal(format!("插件未找到: {name}")))?;

        if matches!(instance.plugin.status(), PluginStatus::Stopped) {
            return Ok(());
        }

        info!("正在停止插件: {}", name);

        // 执行生命周期回调
        for callback in &self.lifecycle_callbacks {
            callback.before_stop(name).await?;
        }

        // 停止插件
        let stop_timeout = Duration::from_secs(30);
        let result = timeout(stop_timeout, instance.plugin.stop())
            .await
            .map_err(|_| PluginError::Timeout("插件停止超时".to_string()));

        match result {
            Ok(Ok(())) => {
                instance.started_at = None;
                instance.last_error = None;

                // 执行生命周期回调
                for callback in &self.lifecycle_callbacks {
                    callback.after_stop(name).await?;
                }

                info!("插件 {} 停止成功", name);
                Ok(())
            }
            Ok(Err(e)) => {
                instance.last_error = Some(e.clone());
                error!("插件 {} 停止失败: {}", name, e);
                Err(e)
            }
            Err(e) => {
                instance.last_error = Some(e.clone());
                error!("插件 {} 停止超时", name);
                Err(e)
            }
        }
    }

    /// 移除插件
    pub async fn remove_plugin(&mut self, name: &str) -> Result<(), PluginError> {
        info!("正在卸载插件: {}", name);

        // 先停止插件
        if let Err(e) = self.stop_plugin(name).await {
            warn!("停止插件 {} 时发生错误: {}", name, e);
        }

        // 执行生命周期回调
        for callback in &self.lifecycle_callbacks {
            callback.before_unload(name).await?;
        }

        // 移除插件
        self.plugins.remove(name);

        // 执行生命周期回调
        for callback in &self.lifecycle_callbacks {
            callback.after_unload(name).await?;
        }

        // 发送事件
        let _ = self.event_sender.send(SystemEvent::PluginUnloaded {
            name: name.to_string(),
        });

        info!("插件 {} 卸载成功", name);
        Ok(())
    }

    /// 启动所有插件
    pub async fn start_all_plugins(&mut self) -> Result<(), PluginError> {
        info!("正在启动所有插件...");

        // 按优先级排序
        let mut plugin_names: Vec<_> = self
            .plugins
            .iter()
            .map(|(name, instance)| (name.clone(), instance.config.priority))
            .collect();
        plugin_names.sort_by_key(|(_, priority)| *priority);

        // 启动插件
        for (name, _) in plugin_names {
            if let Err(e) = self.start_plugin(&name).await {
                error!("启动插件 {} 失败: {}", name, e);

                // 发送错误事件
                let _ = self.event_sender.send(SystemEvent::Error {
                    plugin_name: Some(name.clone()),
                    error: e.to_string(),
                });
            }
        }

        info!("所有插件启动完成");
        Ok(())
    }

    /// 停止所有插件
    pub async fn stop_all_plugins(&mut self) -> Result<(), PluginError> {
        info!("正在停止所有插件...");

        // 按优先级逆序停止
        let mut plugin_names: Vec<_> = self
            .plugins
            .iter()
            .map(|(name, instance)| (name.clone(), instance.config.priority))
            .collect();
        plugin_names.sort_by_key(|(_, priority)| -priority);

        // 停止插件
        for (name, _) in plugin_names {
            if let Err(e) = self.stop_plugin(&name).await {
                error!("停止插件 {} 失败: {}", name, e);
            }
        }

        info!("所有插件停止完成");
        Ok(())
    }

    /// 重启插件
    pub async fn restart_plugin(&mut self, name: &str) -> Result<(), PluginError> {
        info!("正在重启插件: {}", name);

        let should_restart = {
            let instance = self
                .plugins
                .get(name)
                .ok_or_else(|| PluginError::Internal(format!("插件未找到: {name}")))?;

            instance.config.auto_restart && instance.restart_count < instance.config.max_restarts
        };

        if !should_restart {
            return Err(PluginError::Internal(
                "插件不允许重启或已达到最大重启次数".to_string(),
            ));
        }

        // 停止插件
        self.stop_plugin(name).await?;

        // 等待重启延迟
        let delay = {
            let instance = self.plugins.get(name).unwrap();
            instance.config.restart_delay
        };

        if delay > 0 {
            tokio::time::sleep(Duration::from_secs(delay)).await;
        }

        // 增加重启计数
        if let Some(instance) = self.plugins.get_mut(name) {
            instance.restart_count += 1;
        }

        // 启动插件
        self.start_plugin(name).await?;

        info!("插件 {} 重启成功", name);
        Ok(())
    }

    /// 获取插件状态
    pub fn get_plugin_status(&self, name: &str) -> Option<PluginStatus> {
        self.plugins
            .get(name)
            .map(|instance| instance.plugin.status())
    }

    /// 获取所有插件状态
    pub fn get_all_plugin_status(&self) -> HashMap<String, PluginStatus> {
        self.plugins
            .iter()
            .map(|(name, instance)| (name.clone(), instance.plugin.status()))
            .collect()
    }

    /// 获取插件信息
    pub fn get_plugin_info(&self, name: &str) -> Option<PluginInfo> {
        self.plugins.get(name).map(|instance| PluginInfo {
            name: instance.plugin.name().to_string(),
            version: instance.plugin.version().to_string(),
            description: instance.plugin.description().to_string(),
            author: instance.plugin.author().to_string(),
            status: instance.plugin.status(),
            started_at: instance.started_at,
            restart_count: instance.restart_count,
            last_error: instance.last_error.clone(),
            config: instance.config.clone(),
            context: instance.context.clone(),
        })
    }

    /// 添加生命周期回调
    pub fn add_lifecycle_callback(&mut self, callback: Arc<dyn PluginLifecycleCallback>) {
        self.lifecycle_callbacks.push(callback);
    }

    /// 健康检查
    pub async fn health_check(
        &self,
    ) -> HashMap<String, Result<crate::plugin::traits::HealthStatus, PluginError>> {
        let mut results = HashMap::new();

        for (name, instance) in &self.plugins {
            let result = instance.plugin.health_check().await;
            results.insert(name.clone(), result);
        }

        results
    }

    /// 获取事件接收器
    pub async fn take_event_receiver(
        &self,
    ) -> Option<tokio::sync::mpsc::UnboundedReceiver<SystemEvent>> {
        self.event_receiver.write().await.take()
    }
}

/// 插件信息
#[derive(Debug, Clone)]
pub struct PluginInfo {
    pub name: String,
    pub version: String,
    pub description: String,
    pub author: String,
    pub status: PluginStatus,
    pub started_at: Option<Instant>,
    pub restart_count: u32,
    pub last_error: Option<PluginError>,
    pub config: PluginConfig,
    pub context: PluginContext,
}

#[cfg(test)]
mod tests {
    use super::*;
    use async_trait::async_trait;
    use std::sync::atomic::{AtomicBool, Ordering};

    #[derive(Debug)]
    struct TestPlugin {
        name: String,
        status: Arc<RwLock<PluginStatus>>,
        initialized: Arc<AtomicBool>,
        started: Arc<AtomicBool>,
    }

    impl TestPlugin {
        fn new(name: String) -> Self {
            Self {
                name,
                status: Arc::new(RwLock::new(PluginStatus::Uninitialized)),
                initialized: Arc::new(AtomicBool::new(false)),
                started: Arc::new(AtomicBool::new(false)),
            }
        }
    }

    #[async_trait]
    impl Plugin for TestPlugin {
        fn name(&self) -> &str {
            &self.name
        }

        fn version(&self) -> &str {
            "1.0.0"
        }

        fn description(&self) -> &str {
            "Test plugin"
        }

        async fn initialize(&mut self, _context: PluginContext) -> Result<(), PluginError> {
            self.initialized.store(true, Ordering::SeqCst);
            *self.status.write().await = PluginStatus::Initialized;
            Ok(())
        }

        async fn start(&mut self) -> Result<(), PluginError> {
            self.started.store(true, Ordering::SeqCst);
            *self.status.write().await = PluginStatus::Running;
            Ok(())
        }

        async fn stop(&mut self) -> Result<(), PluginError> {
            self.started.store(false, Ordering::SeqCst);
            *self.status.write().await = PluginStatus::Stopped;
            Ok(())
        }

        fn status(&self) -> PluginStatus {
            // 在测试中简化状态获取
            if self.started.load(Ordering::SeqCst) {
                PluginStatus::Running
            } else if self.initialized.load(Ordering::SeqCst) {
                PluginStatus::Stopped
            } else {
                PluginStatus::Uninitialized
            }
        }
    }

    #[tokio::test]
    async fn test_plugin_manager() {
        let mut manager = PluginManager::new();
        let plugin = Box::new(TestPlugin::new("test".to_string()));
        let config = PluginConfig::new("test".to_string());

        // 添加插件
        manager
            .add_plugin("test".to_string(), plugin, config)
            .await
            .unwrap();

        // 启动插件
        manager.start_plugin("test").await.unwrap();
        assert_eq!(
            manager.get_plugin_status("test"),
            Some(PluginStatus::Running)
        );

        // 停止插件
        manager.stop_plugin("test").await.unwrap();
        assert_eq!(
            manager.get_plugin_status("test"),
            Some(PluginStatus::Stopped)
        );

        // 移除插件
        manager.remove_plugin("test").await.unwrap();
        assert_eq!(manager.get_plugin_status("test"), None);
    }
}
