use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;

/// 插件信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginInfo {
    /// 插件名称
    pub name: String,
    /// 插件版本
    pub version: String,
    /// 插件描述
    pub description: String,
    /// 插件作者
    pub author: String,
    /// 插件主页
    pub homepage: Option<String>,
    /// 插件许可证
    pub license: Option<String>,
    /// 插件文件路径
    pub path: PathBuf,
    /// 插件类型
    pub plugin_type: PluginType,
    /// 插件依赖
    pub dependencies: Vec<String>,
    /// 支持的API版本
    pub api_version: String,
    /// 插件标签
    pub tags: Vec<String>,
    /// 插件元数据
    pub metadata: HashMap<String, serde_json::Value>,
    /// 是否已验证
    pub verified: bool,
    /// 插件大小（字节）
    pub size: u64,
    /// 插件哈希值
    pub hash: Option<String>,
    /// 最后修改时间
    pub modified_time: Option<std::time::SystemTime>,
}

impl PluginInfo {
    /// 创建新的插件信息
    pub fn new(name: String, version: String, path: PathBuf) -> Self {
        Self {
            name,
            version,
            description: String::new(),
            author: String::new(),
            homepage: None,
            license: None,
            path,
            plugin_type: PluginType::Dynamic,
            dependencies: Vec::new(),
            api_version: "1.0.0".to_string(),
            tags: Vec::new(),
            metadata: HashMap::new(),
            verified: false,
            size: 0,
            hash: None,
            modified_time: None,
        }
    }

    /// 设置描述
    pub fn with_description(mut self, description: String) -> Self {
        self.description = description;
        self
    }

    /// 设置作者
    pub fn with_author(mut self, author: String) -> Self {
        self.author = author;
        self
    }

    /// 添加依赖
    pub fn with_dependency(mut self, dependency: String) -> Self {
        self.dependencies.push(dependency);
        self
    }

    /// 添加标签
    pub fn with_tag(mut self, tag: String) -> Self {
        self.tags.push(tag);
        self
    }

    /// 设置插件类型
    pub fn with_type(mut self, plugin_type: PluginType) -> Self {
        self.plugin_type = plugin_type;
        self
    }

    /// 添加元数据
    pub fn with_metadata<T: Serialize>(
        mut self,
        key: String,
        value: T,
    ) -> Result<Self, serde_json::Error> {
        let json_value = serde_json::to_value(value)?;
        self.metadata.insert(key, json_value);
        Ok(self)
    }

    /// 验证插件信息
    pub fn validate(&self) -> Result<(), String> {
        if self.name.is_empty() {
            return Err("插件名称不能为空".to_string());
        }

        if self.version.is_empty() {
            return Err("插件版本不能为空".to_string());
        }

        if !self.path.exists() {
            return Err(format!("插件文件不存在: {:?}", self.path));
        }

        // 验证版本格式
        if !self.is_valid_version(&self.version) {
            return Err(format!("无效的版本格式: {}", self.version));
        }

        if !self.is_valid_version(&self.api_version) {
            return Err(format!("无效的API版本格式: {}", self.api_version));
        }

        Ok(())
    }

    /// 检查版本格式是否有效
    fn is_valid_version(&self, version: &str) -> bool {
        // 简单的语义版本检查 (major.minor.patch)
        let parts: Vec<&str> = version.split('.').collect();
        if parts.len() != 3 {
            return false;
        }

        for part in parts {
            if part.parse::<u32>().is_err() {
                return false;
            }
        }

        true
    }

    /// 检查是否满足依赖版本要求
    pub fn satisfies_version(&self, required_version: &str) -> bool {
        // 简化版本比较，实际应用中可以使用semver crate
        if required_version.is_empty() {
            return true;
        }

        // 支持 ">=1.0.0", "^1.0.0", "~1.0.0" 等格式
        if let Some(version) = required_version.strip_prefix(">=") {
            return self.compare_version(&self.version, version) >= 0;
        }

        if let Some(version) = required_version.strip_prefix('^') {
            return self.compatible_version(&self.version, version);
        }

        if let Some(version) = required_version.strip_prefix('~') {
            return self.patch_compatible_version(&self.version, version);
        }

        // 精确匹配
        self.version == required_version
    }

    /// 比较版本号
    fn compare_version(&self, v1: &str, v2: &str) -> i32 {
        let v1_parts: Vec<u32> = v1.split('.').filter_map(|s| s.parse().ok()).collect();
        let v2_parts: Vec<u32> = v2.split('.').filter_map(|s| s.parse().ok()).collect();

        for i in 0..3 {
            let p1 = v1_parts.get(i).unwrap_or(&0);
            let p2 = v2_parts.get(i).unwrap_or(&0);

            match p1.cmp(p2) {
                std::cmp::Ordering::Greater => return 1,
                std::cmp::Ordering::Less => return -1,
                std::cmp::Ordering::Equal => continue,
            }
        }

        0
    }

    /// 检查兼容版本 (^1.2.3 = >=1.2.3 <2.0.0)
    fn compatible_version(&self, current: &str, required: &str) -> bool {
        let current_parts: Vec<u32> = current.split('.').filter_map(|s| s.parse().ok()).collect();
        let required_parts: Vec<u32> = required.split('.').filter_map(|s| s.parse().ok()).collect();

        if current_parts.len() != 3 || required_parts.len() != 3 {
            return false;
        }

        // 主版本必须相同
        if current_parts[0] != required_parts[0] {
            return false;
        }

        // 当前版本必须 >= 要求版本
        self.compare_version(current, required) >= 0
    }

    /// 检查补丁兼容版本 (~1.2.3 = >=1.2.3 <1.3.0)
    fn patch_compatible_version(&self, current: &str, required: &str) -> bool {
        let current_parts: Vec<u32> = current.split('.').filter_map(|s| s.parse().ok()).collect();
        let required_parts: Vec<u32> = required.split('.').filter_map(|s| s.parse().ok()).collect();

        if current_parts.len() != 3 || required_parts.len() != 3 {
            return false;
        }

        // 主版本和次版本必须相同
        if current_parts[0] != required_parts[0] || current_parts[1] != required_parts[1] {
            return false;
        }

        // 当前版本必须 >= 要求版本
        self.compare_version(current, required) >= 0
    }
}

/// 插件类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum PluginType {
    /// 动态链接库
    #[serde(rename = "dynamic")]
    Dynamic,
    /// WebAssembly模块
    #[serde(rename = "wasm")]
    WebAssembly,
    /// 脚本插件
    #[serde(rename = "script")]
    Script,
    /// 内置插件
    #[serde(rename = "builtin")]
    Builtin,
}

/// 插件注册表
///
/// 管理所有可用插件的信息和元数据
#[derive(Debug)]
pub struct PluginRegistry {
    /// 插件信息映射
    plugins: HashMap<String, PluginInfo>,
    /// 按标签分组的插件
    tag_index: HashMap<String, Vec<String>>,
    /// 按类型分组的插件
    type_index: HashMap<PluginType, Vec<String>>,
    /// 依赖关系图
    dependency_graph: HashMap<String, Vec<String>>,
}

impl Default for PluginRegistry {
    fn default() -> Self {
        Self::new()
    }
}

impl PluginRegistry {
    /// 创建新的插件注册表
    pub fn new() -> Self {
        Self {
            plugins: HashMap::new(),
            tag_index: HashMap::new(),
            type_index: HashMap::new(),
            dependency_graph: HashMap::new(),
        }
    }

    /// 注册插件
    pub fn register_plugin(&mut self, info: PluginInfo) -> Result<(), String> {
        // 验证插件信息
        info.validate()?;

        let name = info.name.clone();

        // 检查是否已存在
        if self.plugins.contains_key(&name) {
            return Err(format!("插件 {name} 已存在"));
        }

        // 更新标签索引
        for tag in &info.tags {
            self.tag_index
                .entry(tag.clone())
                .or_default()
                .push(name.clone());
        }

        // 更新类型索引
        self.type_index
            .entry(info.plugin_type.clone())
            .or_default()
            .push(name.clone());

        // 更新依赖图
        self.dependency_graph
            .insert(name.clone(), info.dependencies.clone());

        // 注册插件
        self.plugins.insert(name.clone(), info);

        info!("插件 {} 注册成功", name);
        Ok(())
    }

    /// 注销插件
    pub fn unregister_plugin(&mut self, name: &str) -> Result<(), String> {
        if let Some(info) = self.plugins.remove(name) {
            // 更新标签索引
            for tag in &info.tags {
                if let Some(plugins) = self.tag_index.get_mut(tag) {
                    plugins.retain(|p| p != name);
                    if plugins.is_empty() {
                        self.tag_index.remove(tag);
                    }
                }
            }

            // 更新类型索引
            if let Some(plugins) = self.type_index.get_mut(&info.plugin_type) {
                plugins.retain(|p| p != name);
                if plugins.is_empty() {
                    self.type_index.remove(&info.plugin_type);
                }
            }

            // 更新依赖图
            self.dependency_graph.remove(name);

            info!("插件 {} 注销成功", name);
            Ok(())
        } else {
            Err(format!("插件 {name} 不存在"))
        }
    }

    /// 获取插件信息
    pub fn get_plugin(&self, name: &str) -> Option<&PluginInfo> {
        self.plugins.get(name)
    }

    /// 获取所有插件
    pub fn get_all_plugins(&self) -> Vec<&PluginInfo> {
        self.plugins.values().collect()
    }

    /// 按标签搜索插件
    pub fn find_by_tag(&self, tag: &str) -> Vec<&PluginInfo> {
        self.tag_index
            .get(tag)
            .map(|names| {
                names
                    .iter()
                    .filter_map(|name| self.plugins.get(name))
                    .collect()
            })
            .unwrap_or_default()
    }

    /// 按类型搜索插件
    pub fn find_by_type(&self, plugin_type: &PluginType) -> Vec<&PluginInfo> {
        self.type_index
            .get(plugin_type)
            .map(|names| {
                names
                    .iter()
                    .filter_map(|name| self.plugins.get(name))
                    .collect()
            })
            .unwrap_or_default()
    }

    /// 搜索插件
    pub fn search(&self, query: &str) -> Vec<&PluginInfo> {
        let query = query.to_lowercase();
        self.plugins
            .values()
            .filter(|info| {
                info.name.to_lowercase().contains(&query)
                    || info.description.to_lowercase().contains(&query)
                    || info.author.to_lowercase().contains(&query)
                    || info
                        .tags
                        .iter()
                        .any(|tag| tag.to_lowercase().contains(&query))
            })
            .collect()
    }

    /// 检查依赖关系
    pub fn check_dependencies(&self, name: &str) -> Result<Vec<String>, String> {
        let info = self
            .plugins
            .get(name)
            .ok_or_else(|| format!("插件 {name} 不存在"))?;

        let mut missing_deps = Vec::new();

        for dep in &info.dependencies {
            if !self.plugins.contains_key(dep) {
                missing_deps.push(dep.clone());
            }
        }

        if missing_deps.is_empty() {
            Ok(Vec::new())
        } else {
            Err(format!("缺少依赖: {}", missing_deps.join(", ")))
        }
    }

    /// 获取依赖顺序
    pub fn get_dependency_order(&self, names: &[String]) -> Result<Vec<String>, String> {
        let mut visited = std::collections::HashSet::new();
        let mut temp_visited = std::collections::HashSet::new();
        let mut result = Vec::new();

        for name in names {
            if !visited.contains(name) {
                self.dfs_visit(name, &mut visited, &mut temp_visited, &mut result)?;
            }
        }

        Ok(result)
    }

    /// 深度优先搜索访问
    fn dfs_visit(
        &self,
        name: &str,
        visited: &mut std::collections::HashSet<String>,
        temp_visited: &mut std::collections::HashSet<String>,
        result: &mut Vec<String>,
    ) -> Result<(), String> {
        if temp_visited.contains(name) {
            return Err(format!("检测到循环依赖: {name}"));
        }

        if visited.contains(name) {
            return Ok(());
        }

        temp_visited.insert(name.to_string());

        if let Some(deps) = self.dependency_graph.get(name) {
            for dep in deps {
                if self.plugins.contains_key(dep) {
                    self.dfs_visit(dep, visited, temp_visited, result)?;
                } else {
                    return Err(format!("依赖 {dep} 不存在"));
                }
            }
        }

        temp_visited.remove(name);
        visited.insert(name.to_string());
        result.push(name.to_string());

        Ok(())
    }

    /// 获取统计信息
    pub fn get_statistics(&self) -> RegistryStatistics {
        let mut type_counts = HashMap::new();
        let mut total_size = 0;
        let mut verified_count = 0;

        for info in self.plugins.values() {
            *type_counts.entry(info.plugin_type.clone()).or_insert(0) += 1;
            total_size += info.size;
            if info.verified {
                verified_count += 1;
            }
        }

        RegistryStatistics {
            total_plugins: self.plugins.len(),
            type_counts,
            total_size,
            verified_count,
            tag_count: self.tag_index.len(),
        }
    }
}

/// 注册表统计信息
#[derive(Debug, Clone)]
pub struct RegistryStatistics {
    pub total_plugins: usize,
    pub type_counts: HashMap<PluginType, usize>,
    pub total_size: u64,
    pub verified_count: usize,
    pub tag_count: usize,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_plugin_info_creation() {
        let info = PluginInfo::new(
            "test_plugin".to_string(),
            "1.0.0".to_string(),
            PathBuf::from("/tmp/test.so"),
        )
        .with_description("Test plugin".to_string())
        .with_author("Test Author".to_string())
        .with_dependency("dep1".to_string())
        .with_tag("test".to_string());

        assert_eq!(info.name, "test_plugin");
        assert_eq!(info.version, "1.0.0");
        assert_eq!(info.description, "Test plugin");
        assert_eq!(info.author, "Test Author");
        assert!(info.dependencies.contains(&"dep1".to_string()));
        assert!(info.tags.contains(&"test".to_string()));
    }

    #[test]
    fn test_version_comparison() {
        let info = PluginInfo::new("test".to_string(), "1.2.3".to_string(), PathBuf::new());

        assert!(info.satisfies_version("1.2.3"));
        assert!(info.satisfies_version(">=1.2.0"));
        assert!(info.satisfies_version("^1.2.0"));
        assert!(info.satisfies_version("~1.2.0"));
        assert!(!info.satisfies_version(">=2.0.0"));
        assert!(!info.satisfies_version("^2.0.0"));
    }

    #[test]
    fn test_plugin_registry() {
        let _registry = PluginRegistry::new();

        let _info = PluginInfo::new(
            "test_plugin".to_string(),
            "1.0.0".to_string(),
            PathBuf::from("test.so"),
        )
        .with_tag("test".to_string());

        // 注册插件会失败，因为文件不存在
        // 在实际使用中，应该使用存在的文件路径
        // assert!(registry.register_plugin(info).is_ok());
    }

    #[test]
    fn test_dependency_order() {
        let mut registry = PluginRegistry::new();

        // 模拟依赖关系: A -> B -> C
        registry
            .dependency_graph
            .insert("A".to_string(), vec!["B".to_string()]);
        registry
            .dependency_graph
            .insert("B".to_string(), vec!["C".to_string()]);
        registry.dependency_graph.insert("C".to_string(), vec![]);

        // 添加插件到注册表
        registry.plugins.insert(
            "A".to_string(),
            PluginInfo::new("A".to_string(), "1.0.0".to_string(), PathBuf::new()),
        );
        registry.plugins.insert(
            "B".to_string(),
            PluginInfo::new("B".to_string(), "1.0.0".to_string(), PathBuf::new()),
        );
        registry.plugins.insert(
            "C".to_string(),
            PluginInfo::new("C".to_string(), "1.0.0".to_string(), PathBuf::new()),
        );

        let order = registry.get_dependency_order(&["A".to_string()]).unwrap();
        assert_eq!(order, vec!["C", "B", "A"]);
    }
}
