use quant_common::{Result, qerror};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;
use std::sync::Arc;
use tokio::sync::RwLock;

pub mod config;
pub mod loader;
pub mod manager;
pub mod plugins;
pub mod registry;
pub mod traits;

pub use config::PluginConfig;
pub use loader::PluginLoader;
pub use manager::PluginManager;
pub use registry::PluginRegistry;
pub use traits::{Plugin, PluginContext, PluginError, PluginStatus};

/// 插件系统的核心结构
///
/// 这个结构体管理所有插件的生命周期，包括加载、启动、停止和卸载
#[derive(Debug)]
pub struct PluginSystem {
    /// 插件管理器
    manager: Arc<RwLock<PluginManager>>,
    /// 插件注册表
    registry: Arc<RwLock<PluginRegistry>>,
    /// 插件加载器
    loader: Arc<PluginLoader>,
    /// 系统配置
    config: PluginSystemConfig,
}

impl PluginSystem {
    /// 创建新的插件系统
    pub fn new(config: PluginSystemConfig) -> Self {
        let manager = Arc::new(RwLock::new(PluginManager::new()));
        let registry = Arc::new(RwLock::new(PluginRegistry::new()));
        let loader = Arc::new(PluginLoader::new());

        Self {
            manager,
            registry,
            loader,
            config,
        }
    }

    /// 从配置文件初始化插件系统
    pub async fn from_config(config_path: &str) -> Result<Self> {
        let config = PluginSystemConfig::load_from_file(config_path).await?;
        Ok(Self::new(config))
    }

    /// 启动插件系统
    ///
    /// 这会自动加载配置中启用的所有插件
    pub async fn start(&self) -> Result<()> {
        info!("启动插件系统...");

        // 扫描并注册可用插件
        self.scan_and_register_plugins().await?;

        // 根据配置加载并启动插件
        self.load_configured_plugins().await?;

        info!("插件系统启动完成");
        Ok(())
    }

    /// 停止插件系统
    pub async fn stop(&self) -> Result<()> {
        info!("正在停止插件系统...");

        let mut manager = self.manager.write().await;
        manager
            .stop_all_plugins()
            .await
            .map_err(|e| qerror!("停止插件失败: {}", e))?;

        info!("插件系统已停止");
        Ok(())
    }

    /// 扫描并注册所有可用的插件
    async fn scan_and_register_plugins(&self) -> Result<()> {
        info!("注册内置插件...");

        // 这里可以注册所有内置插件
        // 例如：self.loader.register_builtin_plugin(...);

        info!("内置插件注册完成");
        Ok(())
    }

    /// 根据配置加载插件
    async fn load_configured_plugins(&self) -> Result<()> {
        let registry = self.registry.read().await;
        let mut manager = self.manager.write().await;

        for plugin_config in &self.config.plugins {
            if plugin_config.enabled {
                if let Some(plugin_info) = registry.get_plugin(&plugin_config.name) {
                    let plugin = self
                        .loader
                        .load_plugin(plugin_info)
                        .await
                        .map_err(|e| qerror!("加载插件失败: {}", e))?;
                    manager
                        .add_plugin(plugin_config.name.clone(), plugin, plugin_config.clone())
                        .await
                        .map_err(|e| qerror!("添加插件失败: {}", e))?;
                } else {
                    warn!("未找到插件: {}", plugin_config.name);
                }
            }
        }

        Ok(())
    }

    /// 动态加载插件
    pub async fn load_plugin(&self, name: &str, config: PluginConfig) -> Result<()> {
        let registry = self.registry.read().await;
        let mut manager = self.manager.write().await;

        if let Some(plugin_info) = registry.get_plugin(name) {
            let plugin = self
                .loader
                .load_plugin(plugin_info)
                .await
                .map_err(|e| qerror!("加载插件失败: {}", e))?;
            manager
                .add_plugin(name.to_string(), plugin, config)
                .await
                .map_err(|e| qerror!("添加插件失败: {}", e))?;
            Ok(())
        } else {
            Err(qerror!("插件未找到: {}", name))
        }
    }

    /// 动态卸载插件
    pub async fn unload_plugin(&self, name: &str) -> Result<()> {
        let mut manager = self.manager.write().await;
        manager
            .remove_plugin(name)
            .await
            .map_err(|e| qerror!("卸载插件失败: {}", e))
    }

    /// 获取插件状态
    pub async fn get_plugin_status(&self, name: &str) -> Option<PluginStatus> {
        let manager = self.manager.read().await;
        manager.get_plugin_status(name)
    }

    /// 获取所有插件状态
    pub async fn get_all_plugin_status(&self) -> HashMap<String, PluginStatus> {
        let manager = self.manager.read().await;
        manager.get_all_plugin_status()
    }

    /// 重新加载插件配置
    pub async fn reload_config(&mut self, config_path: &str) -> Result<()> {
        let new_config = PluginSystemConfig::load_from_file(config_path).await?;
        self.config = new_config;

        // 重新加载插件
        self.load_configured_plugins().await?;

        Ok(())
    }
}

/// 插件系统配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginSystemConfig {
    /// 插件搜索路径
    pub plugin_paths: Vec<PathBuf>,
    /// 插件配置列表
    pub plugins: Vec<PluginConfig>,
    /// 系统级配置
    pub system: SystemConfig,
}

impl Default for PluginSystemConfig {
    fn default() -> Self {
        Self {
            plugin_paths: vec![
                PathBuf::from("./plugins"),
                PathBuf::from("./target/debug"),
                PathBuf::from("./target/release"),
            ],
            plugins: Vec::new(),
            system: SystemConfig::default(),
        }
    }
}

impl PluginSystemConfig {
    /// 从文件加载配置
    pub async fn load_from_file(path: &str) -> Result<Self> {
        let content = tokio::fs::read_to_string(path)
            .await
            .map_err(|e| qerror!("无法读取配置文件 {}: {}", path, e))?;

        let config: Self = toml::from_str(&content)
            .or_else(|_| serde_json::from_str(&content))
            .map_err(|e| qerror!("无法解析配置文件: {}", e))?;

        Ok(config)
    }

    /// 保存配置到文件
    pub async fn save_to_file(&self, path: &str) -> Result<()> {
        let content = toml::to_string_pretty(self).map_err(|e| qerror!("无法序列化配置: {}", e))?;

        tokio::fs::write(path, content)
            .await
            .map_err(|e| qerror!("无法写入配置文件 {}: {}", path, e))?;

        Ok(())
    }
}

/// 系统级配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemConfig {
    /// 插件加载超时时间（秒）
    #[serde(default = "default_load_timeout")]
    pub load_timeout: u64,
    /// 插件启动超时时间（秒）
    #[serde(default = "default_start_timeout")]
    pub start_timeout: u64,
    /// 插件停止超时时间（秒）
    #[serde(default = "default_stop_timeout")]
    pub stop_timeout: u64,
    /// 启用热重载
    #[serde(default)]
    pub hot_reload: bool,
    /// 配置文件监视
    #[serde(default)]
    pub config_watch: bool,
}

impl Default for SystemConfig {
    fn default() -> Self {
        Self {
            load_timeout: default_load_timeout(),
            start_timeout: default_start_timeout(),
            stop_timeout: default_stop_timeout(),
            hot_reload: false,
            config_watch: false,
        }
    }
}

fn default_load_timeout() -> u64 {
    30
}

fn default_start_timeout() -> u64 {
    60
}

fn default_stop_timeout() -> u64 {
    30
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_plugin_system_creation() {
        let config = PluginSystemConfig::default();
        let system = PluginSystem::new(config);

        assert!(!system.config.plugin_paths.is_empty());
    }

    #[tokio::test]
    async fn test_config_serialization() {
        let config = PluginSystemConfig::default();
        let toml_str = toml::to_string(&config).unwrap();
        let deserialized: PluginSystemConfig = toml::from_str(&toml_str).unwrap();

        assert_eq!(config.plugin_paths.len(), deserialized.plugin_paths.len());
    }
}
