use std::collections::HashMap;

use crate::plugin::{
    Plugin, PluginError,
    registry::{PluginInfo, PluginType},
};

/// 插件加载器接口
pub trait PluginLoaderTrait: Send + Sync {
    /// 加载插件
    fn load_plugin(&self, info: &PluginInfo) -> Result<Box<dyn Plugin>, PluginError>;

    /// 卸载插件
    fn unload_plugin(&self, name: &str) -> Result<(), PluginError>;

    /// 检查是否支持该插件类型
    fn supports_type(&self, plugin_type: &PluginType) -> bool;
}

/// 内置插件加载器
#[derive(Debug)]
pub struct BuiltinPluginLoader {
    registry: HashMap<String, fn() -> Box<dyn Plugin>>,
}

impl Default for BuiltinPluginLoader {
    fn default() -> Self {
        Self::new()
    }
}

impl BuiltinPluginLoader {
    pub fn new() -> Self {
        Self {
            registry: HashMap::new(),
        }
    }

    /// 注册内置插件
    pub fn register_builtin(&mut self, name: String, factory: fn() -> Box<dyn Plugin>) {
        self.registry.insert(name, factory);
    }
}

impl PluginLoaderTrait for BuiltinPluginLoader {
    fn load_plugin(&self, info: &PluginInfo) -> Result<Box<dyn Plugin>, PluginError> {
        if !self.supports_type(&info.plugin_type) {
            return Err(PluginError::Internal(format!(
                "不支持的插件类型: {:?}",
                info.plugin_type
            )));
        }

        if let Some(factory) = self.registry.get(&info.name) {
            Ok(factory())
        } else {
            Err(PluginError::Internal(format!(
                "内置插件 {} 未注册",
                info.name
            )))
        }
    }

    fn unload_plugin(&self, _name: &str) -> Result<(), PluginError> {
        // 内置插件不需要特别的卸载逻辑
        Ok(())
    }

    fn supports_type(&self, plugin_type: &PluginType) -> bool {
        matches!(plugin_type, PluginType::Builtin)
    }
}

/// 插件加载器
///
/// 专注于管理内置插件
#[derive(Debug)]
pub struct PluginLoader {
    /// 内置插件加载器
    builtin_loader: BuiltinPluginLoader,
}

impl Default for PluginLoader {
    fn default() -> Self {
        Self::new()
    }
}

impl PluginLoader {
    /// 创建新的插件加载器
    pub fn new() -> Self {
        Self {
            builtin_loader: BuiltinPluginLoader::new(),
        }
    }

    /// 注册内置插件
    pub fn register_builtin_plugin(&mut self, name: String, factory: fn() -> Box<dyn Plugin>) {
        info!("内置插件 {} 注册成功", name);
        self.builtin_loader.register_builtin(name, factory);
    }

    /// 加载插件
    pub async fn load_plugin(&self, info: &PluginInfo) -> Result<Box<dyn Plugin>, PluginError> {
        info!("正在加载插件: {} ({})", info.name, info.version);

        let plugin = match info.plugin_type {
            PluginType::Builtin => self.builtin_loader.load_plugin(info)?,
            _ => {
                return Err(PluginError::Internal(format!(
                    "不支持的插件类型: {:?}",
                    info.plugin_type
                )));
            }
        };

        info!("插件 {} 加载成功", info.name);
        Ok(plugin)
    }

    /// 卸载插件
    pub async fn unload_plugin(
        &self,
        name: &str,
        plugin_type: &PluginType,
    ) -> Result<(), PluginError> {
        match plugin_type {
            PluginType::Builtin => self.builtin_loader.unload_plugin(name),
            _ => Err(PluginError::Internal(format!(
                "不支持的插件类型: {plugin_type:?}"
            ))),
        }
    }

    /// 获取已注册的内置插件列表
    pub fn get_builtin_plugins(&self) -> Vec<String> {
        self.builtin_loader.registry.keys().cloned().collect()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::plugin::traits::{PluginContext, PluginStatus};
    use async_trait::async_trait;

    #[derive(Debug)]
    struct TestPlugin {
        name: String,
    }

    impl TestPlugin {
        fn new(name: String) -> Self {
            Self { name }
        }
    }

    #[async_trait]
    impl Plugin for TestPlugin {
        fn name(&self) -> &str {
            &self.name
        }

        fn version(&self) -> &str {
            "1.0.0"
        }

        fn description(&self) -> &str {
            "Test plugin"
        }

        async fn initialize(&mut self, _context: PluginContext) -> Result<(), PluginError> {
            Ok(())
        }

        async fn start(&mut self) -> Result<(), PluginError> {
            Ok(())
        }

        async fn stop(&mut self) -> Result<(), PluginError> {
            Ok(())
        }

        fn status(&self) -> PluginStatus {
            PluginStatus::Running
        }
    }

    #[tokio::test]
    async fn test_builtin_plugin_loader() {
        let mut loader = PluginLoader::new();

        // 注册测试插件
        loader.register_builtin_plugin("test_plugin".to_string(), || {
            Box::new(TestPlugin::new("test_plugin".to_string()))
        });

        // 创建插件信息
        let info = PluginInfo::new(
            "test_plugin".to_string(),
            "1.0.0".to_string(),
            std::path::PathBuf::new(),
        )
        .with_type(PluginType::Builtin);

        // 加载插件
        let plugin = loader.load_plugin(&info).await.unwrap();
        assert_eq!(plugin.name(), "test_plugin");
    }
}
