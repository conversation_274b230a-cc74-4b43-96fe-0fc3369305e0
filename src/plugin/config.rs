use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;

/// 单个插件的配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginConfig {
    /// 插件名称
    pub name: String,
    /// 是否启用
    #[serde(default = "default_enabled")]
    pub enabled: bool,
    /// 插件版本要求
    pub version: Option<String>,
    /// 启动优先级 (数字越小优先级越高)
    #[serde(default = "default_priority")]
    pub priority: i32,
    /// 插件特定配置
    #[serde(default)]
    pub config: HashMap<String, serde_json::Value>,
    /// 环境变量
    #[serde(default)]
    pub env: HashMap<String, String>,
    /// 插件文件路径 (可选，用于动态加载)
    pub path: Option<PathBuf>,
    /// 自动重启
    #[serde(default)]
    pub auto_restart: bool,
    /// 重启延迟 (秒)
    #[serde(default = "default_restart_delay")]
    pub restart_delay: u64,
    /// 最大重启次数
    #[serde(default = "default_max_restarts")]
    pub max_restarts: u32,
    /// 插件标签
    #[serde(default)]
    pub tags: Vec<String>,
    /// 插件描述
    pub description: Option<String>,
}

impl Default for PluginConfig {
    fn default() -> Self {
        Self {
            name: String::new(),
            enabled: default_enabled(),
            version: None,
            priority: default_priority(),
            config: HashMap::new(),
            env: HashMap::new(),
            path: None,
            auto_restart: false,
            restart_delay: default_restart_delay(),
            max_restarts: default_max_restarts(),
            tags: Vec::new(),
            description: None,
        }
    }
}

impl PluginConfig {
    /// 创建新的插件配置
    pub fn new(name: String) -> Self {
        Self {
            name,
            ..Default::default()
        }
    }

    /// 设置是否启用
    pub fn enabled(mut self, enabled: bool) -> Self {
        self.enabled = enabled;
        self
    }

    /// 设置优先级
    pub fn priority(mut self, priority: i32) -> Self {
        self.priority = priority;
        self
    }

    /// 添加配置项
    pub fn with_config<T: Serialize>(
        mut self,
        key: String,
        value: T,
    ) -> Result<Self, serde_json::Error> {
        let json_value = serde_json::to_value(value)?;
        self.config.insert(key, json_value);
        Ok(self)
    }

    /// 添加环境变量
    pub fn with_env(mut self, key: String, value: String) -> Self {
        self.env.insert(key, value);
        self
    }

    /// 设置插件路径
    pub fn with_path(mut self, path: PathBuf) -> Self {
        self.path = Some(path);
        self
    }

    /// 设置自动重启
    pub fn auto_restart(mut self, auto_restart: bool) -> Self {
        self.auto_restart = auto_restart;
        self
    }

    /// 添加标签
    pub fn with_tag(mut self, tag: String) -> Self {
        self.tags.push(tag);
        self
    }

    /// 获取配置值
    pub fn get_config<T>(&self, key: &str) -> Option<T>
    where
        T: for<'de> Deserialize<'de>,
    {
        self.config
            .get(key)
            .and_then(|v| serde_json::from_value(v.clone()).ok())
    }

    /// 验证配置
    pub fn validate(&self) -> Result<(), String> {
        if self.name.is_empty() {
            return Err("插件名称不能为空".to_string());
        }

        if self.priority < 0 {
            return Err("优先级不能为负数".to_string());
        }

        if self.restart_delay > 3600 {
            return Err("重启延迟不能超过3600秒".to_string());
        }

        if self.max_restarts > 100 {
            return Err("最大重启次数不能超过100".to_string());
        }

        Ok(())
    }

    /// 合并配置
    ///
    /// 将另一个配置的值合并到当前配置中
    pub fn merge(&mut self, other: &PluginConfig) {
        if other.enabled != default_enabled() {
            self.enabled = other.enabled;
        }

        if let Some(ref version) = other.version {
            self.version = Some(version.clone());
        }

        if other.priority != default_priority() {
            self.priority = other.priority;
        }

        // 合并配置项
        for (key, value) in &other.config {
            self.config.insert(key.clone(), value.clone());
        }

        // 合并环境变量
        for (key, value) in &other.env {
            self.env.insert(key.clone(), value.clone());
        }

        if other.path.is_some() {
            self.path = other.path.clone();
        }

        if other.auto_restart {
            self.auto_restart = other.auto_restart;
        }

        if other.restart_delay != default_restart_delay() {
            self.restart_delay = other.restart_delay;
        }

        if other.max_restarts != default_max_restarts() {
            self.max_restarts = other.max_restarts;
        }

        // 合并标签
        for tag in &other.tags {
            if !self.tags.contains(tag) {
                self.tags.push(tag.clone());
            }
        }

        if other.description.is_some() {
            self.description = other.description.clone();
        }
    }
}

/// 插件组配置
///
/// 用于管理一组相关的插件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginGroupConfig {
    /// 组名称
    pub name: String,
    /// 组描述
    pub description: Option<String>,
    /// 组中的插件列表
    pub plugins: Vec<String>,
    /// 组级别的配置
    #[serde(default)]
    pub config: HashMap<String, serde_json::Value>,
    /// 是否启用整个组
    #[serde(default = "default_enabled")]
    pub enabled: bool,
    /// 启动顺序 (parallel 或 sequential)
    #[serde(default = "default_startup_order")]
    pub startup_order: StartupOrder,
}

/// 启动顺序
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Default)]
pub enum StartupOrder {
    /// 并行启动
    #[serde(rename = "parallel")]
    #[default]
    Parallel,
    /// 顺序启动
    #[serde(rename = "sequential")]
    Sequential,
}

// 默认值函数
fn default_enabled() -> bool {
    true
}

fn default_priority() -> i32 {
    100
}

fn default_restart_delay() -> u64 {
    5
}

fn default_max_restarts() -> u32 {
    3
}

fn default_startup_order() -> StartupOrder {
    StartupOrder::Parallel
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;

    #[test]
    fn test_plugin_config_creation() {
        let config = PluginConfig::new("test_plugin".to_string())
            .enabled(true)
            .priority(50)
            .with_config("key1".to_string(), "value1")
            .unwrap()
            .with_env("ENV_VAR".to_string(), "env_value".to_string())
            .auto_restart(true)
            .with_tag("test".to_string());

        assert_eq!(config.name, "test_plugin");
        assert!(config.enabled);
        assert_eq!(config.priority, 50);
        assert!(config.auto_restart);
        assert_eq!(
            config.get_config::<String>("key1"),
            Some("value1".to_string())
        );
        assert_eq!(config.env.get("ENV_VAR"), Some(&"env_value".to_string()));
        assert!(config.tags.contains(&"test".to_string()));
    }

    #[test]
    fn test_config_validation() {
        let mut config = PluginConfig::new("".to_string());
        assert!(config.validate().is_err());

        config.name = "valid_name".to_string();
        assert!(config.validate().is_ok());

        config.priority = -1;
        assert!(config.validate().is_err());
    }

    #[test]
    fn test_config_merge() {
        let mut config1 = PluginConfig::new("test".to_string());
        config1.config.insert("key1".to_string(), json!("value1"));

        let mut config2 = PluginConfig::new("test".to_string());
        config2.config.insert("key2".to_string(), json!("value2"));
        config2.priority = 50;

        config1.merge(&config2);

        assert_eq!(config1.priority, 50);
        assert_eq!(
            config1.get_config::<String>("key1"),
            Some("value1".to_string())
        );
        assert_eq!(
            config1.get_config::<String>("key2"),
            Some("value2".to_string())
        );
    }

    #[test]
    fn test_serialization() {
        let config = PluginConfig::new("test_plugin".to_string())
            .enabled(true)
            .priority(50);

        let json_str = serde_json::to_string(&config).unwrap();
        let deserialized: PluginConfig = serde_json::from_str(&json_str).unwrap();

        assert_eq!(config.name, deserialized.name);
        assert_eq!(config.enabled, deserialized.enabled);
        assert_eq!(config.priority, deserialized.priority);
    }
}
