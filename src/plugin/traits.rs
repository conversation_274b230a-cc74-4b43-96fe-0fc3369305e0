use async_trait::async_trait;
use quant_common::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fmt;
use std::sync::Arc;
use tokio::sync::RwLock;

/// 插件主特征
///
/// 所有插件都必须实现这个特征
#[async_trait]
pub trait Plugin: Send + Sync + fmt::Debug {
    /// 插件名称
    fn name(&self) -> &str;

    /// 插件版本
    fn version(&self) -> &str;

    /// 插件描述
    fn description(&self) -> &str;

    /// 插件作者
    fn author(&self) -> &str {
        "Unknown"
    }

    /// 插件依赖项
    fn dependencies(&self) -> Vec<String> {
        Vec::new()
    }

    /// 初始化插件
    ///
    /// 在插件加载后调用，用于初始化插件的内部状态
    async fn initialize(&mut self, context: PluginContext) -> Result<(), PluginError>;

    /// 启动插件
    ///
    /// 在系统准备就绪后调用，插件应该在此开始其主要功能
    async fn start(&mut self) -> Result<(), PluginError>;

    /// 停止插件
    ///
    /// 在系统关闭前调用，插件应该清理资源并停止所有任务
    async fn stop(&mut self) -> Result<(), PluginError>;

    /// 获取插件状态
    fn status(&self) -> PluginStatus;

    /// 获取插件配置架构
    ///
    /// 返回JSON Schema格式的配置验证规则
    fn config_schema(&self) -> Option<String> {
        None
    }

    /// 配置变更通知
    ///
    /// 当插件配置发生变化时调用
    async fn on_config_changed(
        &mut self,
        _config: HashMap<String, serde_json::Value>,
    ) -> Result<(), PluginError> {
        Ok(())
    }

    /// 处理系统事件
    ///
    /// 当系统发生重要事件时调用
    async fn on_event(&mut self, _event: SystemEvent) -> Result<(), PluginError> {
        Ok(())
    }

    /// 健康检查
    ///
    /// 定期调用以检查插件健康状态
    async fn health_check(&self) -> Result<HealthStatus, PluginError> {
        Ok(HealthStatus::Healthy)
    }
}

/// 插件上下文
///
/// 提供插件运行所需的系统资源和接口
#[derive(Debug, Clone)]
pub struct PluginContext {
    /// 插件名称
    pub plugin_name: String,
    /// 系统配置
    pub system_config: Arc<RwLock<HashMap<String, serde_json::Value>>>,
    /// 插件配置
    pub plugin_config: Arc<RwLock<HashMap<String, serde_json::Value>>>,
    /// 事件发送器
    pub event_sender: tokio::sync::mpsc::UnboundedSender<SystemEvent>,
    /// 日志前缀
    pub log_prefix: String,
    /// 工作目录
    pub work_dir: std::path::PathBuf,
    /// 临时目录
    pub temp_dir: std::path::PathBuf,
}

impl PluginContext {
    /// 创建新的插件上下文
    pub fn new(plugin_name: String) -> Self {
        let (event_sender, _) = tokio::sync::mpsc::unbounded_channel();

        Self {
            log_prefix: format!("[{plugin_name}]"),
            work_dir: std::env::current_dir().unwrap_or_default(),
            temp_dir: std::env::temp_dir(),
            plugin_name,
            system_config: Arc::new(RwLock::new(HashMap::new())),
            plugin_config: Arc::new(RwLock::new(HashMap::new())),
            event_sender,
        }
    }

    /// 获取插件配置值
    pub async fn get_config<T>(&self, key: &str) -> Option<T>
    where
        T: for<'de> Deserialize<'de>,
    {
        let config = self.plugin_config.read().await;
        config
            .get(key)
            .and_then(|v| serde_json::from_value(v.clone()).ok())
    }

    /// 设置插件配置值
    pub async fn set_config<T>(&self, key: &str, value: T) -> Result<()>
    where
        T: Serialize,
    {
        let mut config = self.plugin_config.write().await;
        let json_value = serde_json::to_value(value)?;
        config.insert(key.to_string(), json_value);
        Ok(())
    }

    /// 发送系统事件
    pub fn send_event(&self, event: SystemEvent) -> Result<(), PluginError> {
        self.event_sender
            .send(event)
            .map_err(|_| PluginError::Internal("无法发送事件".to_string()))
    }

    /// 记录日志
    pub fn log_info(&self, message: &str) {
        info!("{} {}", self.log_prefix, message);
    }

    pub fn log_warn(&self, message: &str) {
        warn!("{} {}", self.log_prefix, message);
    }

    pub fn log_error(&self, message: &str) {
        error!("{} {}", self.log_prefix, message);
    }
}

/// 插件状态
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum PluginStatus {
    /// 未初始化
    Uninitialized,
    /// 正在初始化
    Initializing,
    /// 已初始化
    Initialized,
    /// 正在启动
    Starting,
    /// 运行中
    Running,
    /// 正在停止
    Stopping,
    /// 已停止
    Stopped,
    /// 错误状态
    Error(String),
    /// 已崩溃
    Crashed(String),
}

impl fmt::Display for PluginStatus {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            PluginStatus::Uninitialized => write!(f, "未初始化"),
            PluginStatus::Initializing => write!(f, "正在初始化"),
            PluginStatus::Initialized => write!(f, "已初始化"),
            PluginStatus::Starting => write!(f, "正在启动"),
            PluginStatus::Running => write!(f, "运行中"),
            PluginStatus::Stopping => write!(f, "正在停止"),
            PluginStatus::Stopped => write!(f, "已停止"),
            PluginStatus::Error(msg) => write!(f, "错误: {msg}"),
            PluginStatus::Crashed(msg) => write!(f, "崩溃: {msg}"),
        }
    }
}

/// 插件错误类型
#[derive(Debug, Clone)]
pub enum PluginError {
    InitializationError(String),
    StartupError(String),
    RuntimeError(String),
    ConfigurationError(String),
    DependencyError(String),
    Internal(String),
    Timeout(String),
}

impl fmt::Display for PluginError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            PluginError::InitializationError(msg) => write!(f, "初始化错误: {msg}"),
            PluginError::StartupError(msg) => write!(f, "启动错误: {msg}"),
            PluginError::RuntimeError(msg) => write!(f, "运行时错误: {msg}"),
            PluginError::ConfigurationError(msg) => write!(f, "配置错误: {msg}"),
            PluginError::DependencyError(msg) => write!(f, "依赖错误: {msg}"),
            PluginError::Internal(msg) => write!(f, "内部错误: {msg}"),
            PluginError::Timeout(msg) => write!(f, "超时错误: {msg}"),
        }
    }
}

impl std::error::Error for PluginError {}

/// 健康状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum HealthStatus {
    /// 健康
    Healthy,
    /// 警告
    Warning { message: String },
    /// 不健康
    Unhealthy { message: String },
    /// 未知
    Unknown,
}

/// 系统事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SystemEvent {
    /// 系统启动
    SystemStartup,
    /// 系统关闭
    SystemShutdown,
    /// 插件加载
    PluginLoaded { name: String },
    /// 插件卸载
    PluginUnloaded { name: String },
    /// 配置更新
    ConfigurationUpdated { plugin_name: Option<String> },
    /// 错误事件
    Error {
        plugin_name: Option<String>,
        error: String,
    },
    /// 自定义事件
    Custom {
        event_type: String,
        data: HashMap<String, serde_json::Value>,
    },
}

/// 插件生命周期回调特征
///
/// 可选实现，用于监听插件生命周期事件
#[async_trait]
pub trait PluginLifecycleCallback: Send + Sync {
    /// 插件加载前
    async fn before_load(&self, _name: &str) -> Result<(), PluginError> {
        Ok(())
    }

    /// 插件加载后
    async fn after_load(&self, _name: &str) -> Result<(), PluginError> {
        Ok(())
    }

    /// 插件启动前
    async fn before_start(&self, _name: &str) -> Result<(), PluginError> {
        Ok(())
    }

    /// 插件启动后
    async fn after_start(&self, _name: &str) -> Result<(), PluginError> {
        Ok(())
    }

    /// 插件停止前
    async fn before_stop(&self, _name: &str) -> Result<(), PluginError> {
        Ok(())
    }

    /// 插件停止后
    async fn after_stop(&self, _name: &str) -> Result<(), PluginError> {
        Ok(())
    }

    /// 插件卸载前
    async fn before_unload(&self, _name: &str) -> Result<(), PluginError> {
        Ok(())
    }

    /// 插件卸载后
    async fn after_unload(&self, _name: &str) -> Result<(), PluginError> {
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_plugin_status_display() {
        assert_eq!(PluginStatus::Running.to_string(), "运行中");
        assert_eq!(
            PluginStatus::Error("测试错误".to_string()).to_string(),
            "错误: 测试错误"
        );
    }

    #[tokio::test]
    async fn test_plugin_context() {
        let context = PluginContext::new("test_plugin".to_string());
        assert_eq!(context.plugin_name, "test_plugin");
        assert_eq!(context.log_prefix, "[test_plugin]");

        // 测试配置操作
        context.set_config("test_key", "test_value").await.unwrap();
        let value: Option<String> = context.get_config("test_key").await;
        assert_eq!(value, Some("test_value".to_string()));
    }
}
