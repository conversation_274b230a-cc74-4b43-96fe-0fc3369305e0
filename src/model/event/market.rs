use quant_common::base::{
    BboTicker, Depth, Exchange, Funding, Instrument, Kline, MarkPrice, Symbol, Ticker, Trade,
};

use crate::model::context::Context;

#[derive(Debug)]

pub struct MarketEvent {
    pub exchange: Exchange,
    pub context: Context,
    pub event: MarketEventInner,
}

#[derive(Clone, Debug)]
pub enum MarketEventInner {
    BboTicker(BboTicker),
    Depth(Depth),
    Ticker(Ticker),
    Funding(Vec<Funding>),
    Instrument(Vec<Instrument>),
    Trade(Trade),
    MarkPrice(MarkPrice),
    Kline(Kline),
    /// 产品更新
    InstrumentUpdated(Vec<Instrument>),
    /// 产品新增
    InstrumentAdded(Vec<Instrument>),
    /// 产品删除
    InstrumentRemoved(Vec<Symbol>),
}
