use quant_common::base::{Balance, FundingFee, Order, Position};
use serde_json::Value;

use crate::model::{account::AccountId, context::Context};

#[derive(Debug)]
pub struct AccountEvent {
    pub account_id: AccountId,
    pub context: Context,
    pub event: AccountEventInner,
}

#[derive(Clone, Debug)]
pub enum AccountEventInner {
    Position(Vec<Position>),
    Balance(Vec<Balance>),
    Order(Order),
    FudingFee(FundingFee),
    OrderAndFill(Order),
    Dex((String, Value)),
}
