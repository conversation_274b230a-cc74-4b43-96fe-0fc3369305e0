use serde::Serialize;

pub struct Monitor {
    pub monitor_system_info: MonitorSystemInfo,
}

pub struct MonitorSystemInfo {
    pub system_info: SystemMetaInfo,
    pub cpus_info: Option<CpusInfo>,
    pub memory_info: Option<MemoryInfo>,
    pub disk_info: Option<DiskInfo>,
    pub network_info: Vec<NetworkInfo>,
}

#[derive(Debug, Default, Serialize)]
pub struct SystemMetaInfo {
    pub system_name: String,
    pub kernel_version: String,
    pub os_version: String,
    pub os_long_version: String,
    pub host_name: String,
}

#[derive(Debug, Default, Serialize)]
pub struct CpusInfo {
    pub physical_core_count: String,
    pub total_usage: f32,
    pub cpus: Vec<CpuInfo>,
}

#[derive(Debug, Default, Serialize)]
pub struct CpuInfo {
    pub name: String,
    pub usage: f32,
    pub frequency: u64,
    pub vendor_id: String,
    pub brand: String,
}

#[derive(Debug, Default, Serialize)]
pub struct MemoryInfo {
    // 总内存，单位KB，下同
    pub total_memory: u64,
    // 可用内存
    pub available_memory: u64,
    // 已用内存
    pub used_memory: u64,
    // 空闲内存
    pub free_memory: u64,
    // 总swap
    pub total_swap: u64,
    // 已用swap
    pub used_swap: u64,
}

#[derive(Debug, Default, Serialize)]
pub struct DiskInfo {
    // 磁盘名称
    pub name: String,
    // 磁盘类型，HDD/SSD/UNKNOWN
    pub kind: String,
    // 文件系统，如NTFS/FAT32/EXT4等
    pub file_system: String,
    // 挂载点
    pub mount_point: String,
    // 总空间（单位B，下同）
    pub total_space: u64,
    // 可用空间
    pub available_space: u64,
    // 是否可移除
    pub is_removable: bool,
    // 是否只读
    pub is_read_only: bool,
}

#[derive(Debug, Default, Serialize)]
pub struct NetworkInfo {
    pub interface_name: String,
    pub ether: String,
    pub ips: Vec<String>,
    // 上次刷新后接收的字节数，单位B，下同
    pub received: u64,
    pub total_received: u64,
    // 上次刷新后接收的包数
    pub packets_received: u64,
    pub total_packets_received: u64,
    // 上次刷新后接收的错误数
    pub errors_on_received: u64,
    pub total_errors_on_received: u64,

    pub transmitted: u64,
    pub total_transmitted: u64,
    pub packets_transmitted: u64,
    pub total_packets_transmitted: u64,
    pub errors_on_transmitted: u64,
    pub total_errors_on_transmitted: u64,
}
