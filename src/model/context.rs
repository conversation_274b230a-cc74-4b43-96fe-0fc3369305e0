use std::borrow::Cow;

use minstant::Instant;
use quant_common::Result;
use rustc_hash::FxHashMap;
use serde::{Deserialize, Serialize};
use serde_json::json;

/// 上下文信息
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Context {
    /// 延迟
    #[serde(default)]
    pub latency: Option<Latency>,
    /// 请求id
    pub request_id: Option<RequestId>,
}

impl Default for Context {
    fn default() -> Self {
        Self {
            latency: Some(Default::default()),
            request_id: Default::default(),
        }
    }
}

impl Context {
    pub fn new(request_id: Option<RequestId>) -> Self {
        Self {
            latency: Some(Default::default()),
            request_id,
        }
    }

    pub fn take(&mut self) -> Self {
        std::mem::take(self)
    }
}
/// 延迟
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Latency {
    timer: Instant,
    times: LatencyTimes,
    label_times: FxHashMap<Cow<'static, str>, f32>,
    label_duration_metas: Vec<ReportDurationMeta>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
struct ReportDurationMeta {
    start_label: Cow<'static, str>,
    end_label: Cow<'static, str>,
    end_time: f32,
}

impl Default for Latency {
    fn default() -> Self {
        let mut label_times: FxHashMap<Cow<'static, str>, f32> = Default::default();
        label_times.insert(std::borrow::Cow::Borrowed("start"), 0f32);
        Self {
            timer: Instant::now(),
            times: Default::default(),
            label_times,
            label_duration_metas: Vec::with_capacity(32),
        }
    }
}

// 记录所在的位置, 用于加速日常测量
#[derive(Serialize, Debug)]
pub enum Milestone {
    StrategyBegin,
    StrategyEnd,

    // Execution
    ExCommandBegin,
    ExPlaceOrderEnd,
    ExAmendEnd,
    ExCancelEnd,
}

#[derive(Debug, Default, Serialize, Deserialize, Clone)]
struct LatencyTimes {
    strategy_begin: Option<f32>,
    strategy_end: Option<f32>,

    ex_command_begin: Option<f32>,

    place_order_end: Option<f32>,
    amend_end: Option<f32>,
    cancel_end: Option<f32>,
}

#[derive(Serialize, Debug, Default)]
pub struct LatencyRecord {
    #[serde(rename = "start -> strategy")]
    #[serde(skip_serializing_if = "Option::is_none")]
    start_strategy: Option<f32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    strategy: Option<f32>,
    #[serde(rename = "strategy -> execution begin")]
    #[serde(skip_serializing_if = "Option::is_none")]
    strategy_execution: Option<f32>,
    #[serde(rename = "start -> execution begin")]
    #[serde(skip_serializing_if = "Option::is_none")]
    start_execution_begin: Option<f32>,
    #[serde(rename = "start -> execution end")]
    #[serde(skip_serializing_if = "Option::is_none")]
    start_execution_end: Option<f32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    execution_place_order: Option<f32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    execution_amend_order: Option<f32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    execution_cancel_order: Option<f32>,
}

impl Latency {
    pub fn now(&self) -> f32 {
        self.timer.elapsed().as_secs_f32()
    }

    pub fn record(&mut self, milestone: Milestone) {
        let time = Some(self.now());
        let times = &mut self.times;
        match milestone {
            Milestone::ExCommandBegin => times.ex_command_begin = time,
            Milestone::ExPlaceOrderEnd => times.place_order_end = time,
            Milestone::ExAmendEnd => times.amend_end = time,
            Milestone::StrategyBegin => times.strategy_begin = time,
            Milestone::StrategyEnd => times.strategy_end = time,
            Milestone::ExCancelEnd => times.cancel_end = time,
        }
    }

    pub fn report(&self) -> Result<LatencyRecord> {
        let times = &self.times;

        let start_execution = if times.strategy_begin.is_none() {
            calculate_latency(times.ex_command_begin, Some(0.))
        } else {
            None
        };

        let latencies = LatencyRecord {
            start_strategy: calculate_latency(times.strategy_begin, Some(0.)),
            strategy: calculate_latency(times.strategy_end, times.strategy_begin),
            strategy_execution: calculate_latency(times.ex_command_begin, times.strategy_end),
            start_execution_begin: start_execution,
            start_execution_end: calculate_latency(times.ex_command_begin, Some(0.)),
            execution_place_order: calculate_latency(times.place_order_end, times.ex_command_begin),
            execution_amend_order: calculate_latency(times.amend_end, times.ex_command_begin),
            execution_cancel_order: calculate_latency(times.cancel_end, times.ex_command_begin),
        };

        let latency_json = serde_json::to_string(&latencies)?;
        debug!("latecy: {latency_json}");

        let mut json_hash = FxHashMap::default();
        for ReportDurationMeta {
            start_label,
            end_label,
            end_time,
        } in self.label_duration_metas.iter()
        {
            let start_time = self.label_times.get(start_label).copied();
            if let Some(duration) = calculate_latency(Some(*end_time), start_time) {
                json_hash.insert(format!("{start_label} => {end_label}"), duration);
            }
        }
        if !json_hash.is_empty() {
            let report_json = json!(json_hash);
            debug!("latecy: {report_json}");
        }
        Ok(latencies)
    }

    // 用于开发时灵活计时
    pub fn mark_time(&mut self, label: &'static str) {
        let check = self
            .label_times
            .insert(std::borrow::Cow::Borrowed(label), self.now());
        if check.is_some() {
            error!("耗时起点冲突, 已经更新新值, mark: {label}");
        }
    }

    pub fn record_time_since_mark(&mut self, now: &'static str, label: &'static str) {
        self.label_duration_metas.push(ReportDurationMeta {
            start_label: std::borrow::Cow::Borrowed(label),
            end_label: std::borrow::Cow::Borrowed(now),
            end_time: self.timer.elapsed().as_secs_f32(),
        });
    }
}

fn calculate_latency(leave_time: Option<f32>, enter_time: Option<f32>) -> Option<f32> {
    leave_time
        .zip(enter_time)
        .map(|(t1, t0)| t1 - t0)
        .map(|l| l * 1_000_000.0)
}

pub type RequestId = u64;
