use std::time::Duration;

use quant_common::base::{SubscribeChannel, Subscribes, WsHandler};
use serde::{Deserialize, Serialize};
use serde_json::Value;

use crate::strategy::Strategy;

use super::account::AccountId;

/// 数据源订阅包含数据源所需的参数及回调
/// 公共订阅根据exchange订阅
/// 私有订阅根据account id订阅
/// 两者必须传一个，如果两个都传，以account id为准
pub struct DataSourceSubscribe<H: Strategy + WsHandler> {
    pub account_id: AccountId,
    pub inner: DataSourceCommandInner<H>,
}

/// 具体的订阅类型
/// SubscribeWs: websockets订阅，然后传入实现了WsHandler的对象来处理消息
/// SubscribeRest: Rest每隔一段时间(自定义间隔)轮询，然后传入实现了RestHandler的对象来处理消息
/// SubscribeTimer: 每隔一段时间(自定义间隔)执行一次回调, 传入实现了TimerHandler的对象来处理消息
pub enum DataSourceCommandInner<H: Strategy + WsHandler> {
    SubscribeWs(Subscribes<H>),
    SubscribeRest((RestSubscribe, H)),
    SubscribeTimer((TimerSubscribe, H)),
}

#[derive(Debug, Deserialize, Serialize)]
pub struct RestSubscribe {
    pub update_interval: Duration,
    pub rest_type: RestType,
}

#[derive(Debug, Deserialize, Serialize)]
pub enum RestType {
    Balance,
    BalanceByCoin(String),
    Position,
    Funding,
    Instrument,
}

impl RestType {
    pub fn is_private(&self) -> bool {
        matches!(self, RestType::Balance | RestType::Position)
    }
}

#[derive(Debug, Deserialize, Serialize)]
pub struct TimerSubscribe {
    /// 定时器执行间隔
    pub update_interval: Duration,
    /// 定时器名称，用于标识和日志记录
    pub name: String,
    /// 定时器启动前的初始延迟时间，避免所有定时器同时启动
    #[serde(default)]
    pub initial_delay: Duration,
}

impl TimerSubscribe {
    /// 创建新的定时器订阅
    pub fn new(update_interval: Duration, name: String) -> Self {
        Self {
            update_interval,
            name,
            initial_delay: Duration::ZERO,
        }
    }

    /// 创建带有初始延迟的定时器订阅
    pub fn with_delay(update_interval: Duration, name: String, initial_delay: Duration) -> Self {
        Self {
            update_interval,
            name,
            initial_delay,
        }
    }
}

#[derive(Debug, Deserialize, Serialize)]
pub struct StrategySubscribe {
    #[serde(default)]
    pub account_id: AccountId,
    pub sub: StrategySubscribeInner,
}

#[derive(Debug, Deserialize, Serialize)]
pub enum StrategySubscribeInner {
    SubscribeWs(Vec<SubscribeChannel>),
    SubscribeRest(RestSubscribe),
    SubscribeTimer(TimerSubscribe),
    SubscribeDex(Value),
}

#[cfg(test)]
mod tests {
    use std::sync::Arc;

    use quant_common::base::{DepthWsParams, Symbol};

    #[test]
    fn test_strategy_subscribe_serialize() {
        use super::*;

        let symbols = Arc::new(vec![Symbol::new("BTC")]);

        let sub = StrategySubscribe {
            account_id: 0,
            sub: StrategySubscribeInner::SubscribeWs(vec![
                SubscribeChannel::Bbo(symbols.clone()),
                SubscribeChannel::Funding(symbols.clone()),
                SubscribeChannel::Depth(DepthWsParams {
                    symbols: symbols.clone(),
                    levels: 5,
                }),
            ]),
        };

        let sub_json = serde_json::to_string_pretty(&sub).unwrap();
        println!("{sub_json}");

        let sub_rest = StrategySubscribe {
            account_id: 0,
            sub: StrategySubscribeInner::SubscribeRest(RestSubscribe {
                update_interval: Duration::from_secs(1),
                rest_type: RestType::Balance,
            }),
        };

        let sub_rest_json = serde_json::to_string_pretty(&sub_rest).unwrap();
        println!("{sub_rest_json}");

        let sub_timer = StrategySubscribe {
            account_id: 0,
            sub: StrategySubscribeInner::SubscribeTimer(TimerSubscribe {
                update_interval: Duration::from_secs(1),
                name: "test".to_string(),
                initial_delay: Duration::from_millis(500), // 500ms初始延迟
            }),
        };

        let sub_timer_json = serde_json::to_string_pretty(&sub_timer).unwrap();
        println!("{sub_timer_json}");
    }
}
