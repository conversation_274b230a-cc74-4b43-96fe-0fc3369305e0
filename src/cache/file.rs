use quant_common::Result;
use std::{
    fs::{self, File},
    io::Write,
    path::PathBuf,
};

use super::Cache;

/// 异步文件缓存的实现
pub struct FileCache {
    file_path: PathBuf,
}

impl FileCache {
    // 创建一个新的 FileCache 实例
    pub fn new(file_path: PathBuf) -> Self {
        Self { file_path }
    }
}

impl Cache for FileCache {
    /// 保存数据到缓存文件
    fn save(&self, data: String) -> Result<()> {
        // 创建一个异步任务保存文件
        let file_path = self.file_path.clone();
        if let Err(e) = write(&file_path, data) {
            error!("保存缓存文件 {} 失败: {:?}", file_path.display(), e);
        }

        Ok(())
    }

    /// 从缓存文件中加载数据
    fn load(&self) -> Result<Option<String>> {
        let file_path = self.file_path.clone();
        if let Ok(data) = std::fs::read(file_path) {
            // 如果文件存在并且读取成功，反序列化数据
            // let deserialized_data: String = serde_json::from_slice(&data)?;
            let data = String::from_utf8(data)?;
            Ok(Some(data))
        } else {
            // 如果文件不存在或读取失败，返回 None
            Ok(None)
        }
    }
}

fn write(file_path: &PathBuf, data: String) -> Result<()> {
    let temp_file_path = file_path.with_extension("tmp");

    // 确保父目录存在
    if let Some(parent) = file_path.parent()
        && !parent.exists()
    {
        fs::create_dir_all(parent)?;
    }

    // 确保临时文件的父目录存在
    if let Some(parent) = temp_file_path.parent()
        && !parent.exists()
    {
        fs::create_dir_all(parent)?;
    }

    let mut temp_file = File::create(&temp_file_path).map_err(|e| {
        error!("创建临时文件 {} 失败: {:?}", temp_file_path.display(), e);
        e
    })?;
    temp_file.write_all(data.as_bytes()).map_err(|e| {
        error!("写入临时文件 {} 失败: {:?}", temp_file_path.display(), e);
        e
    })?;
    temp_file.flush().map_err(|e| {
        error!("刷新临时文件 {} 失败: {:?}", temp_file_path.display(), e);
        e
    })?;

    let temp_path_display = temp_file_path.display().to_string();
    fs::rename(temp_file_path, file_path).map_err(|e| {
        error!("重命名临时文件 {} 失败: {:?}", temp_path_display, e);
        e
    })?;
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;

    #[tokio::test]
    async fn test_file_cache() {
        let dir = tempdir().unwrap();
        let file_path = dir.path().join("test_file_cache.json");

        let cache = FileCache::new(file_path.clone());

        // 测试保存数据
        let data = r#"{"test": "test"}"#.to_string();
        cache.save(data.clone()).unwrap();

        // 测试加载数据
        let loaded_data = cache.load().unwrap().unwrap();
        assert_eq!(data, loaded_data);
    }
}
