use enum_dispatch::enum_dispatch;
use file::FileCache;
use quant_common::Result;
use serde::{Deserialize, Serialize};

use crate::config::CacheConfig;

pub mod file;

#[enum_dispatch(StaticCache)]
#[allow(async_fn_in_trait)]
pub trait Cache {
    fn save(&self, data: String) -> Result<()>;
    fn load(&self) -> Result<Option<String>>;
    // 接收 JSON 字符串并更新数据
    fn update(&self, json_data: String) -> Result<()> {
        self.save(json_data)
    }
}

#[enum_dispatch]
pub enum StaticCache {
    File(FileCache),
}

#[derive(Serialize, Deserialize, Copy, Clone, Debug, Default)]
pub enum CacheType {
    #[default]
    File,
}

pub struct CacheFactory;

impl CacheFactory {
    pub fn create(config: CacheConfig) -> StaticCache {
        let cache_type = config.typ;
        match cache_type {
            CacheType::File => {
                let file_path = config.path;
                StaticCache::File(FileCache::new(file_path.into()))
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;

    #[tokio::test]
    async fn test_cache_factory() {
        let dir = tempdir().unwrap();
        let file_path = dir.path().join("test_cache_factory.json");

        let config = CacheConfig {
            typ: CacheType::File,
            path: file_path.to_str().unwrap().to_string(),
        };

        let cache = CacheFactory::create(config);

        let data = r#"{"test": "test"}"#.to_string();
        cache.save(data.clone()).unwrap();

        let loaded_data = cache.load().unwrap().unwrap();
        assert_eq!(data, loaded_data);
    }
}
