use crate::config::model::WebConfig;
use crate::utils::sys::{NetworkUsage, SystemInfoManager};
use base64::engine::general_purpose::STANDARD as BASE64;
use base64::prelude::*;
use once_cell::sync::OnceCell;
use quant_common::{Result, qerror};
use reqwest::ClientBuilder;
use reqwest::header::{ACCEPT, CONTENT_TYPE, HeaderMap, HeaderValue};
use serde::{Deserialize, Serialize};
use serde_json::{Value, json};
use std::sync::{Arc, Mutex};
use std::time::Duration;
use tracing;

/// 全局HTTP客户端实例
/// 使用OnceCell确保线程安全的单例模式
static GLOBAL_HTTP_CLIENT: OnceCell<Arc<WebClient>> = OnceCell::new();

/// 获取全局HTTP客户端实例
///
/// # Arguments
/// * `config` - Web配置，仅在首次初始化时使用
///
/// # Returns
/// 返回全局HTTP客户端的Arc引用
pub fn get_global_http_client(config: Option<WebConfig>) -> Result<Arc<WebClient>> {
    GLOBAL_HTTP_CLIENT
        .get_or_try_init(|| {
            let config = config.ok_or_else(|| qerror!("首次初始化HTTP客户端时必须提供配置"))?;
            let client = WebClient::new(config);
            Ok(Arc::new(client))
        })
        .cloned()
}

/// 获取已初始化的全局HTTP客户端实例
///
/// # Returns
/// 返回全局HTTP客户端的Arc引用，如果未初始化则返回错误
pub fn get_global_http_client_ref() -> Result<Arc<WebClient>> {
    GLOBAL_HTTP_CLIENT
        .get()
        .ok_or_else(|| qerror!("HTTP客户端尚未初始化，请先调用get_global_http_client进行初始化"))
        .cloned()
}

/// API响应的通用结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub code: i32,
    pub msg: String,
    pub data: Option<T>,
}

/// 服务器管理响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerManageResponse {
    pub success: bool,
    pub message: String,
}

/// 授权状态响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthResponse {
    pub is_authorized: bool,
    pub message: String,
}

/// HTTP客户端，处理所有HTTP请求（内部使用，不暴露给Python）
pub struct WebClient {
    base_url: String,
    api_key: String,
    secret_id: String,
    secret_key: String,
    client: reqwest::Client,
    system_manager: Mutex<SystemInfoManager>,
}

impl WebClient {
    /// 创建HTTP客户端实例
    pub fn new(config: WebConfig) -> Self {
        // 根据环境选择域名
        let base_url = if config.is_production {
            "https://clm.nb8.net".to_string()
        } else {
            "https://test.nb8.net".to_string()
        };
        let api_key = "asdos1123399xxx".to_string();

        let mut headers = HeaderMap::new();
        headers.insert(CONTENT_TYPE, HeaderValue::from_static("application/json"));
        headers.insert(ACCEPT, HeaderValue::from_static("application/json"));

        let client = ClientBuilder::new()
            .default_headers(headers)
            .timeout(Duration::from_secs(30))
            .build()
            .unwrap_or_default();

        // 尝试创建系统信息管理器，如果失败也不影响HTTP客户端的其他功能
        let system_manager = Mutex::new(SystemInfoManager::new().unwrap());

        WebClient {
            base_url,
            api_key,
            secret_id: config.secret_id,
            secret_key: config.secret_key,
            client,
            system_manager,
        }
    }

    /// 内部纯Rust HTTP POST方法
    pub async fn post_internal(
        &self,
        endpoint: &str,
        data: Value,
        max_retries: usize,
        timeout: u64,
        need_key: bool,
    ) -> Result<Value> {
        let url = if need_key {
            format!("{}{}?key={}", self.base_url, endpoint, self.api_key)
        } else {
            format!("{}{}", self.base_url, endpoint)
        };
        let json_str = data.to_string();

        for attempt in 0..max_retries {
            match self
                .client
                .post(&url)
                .body(json_str.clone())
                .timeout(Duration::from_secs(timeout))
                .send()
                .await
            {
                Ok(response) => {
                    if response.status().is_success() {
                        let res_text = response.text().await.unwrap_or_default();

                        match serde_json::from_str::<Value>(&res_text) {
                            Ok(json_value) => return Ok(json_value),
                            Err(e) => {
                                tracing::error!(
                                    web = false,
                                    "HTTP请求失败 (尝试 {}/{}): {}, response: {}",
                                    attempt + 1,
                                    max_retries,
                                    e,
                                    res_text
                                );
                            }
                        }
                    } else {
                        let status = response.status();
                        let res_text = response.text().await.unwrap_or_default();
                        tracing::error!(
                            web = false,
                            "HTTP请求失败 (尝试 {}/{}): {}, response: {:?}",
                            attempt + 1,
                            max_retries,
                            status,
                            res_text
                        );
                    }
                }
                Err(e) => {
                    if attempt < max_retries - 1 {
                        tokio::time::sleep(Duration::from_secs(1)).await;
                    } else {
                        tracing::error!(web = false, "请求失败，URL: {}, error: {}", url, e);
                    }
                }
            }
        }

        Err(qerror!("HTTP请求失败: {}", url))
    }

    /// 内部纯Rust HTTP GET方法
    pub async fn get_internal(
        &self,
        endpoint: &str,
        max_retries: usize,
        timeout: u64,
    ) -> Result<Value> {
        let url = format!(
            "{}{}?key={}&SecretId={}&SecretKey={}",
            self.base_url, endpoint, self.api_key, self.secret_id, self.secret_key
        );

        for attempt in 0..max_retries {
            match self
                .client
                .get(&url)
                .timeout(Duration::from_secs(timeout))
                .send()
                .await
            {
                Ok(response) => {
                    if response.status().is_success() {
                        let res_text = response.text().await.unwrap_or_default();

                        match serde_json::from_str::<Value>(&res_text) {
                            Ok(json_value) => return Ok(json_value),
                            Err(e) => {
                                tracing::error!(
                                    web = false,
                                    "HTTP GET请求失败 (尝试 {}/{}): {}, response: {:?}",
                                    attempt + 1,
                                    max_retries,
                                    e,
                                    res_text
                                );
                            }
                        }
                    } else {
                        let status = response.status();
                        let res_text = response.text().await.unwrap_or_default();
                        tracing::error!(
                            web = false,
                            "HTTP GET请求失败 (尝试 {}/{}): {}, response: {:?}",
                            attempt + 1,
                            max_retries,
                            status,
                            res_text
                        );
                    }
                }
                Err(e) => {
                    tracing::warn!(
                        web = false,
                        "HTTP GET请求失败 (尝试 {}/{}): {}",
                        attempt + 1,
                        max_retries,
                        e
                    );
                    if attempt < max_retries - 1 {
                        tokio::time::sleep(Duration::from_secs(1)).await;
                    } else {
                        tracing::error!(web = false, "GET请求失败，URL: {}", url);
                    }
                }
            }
        }

        Err(qerror!("HTTP GET请求失败: {}", url))
    }

    /// 更新服务器配置接口（自动获取系统信息）
    pub async fn update_server_internal(&self) -> Result<ServerManageResponse> {
        // 使用管理器获取系统信息
        let system_info = {
            let mut manager = self
                .system_manager
                .lock()
                .map_err(|e| qerror!("获取系统信息管理器锁失败: {}", e))?;

            match manager.get_system_info() {
                Ok(info) => info,
                Err(e) => {
                    tracing::error!(web = false, "获取系统信息失败: {}", e);
                    return Err(qerror!("获取系统信息失败: {}", e));
                }
            }
        };

        let payload = json!({
            "SecretId": self.secret_id,
            "SecretKey": self.secret_key,
            "cpu": system_info.cpu,
            "cpu_usage": system_info.cpu_usage,
            "memory": system_info.memory,
            "memory_usage": system_info.memory_usage,
            "disk": system_info.disk,
            "disk_usage": system_info.disk_usage,
            "network_usage": {
                "up": system_info.network_usage.up,
                "down": system_info.network_usage.down
            },
            "os_info": system_info.os_info
        });

        let response = self
            .post_internal("/api/server_manage/updateServer", payload, 3, 10, false)
            .await?;

        // 解析API响应
        match serde_json::from_value::<ApiResponse<Value>>(response) {
            Ok(api_response) => {
                let success = api_response.code == 0;
                let message = if success {
                    format!("服务器配置更新成功: {}", api_response.msg)
                } else {
                    format!("服务器配置更新失败: {}", api_response.msg)
                };

                tracing::info!(web = false, "{}", message);

                Ok(ServerManageResponse { success, message })
            }
            Err(e) => {
                tracing::error!(web = false, "解析服务器管理响应失败: {}", e);
                Err(qerror!("解析服务器管理响应失败: {}", e))
            }
        }
    }

    /// 获取静态系统信息
    pub fn get_static_system_info(&self) -> Value {
        let manager = self.system_manager.lock().unwrap();
        let static_info = manager.get_static_info();
        json!({
            "cpu_count": static_info.cpu_count,
            "total_memory": static_info.total_memory,
            "disk_size": static_info.disk_size,
            "os_info": static_info.os_info
        })
    }

    /// 获取动态系统信息
    pub fn get_dynamic_system_info(&self) -> Result<Value> {
        let mut manager = self
            .system_manager
            .lock()
            .map_err(|e| qerror!("获取系统信息管理器锁失败: {}", e))?;

        match manager.get_dynamic_info() {
            Ok(dynamic_info) => Ok(json!({
                "cpu_usage": dynamic_info.cpu_usage,
                "memory_usage": dynamic_info.memory_usage,
                "disk_usage": dynamic_info.disk_usage,
                "network_usage": {
                    "up": dynamic_info.network_usage.up,
                    "down": dynamic_info.network_usage.down
                }
            })),
            Err(e) => {
                tracing::error!(web = false, "获取动态系统信息失败: {}", e);
                Err(qerror!("获取动态系统信息失败: {}", e))
            }
        }
    }

    /// 更新服务器配置接口（手动指定参数）
    #[allow(clippy::too_many_arguments)]
    pub async fn update_server_manual_internal(
        &self,
        cpu: Option<&str>,
        cpu_usage: Option<&str>,
        memory: Option<&str>,
        memory_usage: Option<&str>,
        disk: Option<&str>,
        disk_usage: Option<&str>,
        network_usage: Option<NetworkUsage>,
        os_info: Option<&str>,
    ) -> Result<ServerManageResponse> {
        let mut payload = json!({
            "SecretId": self.secret_id,
            "SecretKey": self.secret_key
        });

        // 添加可选参数
        if let Some(cpu) = cpu {
            payload["cpu"] = json!(cpu);
        }
        if let Some(cpu_usage) = cpu_usage {
            payload["cpu_usage"] = json!(cpu_usage);
        }
        if let Some(memory) = memory {
            payload["memory"] = json!(memory);
        }
        if let Some(memory_usage) = memory_usage {
            payload["memory_usage"] = json!(memory_usage);
        }
        if let Some(disk) = disk {
            payload["disk"] = json!(disk);
        }
        if let Some(disk_usage) = disk_usage {
            payload["disk_usage"] = json!(disk_usage);
        }
        if let Some(network_usage) = network_usage {
            payload["network_usage"] = json!({
                "up": network_usage.up,
                "down": network_usage.down
            });
        }
        if let Some(os_info) = os_info {
            payload["os_info"] = json!(os_info);
        }

        tracing::debug!(web = false, "更新服务器配置 (手动): {}", payload);

        let response = self
            .post_internal("/api/server_manage/updateServer", payload, 3, 10, false)
            .await?;

        // 解析API响应
        match serde_json::from_value::<ApiResponse<Value>>(response) {
            Ok(api_response) => {
                let success = api_response.code == 0;
                let message = if success {
                    format!("服务器配置更新成功: {}", api_response.msg)
                } else {
                    format!("服务器配置更新失败: {}", api_response.msg)
                };

                tracing::info!(web = false, "{}", message);

                Ok(ServerManageResponse { success, message })
            }
            Err(e) => {
                tracing::error!(web = false, "解析服务器管理响应失败: {}", e);
                Err(qerror!("解析服务器管理响应失败: {}", e))
            }
        }
    }

    /// 获取授权状态接口 (POST方式)
    pub async fn is_auth_internal(&self) -> Result<AuthResponse> {
        let payload = json!({
            "SecretId": self.secret_id,
            "SecretKey": self.secret_key
        });

        tracing::debug!(web = false, "检查授权状态");

        let response = self
            .post_internal("/api/server_manage/isAuth", payload, 3, 10, false)
            .await?;

        // 解析API响应
        match serde_json::from_value::<ApiResponse<Option<bool>>>(response) {
            Ok(api_response) => {
                let data = api_response.data.unwrap_or_default();
                let is_authorized = api_response.code == 0 && data.unwrap_or_default();
                let message = match api_response.code {
                    0 => {
                        if data.unwrap_or_default() {
                            "服务器已授权".to_string()
                        } else {
                            "服务器未授权".to_string()
                        }
                    }
                    401 => format!("鉴权失败: {}", api_response.msg),
                    _ => format!("授权检查失败: {}", api_response.msg),
                };

                tracing::info!(web = false, "授权状态: {}", message);

                Ok(AuthResponse {
                    is_authorized,
                    message,
                })
            }
            Err(e) => {
                tracing::error!(web = false, "解析授权响应失败: {}", e);
                Err(qerror!("解析授权响应失败: {}", e))
            }
        }
    }

    /// 获取授权状态接口 (GET方式)
    pub async fn is_auth_get_internal(&self) -> Result<AuthResponse> {
        tracing::debug!(web = false, "检查授权状态 (GET)");

        let response = self
            .get_internal("/api/server_manage/isAuth", 3, 10)
            .await?;

        // 解析API响应
        match serde_json::from_value::<ApiResponse<Option<bool>>>(response) {
            Ok(api_response) => {
                let data = api_response.data.unwrap_or_default();
                let is_authorized = api_response.code == 0 && data.unwrap_or_default();
                let message = match api_response.code {
                    0 => {
                        if data.unwrap_or_default() {
                            "服务器已授权".to_string()
                        } else {
                            "服务器未授权".to_string()
                        }
                    }
                    401 => "鉴权失败: 密钥无效".to_string(),
                    _ => format!("授权检查失败: {}", api_response.msg),
                };

                tracing::info!(web = false, "授权状态 (GET): {}", message);

                Ok(AuthResponse {
                    is_authorized,
                    message,
                })
            }
            Err(e) => {
                tracing::error!(web = false, "解析授权响应失败: {}", e);
                Err(qerror!("解析授权响应失败: {}", e))
            }
        }
    }

    /// 获取实盘账户信息接口
    pub async fn get_account_data_internal(
        &self,
        name: &str,
        data_type: &str, // historyOrder, pendingOrder, position, balance
        exchange: &str,
        page: Option<u32>,
        limit: Option<u32>,
    ) -> Result<Value> {
        let page = page.unwrap_or(1);
        let limit = limit.unwrap_or(30);

        let payload = json!({
            "name": name,
            "type": data_type,
            "exchange": exchange,
            "page": page,
            "limit": limit
        });

        tracing::debug!(web = false, "获取账户数据: {}", payload);

        self.post_internal("/accountData", payload, 3, 10, false)
            .await
    }

    /// 内部纯Rust上传日志方法 (支持端口参数)
    pub async fn upload_log_internal(
        &self,
        server_name: &str,
        run_time: i64,
        side_time: i64,
        data: Value,
        port: Option<u16>,
    ) -> Result<Value> {
        let json_str = data.to_string();

        trace!(web = false, "上传日志: {}", json_str);

        let encoded_data = BASE64.encode(json_str);

        let mut payload = json!({
            "serverName": server_name,
            "runTime": run_time,
            "sideTime": side_time,
            "data": encoded_data
        });

        // 如果提供了端口，添加到payload中
        if let Some(port) = port {
            payload["port"] = json!(port);
        }

        self.post_internal("/api/log", payload, 10, 5, true).await
    }

    /// 内部纯Rust上传错误日志方法
    pub async fn upload_error_internal(
        &self,
        server_name: &str,
        error_data: &str,
    ) -> Result<Value> {
        let encoded_data = BASE64.encode(error_data);

        let payload = json!({
            "serverName": server_name,
            "data": encoded_data
        });

        self.post_internal("/api/log/addError", payload, 10, 5, true)
            .await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;
    use tokio;

    /// 创建测试用的HTTP客户端
    fn create_test_client() -> Arc<WebClient> {
        get_global_http_client(Some(WebConfig {
            is_production: false,
            secret_id: "Oxo3XNOnWS0DQsbX".to_string(),
            secret_key: "280c53acfc9bdd592042286d08f2f46e".to_string(),
        }))
        .unwrap()
    }

    #[tokio::test]
    async fn test_update_server_auto() {
        let client = create_test_client();

        // 测试自动获取系统信息的服务器更新
        match client.update_server_internal().await {
            Ok(response) => {
                println!("服务器更新响应: {response:?}");
            }
            Err(e) => {
                println!("错误: {e}");
            }
        }
    }

    #[tokio::test]
    async fn test_update_server_manual() {
        let client = create_test_client();

        let network_usage = NetworkUsage {
            up: "1986.12".to_string(),
            down: "4356.10".to_string(),
        };

        // 测试手动指定参数的服务器更新
        match client
            .update_server_manual_internal(
                None,
                Some("56.81"),
                None,
                Some("16.12"),
                None,
                Some("46.08"),
                Some(network_usage),
                None,
            )
            .await
        {
            Ok(response) => {
                println!("手动服务器更新响应: {response:?}");
            }
            Err(e) => {
                println!("预期的错误: {e}");
            }
        }
    }

    #[tokio::test]
    async fn test_is_auth_post() {
        let client = create_test_client();

        match client.is_auth_internal().await {
            Ok(response) => {
                println!("POST授权检查响应: {response:?}");
                assert!(!response.message.is_empty());
            }
            Err(e) => {
                println!("预期的错误: {e}");
            }
        }
    }

    #[tokio::test]
    async fn test_is_auth_get() {
        let client = create_test_client();

        match client.is_auth_get_internal().await {
            Ok(response) => {
                println!("GET授权检查响应: {response:?}");
                assert!(!response.message.is_empty());
            }
            Err(e) => {
                println!("预期的错误: {e}");
            }
        }
    }

    #[tokio::test]
    async fn test_get_account_data() {
        let client = create_test_client();

        match client
            .get_account_data_internal(
                "GridStrategy_bitget_margin",
                "position",
                "bitget",
                Some(1),
                Some(30),
            )
            .await
        {
            Ok(response) => {
                println!("账户数据响应: {response:?}");
            }
            Err(e) => {
                println!("预期的错误: {e}");
            }
        }
    }

    #[tokio::test]
    async fn test_get_account_data_with_defaults() {
        let client = create_test_client();

        match client
            .get_account_data_internal(
                "GridStrategy_bitget_margin",
                "balance",
                "bitget",
                None, // 使用默认page=1
                None, // 使用默认limit=30
            )
            .await
        {
            Ok(response) => {
                println!("账户数据（默认参数）响应: {response:?}");
            }
            Err(e) => {
                println!("预期的错误: {e}");
            }
        }
    }

    #[tokio::test]
    async fn test_upload_log_with_port() {
        let client = create_test_client();

        let test_data = json!({
            "test": "data",
            "timestamp": **********
        });

        match client
            .upload_log_internal("test_server", **********, 0, test_data, Some(8080))
            .await
        {
            Ok(response) => {
                println!("日志上传（带端口）响应: {response:?}");
            }
            Err(e) => {
                println!("预期的错误: {e}");
            }
        }
    }

    #[tokio::test]
    async fn test_upload_log_without_port() {
        let client = create_test_client();

        let test_data = json!({
            "test": "data",
            "timestamp": **********
        });

        match client
            .upload_log_internal("test_server", **********, 0, test_data, None)
            .await
        {
            Ok(response) => {
                println!("日志上传（无端口）响应: {response:?}");
            }
            Err(e) => {
                println!("预期的错误: {e}");
            }
        }
    }

    #[tokio::test]
    async fn test_upload_error() {
        let client = create_test_client();

        match client
            .upload_error_internal("test_server", "Test error message\nwith newlines")
            .await
        {
            Ok(response) => {
                println!("错误日志上传响应: {response:?}");
            }
            Err(e) => {
                println!("预期的错误: {e}");
            }
        }
    }

    #[test]
    fn test_api_response_structures() {
        // 测试API响应结构体的序列化和反序列化

        // 测试服务器管理响应
        let server_response = ServerManageResponse {
            success: true,
            message: "服务器配置更新成功".to_string(),
        };

        let json_str = serde_json::to_string(&server_response).unwrap();
        let parsed: ServerManageResponse = serde_json::from_str(&json_str).unwrap();
        assert!(parsed.success);
        assert_eq!(parsed.message, "服务器配置更新成功");

        // 测试授权响应
        let auth_response = AuthResponse {
            is_authorized: true,
            message: "服务器已授权".to_string(),
        };

        let json_str = serde_json::to_string(&auth_response).unwrap();
        let parsed: AuthResponse = serde_json::from_str(&json_str).unwrap();
        assert!(parsed.is_authorized);
        assert_eq!(parsed.message, "服务器已授权");

        // 测试API响应结构
        let api_response = ApiResponse {
            code: 0,
            msg: "成功".to_string(),
            data: Some(true),
        };

        let json_str = serde_json::to_string(&api_response).unwrap();
        let parsed: ApiResponse<bool> = serde_json::from_str(&json_str).unwrap();
        assert_eq!(parsed.code, 0);
        assert_eq!(parsed.msg, "成功");
        assert!(parsed.data.unwrap_or_default());
    }
}
