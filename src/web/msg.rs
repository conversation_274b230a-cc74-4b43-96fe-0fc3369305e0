use algo_common::{
    logger::tracing_layer::LogMsg,
    msg::{BaseChartData, ChartInfo, ClientToServerMsg, ListDataDetail, LogRecord},
};

pub fn parse_log(mut log: LogMsg) -> Option<ClientToServerMsg> {
    trace!("解析日志: {log:?}");
    match log.event.take() {
        Some(event) => handle_log_event(&event, log),
        None => Some(ClientToServerMsg::LogRecord(LogRecord {
            timestamp: log.time_ms,
            level: log.level.to_string(),
            msg: log.message,
        })),
    }
}

fn handle_log_event(event: &str, log: LogMsg) -> Option<ClientToServerMsg> {
    match event {
        "chart_group" => Some(ClientToServerMsg::ChartGroup(log.message)),
        "chart" => {
            let chart: Result<ChartInfo, _> = serde_json::from_str(&log.message);
            match chart {
                Ok(chart) => Some(ClientToServerMsg::Chart(chart)),
                Err(e) => {
                    error!("解析图表数据失败: {e}");
                    None
                }
            }
        }
        "chart_data" => {
            let chart_data: Result<BaseChartData, _> = serde_json::from_str(&log.message);
            match chart_data {
                Ok(chart_data) => Some(ClientToServerMsg::ChartData(chart_data)),
                Err(e) => {
                    error!("解析图表数据失败: {e}");
                    None
                }
            }
        }
        "dynamic_data" => Some(ClientToServerMsg::DynamicData(log.message)),
        "list_data" => {
            let list_data: Result<Vec<ListDataDetail>, _> = serde_json::from_str(&log.message);
            match list_data {
                Ok(list_data) => Some(ClientToServerMsg::ListData(list_data)),
                Err(e) => {
                    error!("解析列表数据失败: {e}");
                    None
                }
            }
        }
        _ => {
            error!("unknown event: {event}, log: {log:?}");
            None
        }
    }
}

#[cfg(test)]
mod tests {
    use algo_common::msg::YData;
    use quant_common::time_ms;
    use serde_json::json;
    use tracing::Level;

    use super::*;

    #[test]
    fn test_parse_log() {
        let chart = ChartInfo {
            id: "1".to_string(),
            chart: "test".to_string(),
        };
        let log = LogMsg {
            time_ms: 0,
            event: Some("chart".to_string()),
            level: Level::INFO,
            message: serde_json::to_string(&chart).unwrap(),
        };
        let msg = parse_log(log).unwrap();
        println!("{msg:?}");

        let chart_data = BaseChartData {
            id: "1".to_string(),
            x_data: json!(time_ms()),
            y_data: vec![YData {
                index: 0,
                data: 0.7,
            }],
        };

        let message = serde_json::to_string(&chart_data).unwrap();
        println!("message: {message}");
        let log = LogMsg {
            time_ms: 0,
            event: Some("chart_data".to_string()),
            level: Level::INFO,
            message,
        };
        let msg = parse_log(log).unwrap();
        println!("{msg:?}");

        let log = LogMsg {
            time_ms: 0,
            event: Some("dynamic_data".to_string()),
            level: Level::INFO,
            message: "test".to_string(),
        };
        let msg = parse_log(log).unwrap();
        println!("{msg:?}");

        let list_data = vec![
            ListDataDetail {
                index: 0,
                content: "test".to_string(),
                color: "red".to_string(),
                font_size: "fx12".to_string(),
            },
            ListDataDetail {
                index: 1,
                content: "test".to_string(),
                color: "red".to_string(),
                font_size: "fx12".to_string(),
            },
        ];

        let log = LogMsg {
            time_ms: 0,
            event: Some("list_data".to_string()),
            level: Level::INFO,
            message: serde_json::to_string(&list_data).unwrap(),
        };
        let msg = parse_log(log).unwrap();
        println!("{msg:?}");

        let log = LogMsg {
            time_ms: 0,
            event: Some("unknown".to_string()),
            level: Level::INFO,
            message: "test".to_string(),
        };
        assert!(parse_log(log).is_none());

        let log = LogMsg {
            time_ms: 0,
            event: Some("chart_group".to_string()),
            level: Level::INFO,
            message: "[{\"type\": \"text\", \"content\": \"\\u8fd9\\u662f\\u4e00\\u4e2a\\u65b0\\u7684\\u793a\\u4f8b\\u6587\\u672c\\u3002\"}, {\"type\": \"line-break\", \"content\": null}, {\"type\": \"text\", \"content\": \"\\u8fd9\\u662f\\u7b2c\\u4e8c\\u6bb5\\u65b0\\u7684\\u6587\\u672c\\u3002\"}, {\"type\": \"table\", \"content\": {\"headers\": [{\"key\": \"date\", \"name\": \"\\u65e5\\u671f\", \"width\": \"120px\", \"align\": \"center\"}, {\"key\": \"name\", \"name\": \"\\u59d3\\u540d\", \"width\": \"120px\", \"align\": \"center\"}, {\"key\": \"city\", \"name\": \"\\u57ce\\u5e02\", \"width\": \"200px\", \"align\": \"center\"}], \"rows\": [{\"date\": \"2023-10-01\", \"name\": \"Alice\", \"city\": \"New York\"}, {\"date\": \"2023-10-02\", \"name\": \"Bob\", \"city\": \"Los Angeles\"}, {\"date\": \"2023-10-03\", \"name\": \"Charlie\", \"city\": \"Chicago\"}]}}, {\"type\": \"button\", \"content\": {\"name\": \"ClosePosition\", \"label\": \"\\u5e73\\u4ed3\", \"color\": \"#00ff00\", \"bg_color\": \"#ff0000\"}}, {\"type\": \"tab\", \"content\": [{\"label\": \"\\u65b0\\u5185\\u5bb9\", \"data\": [{\"type\": \"text\", \"content\": \"\\u8fd9\\u662f\\u65b0\\u5185\\u5bb9\\u7684\\u6587\\u672c\\u3002\"}, {\"type\": \"line-break\", \"content\": null}, {\"type\": \"table\", \"content\": {\"headers\": [{\"key\": \"date\", \"name\": \"\\u65e5\\u671f\", \"width\": \"120px\", \"align\": \"center\"}, {\"key\": \"name\", \"name\": \"\\u59d3\\u540d\", \"width\": \"120px\", \"align\": \"center\"}, {\"key\": \"city\", \"name\": \"\\u57ce\\u5e02\", \"width\": \"200px\", \"align\": \"center\"}], \"rows\": [{\"date\": \"2023-10-01\", \"name\": \"Alice\", \"city\": \"New York\"}, {\"date\": \"2023-10-02\", \"name\": \"Bob\", \"city\": \"Los Angeles\"}, {\"date\": \"2023-10-03\", \"name\": \"Charlie\", \"city\": \"Chicago\"}]}}, {\"type\": \"button\", \"content\": {\"name\": \"NewButton\", \"label\": \"\\u65b0\\u6309\\u94ae\", \"color\": \"#00ff00\", \"bg_color\": \"#ff0000\"}}]}, {\"label\": \"\\u8d26\\u6237\\u4fe1\\u606f\", \"data\": [{\"type\": \"table\", \"content\": {\"headers\": [{\"key\": \"account\", \"name\": \"\\u8d26\\u6237\", \"width\": \"120px\", \"align\": \"center\"}, {\"key\": \"balance\", \"name\": \"\\u4f59\\u989d\", \"width\": \"120px\", \"align\": \"center\"}, {\"key\": \"status\", \"name\": \"\\u72b6\\u6001\", \"width\": \"200px\", \"align\": \"center\"}, {\"key\": \"action\", \"name\": \"\\u64cd\\u4f5c\", \"width\": \"120px\", \"align\": \"center\"}], \"rows\": [{\"account\": \"A001\", \"balance\": \"$500\", \"status\": \"Active\", \"action\": \"View\"}, {\"account\": \"A002\", \"balance\": \"$750\", \"status\": \"Inactive\", \"action\": \"View\"}, {\"account\": \"A003\", \"balance\": \"$900\", \"status\": \"Active\", \"action\": \"View\"}]}}, {\"type\": \"button\", \"content\": {\"name\": \"OtherButton\", \"label\": \"\\u5176\\u4ed6\\u6309\\u94ae\", \"color\": \"#00ff00\", \"bg_color\": \"#ff0000\"}}]}]}".to_string(),
        };
        let res = parse_log(log);
        println!("res: {res:?}");
        assert!(res.is_some());
    }
}
