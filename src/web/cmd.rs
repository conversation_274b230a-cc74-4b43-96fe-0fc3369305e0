use algo_common::msg::{AsyncCmd, AsyncCmdRsp, Cmd};
use quant_common::{Result, qerror};

use crate::{model::event::Event, strategy::Strategy};

pub async fn handle_command<S: Strategy>(cmd: AsyncCmd, strategy: &S) -> Result<AsyncCmdRsp> {
    let id = cmd.id.clone();
    match cmd.cmd {
        Cmd::StableCmd(_) => match strategy.handle_event(Event::Command(cmd)).await {
            Ok(_) => Ok(AsyncCmdRsp {
                id,
                code: 0,
                msg: "".to_string(),
                cmd_type: algo_common::msg::CmdType::StableCmd,
            }),
            Err(e) => {
                error!("处理命令{id}失败: {}", e);
                Ok(AsyncCmdRsp {
                    id,
                    code: e.code() as i32,
                    msg: e.to_string(),
                    cmd_type: algo_common::msg::CmdType::StableCmd,
                })
            }
        },
        Cmd::CustomCmd(_) => match strategy.handle_event(Event::Command(cmd)).await {
            Ok(_) => Ok(AsyncCmdRsp {
                id,
                code: 0,
                msg: "".to_string(),
                cmd_type: algo_common::msg::CmdType::CustomCmd,
            }),
            Err(e) => {
                error!("处理命令{id}失败: {}", e);
                Ok(AsyncCmdRsp {
                    id,
                    code: e.code() as i32,
                    msg: e.to_string(),
                    cmd_type: algo_common::msg::CmdType::CustomCmd,
                })
            }
        },
        _ => Err(qerror!("未知的服务器命令: {:?}", cmd)),
    }
}
