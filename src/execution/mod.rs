use std::sync::Arc;

use crate::{
    config::{ExParams, ExecutionEngineConfig},
    model::event::ex_command::ExecutionCommand,
    strategy::Strategy,
};
use enum_dispatch::enum_dispatch;
use quant_api::ExchangeRest;

use quant_common::Result;
use serde::{Deserialize, Serialize};
use stand::StandExecutionEngine;

pub mod stand;

#[allow(async_fn_in_trait)]
#[enum_dispatch]
pub enum StaticExecutionEngine {
    Stand(StandExecutionEngine),
}

#[allow(async_fn_in_trait)]
#[enum_dispatch(StaticExecutionEngine)]
pub trait ExecutionEngine {
    async fn start(&self) -> quant_common::Result<()>;

    async fn set_strategy(&self, strategy: Box<dyn Strategy>);

    async fn execution_rest<F, Fut, R>(&self, index: usize, action: F) -> Option<R>
    where
        F: FnOnce(Arc<ExchangeRest>) -> Fut + Send + 'static,
        Fut: std::future::Future<Output = R> + Send + 'static,
        R: Send + 'static;

    async fn execution_async(&self, cmd: ExecutionCommand) -> quant_common::Result<()>;
}

#[derive(Serialize, Deserialize, Copy, Clone, Debug, Default)]
pub enum ExecutionEngineType {
    #[default]
    Stand,
}

pub struct ExecutionEngineFactory;

impl ExecutionEngineFactory {
    pub async fn create(
        exchanges: Vec<ExParams>,
        config: ExecutionEngineConfig,
    ) -> Result<StaticExecutionEngine> {
        let typ = config.typ;
        match typ {
            ExecutionEngineType::Stand => {
                let engine = StandExecutionEngine::new(exchanges, config.task_count).await?;
                Ok(StaticExecutionEngine::Stand(engine))
            }
        }
    }
}
