use std::sync::Arc;
use std::sync::atomic::{AtomicUsize, Ordering};

use quant_api::ws_api::{ExchangeWsAPI, create_private_ws_api};
use quant_common::Result;
use quant_common::base::{AsyncCmd, AsyncCmdResult, AsyncResHandle, Exchange};

use crate::config::ExParams;
use crate::model::account::AccountId;
use crate::model::context::Context;
use crate::model::event::Event;
use crate::model::event::ex_command::ExecutionAsyncCommandResult;
use crate::strategy::Strategy;

/// 性能优化常量
const ATOMIC_ORDERING: Ordering = Ordering::Relaxed; // 放宽内存顺序要求

pub struct WsAPI {
    pub apis: Vec<(ExchangeWsAPI, AccountId)>,
    pub supported: Vec<bool>,
    /// 每个account对应多个通道的发送端，每个task一个通道
    pub txs: Vec<Vec<async_channel::Sender<(u64, AsyncCmd)>>>,
    /// 每个account对应多个通道的接收端，每个task一个通道
    pub rxs: Vec<Vec<async_channel::Receiver<(u64, AsyncCmd)>>>,
    /// 用于轮询发送的计数器，每个account一个
    pub round_robin_counters: Vec<AtomicUsize>,
    /// 每个account的task数量掩码，用于快速取模运算
    pub task_masks: Vec<usize>,
}

impl WsAPI {
    pub async fn new(ex_params: Vec<ExParams>, ws_api_task_count: usize) -> Result<Self> {
        // 将task_count调整为2的幂次方，便于位运算优化
        let optimal_task_count = Self::next_power_of_two(ws_api_task_count);

        // 预分配容器，避免运行时重分配
        let account_count = ex_params.len();
        let mut apis = Vec::with_capacity(account_count);
        let mut supported = vec![false; account_count];
        let mut txs = Vec::with_capacity(account_count);
        let mut rxs = Vec::with_capacity(account_count);
        let mut round_robin_counters = Vec::with_capacity(account_count);
        let mut task_masks = Vec::with_capacity(account_count);

        // 初始化每个account的容器
        txs.resize(account_count, Vec::new());
        rxs.resize(account_count, Vec::new());

        for (i, ex_param) in ex_params.iter().enumerate() {
            round_robin_counters.push(AtomicUsize::new(0));
            task_masks.push(0); // 默认掩码

            let config = &ex_param.config;
            if !ex_param.use_ws_api || config.key.is_empty() || config.secret.is_empty() {
                continue;
            }

            let api = create_private_ws_api(config.clone()).await?;
            info!("creating api");
            match api {
                ExchangeWsAPI::Unsupported(_) => {}
                _ => {
                    info!("support ws");
                    // 预分配通道容器
                    let mut account_txs = Vec::with_capacity(optimal_task_count);
                    let mut account_rxs = Vec::with_capacity(optimal_task_count);

                    // 批量创建通道
                    for _ in 0..optimal_task_count {
                        let (tx, rx) = async_channel::unbounded::<(u64, AsyncCmd)>();
                        account_txs.push(tx);
                        account_rxs.push(rx);
                    }

                    apis.push((api, i));
                    supported[i] = true;
                    txs[i] = account_txs;
                    rxs[i] = account_rxs;
                    // 计算位掩码：2^n - 1
                    task_masks[i] = optimal_task_count - 1;
                }
            }
        }

        Ok(Self {
            apis,
            supported,
            txs,
            rxs,
            round_robin_counters,
            task_masks,
        })
    }

    /// 计算下一个2的幂次方
    #[inline]
    const fn next_power_of_two(n: usize) -> usize {
        if n <= 1 {
            1
        } else if n <= 2 {
            2
        } else if n <= 4 {
            4
        } else if n <= 8 {
            8
        } else if n <= 16 {
            16
        } else if n <= 32 {
            32
        } else if n <= 64 {
            64
        } else {
            128 // 最大值限制
        }
    }

    #[inline]
    pub fn is_supported(&self, account_id: usize) -> bool {
        // 边界检查优化
        unsafe { *self.supported.get_unchecked(account_id) }
    }

    /// 高性能轮询发送命令到指定account的某个task通道
    #[inline]
    pub async fn send_round_robin(
        &self,
        account_id: usize,
        cmd: (u64, AsyncCmd),
    ) -> Result<(), async_channel::SendError<(u64, AsyncCmd)>> {
        // 快速路径：避免重复检查
        if unsafe { !self.supported.get_unchecked(account_id) } {
            return Err(async_channel::SendError(cmd));
        }

        let txs = unsafe { self.txs.get_unchecked(account_id) };
        if txs.is_empty() {
            return Err(async_channel::SendError(cmd));
        }

        // 使用位掩码替代求余运算，性能提升显著
        let mask = unsafe { *self.task_masks.get_unchecked(account_id) };
        let counter = unsafe { self.round_robin_counters.get_unchecked(account_id) };
        let current_index = counter.fetch_add(1, ATOMIC_ORDERING) & mask;

        // 直接索引，避免边界检查
        let tx = unsafe { txs.get_unchecked(current_index) };
        tx.send(cmd).await
    }

    /// 获取指定account的task数量
    #[inline]
    pub fn get_task_count(&self, account_id: usize) -> usize {
        if account_id < self.txs.len() {
            self.txs[account_id].len()
        } else {
            0
        }
    }
}

#[derive(Clone)]
pub struct StrategyWrapper {
    pub strategy: Arc<Box<dyn Strategy>>,
    pub exchanges: Vec<Exchange>,
}

impl StrategyWrapper {
    pub fn new(strategy: Arc<Box<dyn Strategy>>, exchanges: Vec<Exchange>) -> Self {
        Self {
            strategy,
            exchanges,
        }
    }
}

impl AsyncResHandle for StrategyWrapper {
    async fn handle_post_order(
        &self,
        account_id: usize,
        req_id: u64,
        res: quant_common::base::PlaceOrderResult,
    ) -> Result<()> {
        let event = Event::ExecutionResult(ExecutionAsyncCommandResult {
            exchange: self.exchanges[account_id],
            account_id,
            context: Context::new(Some(req_id)),
            result: AsyncCmdResult::PlaceOrder(res),
        });
        self.strategy.handle_event(event).await?;
        Ok(())
    }

    async fn handle_post_batch_order(
        &self,
        account_id: usize,
        req_id: u64,
        res: Result<quant_common::base::BatchOrderRsp>,
    ) -> Result<()> {
        let event = Event::ExecutionResult(ExecutionAsyncCommandResult {
            exchange: self.exchanges[account_id],
            account_id,
            context: Context::new(Some(req_id)),
            result: AsyncCmdResult::BatchPlaceOrder(res),
        });
        self.strategy.handle_event(event).await?;
        Ok(())
    }

    async fn handle_amend_order(
        &self,
        account_id: usize,
        req_id: u64,
        res: quant_common::base::AmendOrderResult,
    ) -> Result<()> {
        let event = Event::ExecutionResult(ExecutionAsyncCommandResult {
            exchange: self.exchanges[account_id],
            account_id,
            context: Context::new(Some(req_id)),
            result: AsyncCmdResult::AmendOrder(res),
        });
        self.strategy.handle_event(event).await?;
        Ok(())
    }

    async fn handle_cancel_order(
        &self,
        account_id: usize,
        req_id: u64,
        res: quant_common::base::CancelOrderResult,
    ) -> Result<()> {
        let event = Event::ExecutionResult(ExecutionAsyncCommandResult {
            exchange: self.exchanges[account_id],
            account_id,
            context: Context::new(Some(req_id)),
            result: AsyncCmdResult::CancelOrder(res),
        });
        self.strategy.handle_event(event).await?;
        Ok(())
    }
    async fn handle_batch_cancel_order(
        &self,
        account_id: usize,
        req_id: u64,
        res: Result<quant_common::base::BatchOrderRsp>,
    ) -> Result<()> {
        let event = Event::ExecutionResult(ExecutionAsyncCommandResult {
            exchange: self.exchanges[account_id],
            account_id,
            context: Context::new(Some(req_id)),
            result: AsyncCmdResult::BatchCancelOrder(res),
        });
        self.strategy.handle_event(event).await?;
        Ok(())
    }

    async fn handle_batch_cancel_order_by_ids(
        &self,
        account_id: usize,
        req_id: u64,
        res: Result<quant_common::base::BatchOrderRsp>,
    ) -> Result<()> {
        let event = Event::ExecutionResult(ExecutionAsyncCommandResult {
            exchange: self.exchanges[account_id],
            account_id,
            context: Context::new(Some(req_id)),
            result: AsyncCmdResult::BatchCancelOrderByIds(res),
        });
        self.strategy.handle_event(event).await?;
        Ok(())
    }
}
