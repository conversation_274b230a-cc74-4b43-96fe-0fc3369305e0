use algo_common::logger::tracing_layer::LogMsg;
use dashmap::DashMap;
use once_cell::sync::OnceCell;
use pyo3::PyErr;
use pyo3::PyResult;
use pyo3::exceptions::PyTypeError;
use pyo3::pyclass;
use std::{sync::Arc, time::Duration};
use tokio::{runtime::Handle, sync::mpsc::Receiver};
use trader::{
    cache::StaticCache,
    config::LogRateLimitConfig,
    dex::{DexManager, sync::DexSync},
    execution::StaticExecutionEngine,
};

// 导入WebClient
use super::nb8::WebClientWrapper;

/// Trader结构体
///
/// 核心交易者结构体，作为Python与Rust交互的主要接口
/// 负责管理交易执行引擎、DEX同步、缓存等核心组件
#[pyclass]
pub struct Trader {
    /// DEX管理器，负责处理去中心化交易所相关操作
    pub dex_manager: Arc<DexManager>,
    /// DEX同步器，负责同步链上和链下数据
    pub dex_sync: Arc<OnceCell<DexSync>>,
    /// 执行引擎，处理交易指令的执行
    pub execution: Arc<StaticExecutionEngine>,
    /// 超时设置，控制异步操作的最大等待时间
    pub time_out: Duration,
    /// 日志速率限制配置，防止日志过多导致性能问题
    pub rate_limit: LogRateLimitConfig,
    /// 缓存管理器，用于存储和恢复状态数据
    pub cache: Arc<StaticCache>,
    /// Tokio运行时句柄，用于在同步代码中执行异步操作
    pub handle: Handle,
    /// 日志接收器，用于接收系统产生的日志消息
    pub log_receiver: Option<Receiver<LogMsg>>,
    /// Web客户端实例，用于与Web后台通信
    pub web_client: Option<WebClientWrapper>,
    /// 日志时间戳，用于记录日志的时间戳
    pub log_timestamps: Arc<DashMap<String, i64>>,
}

impl Trader {
    /// 获取WebClient的不可变引用
    ///
    /// 如果WebClient未初始化，返回错误
    ///
    /// # 返回
    /// * 成功 - WebClient的不可变引用
    /// * 失败 - WebClient未初始化错误
    pub fn get_web_client(&self) -> PyResult<&WebClientWrapper> {
        if let Some(web_client) = &self.web_client {
            Ok(web_client)
        } else {
            Err(PyErr::new::<PyTypeError, _>("WebClient未初始化"))
        }
    }

    /// 获取WebClient
    ///
    /// 如果WebClient未初始化，返回错误
    ///
    /// # 返回
    /// * 成功 - WebClient的可变引用
    /// * 失败 - WebClient未初始化错误
    pub fn get_web_client_clone(&self) -> PyResult<WebClientWrapper> {
        if let Some(web_client) = &self.web_client {
            Ok(web_client.clone())
        } else {
            Err(PyErr::new::<PyTypeError, _>("WebClient未初始化"))
        }
    }

    /// 限频日志记录工具
    ///
    /// # 参数
    /// * `tag` - 日志标识
    /// * `interval` - 日志最小间隔（秒）
    /// * `query` - 是否只查询不更新时间
    ///
    /// # 返回
    /// * `bool` - true表示已记录日志，false表示未记录日志
    pub fn throttled_log<T>(&self, tag: T, interval: i64, query: bool) -> bool
    where
        T: AsRef<str> + Into<String>,
    {
        let tag_string = tag.into();
        let now = quant_common::time();

        match self.log_timestamps.entry(tag_string) {
            dashmap::mapref::entry::Entry::Occupied(mut entry) => {
                let last_time = *entry.get();
                let should_log = now - last_time >= interval;
                if should_log && !query {
                    entry.insert(now);
                }
                should_log
            }
            dashmap::mapref::entry::Entry::Vacant(entry) => {
                if !query {
                    entry.insert(now);
                }
                true
            }
        }
    }
}
