use pyo3::{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, prelude::*};
use pythonize::Depythonizer;
use quant_common::{Result, base::Exchange};
use serde::Deserialize;
use serde_json::Value;

pub fn py_serial<T: serde::ser::Serialize>(res: T) -> quant_common::Result<Value> {
    let v = serde_json::to_value(&res)?;
    Ok(v)
}

pub(crate) fn get_first_key(value: &Value) -> Option<&str> {
    if let Value::Object(map) = value {
        map.keys().next().map(|s| s.as_str())
    } else {
        None
    }
}

#[pyfunction]
pub fn create_cid(exchange: Bound<'_, PyAny>) -> PyResult<String> {
    let exchange: Exchange = depythonize(&exchange)
        .map_err(|e| {
            format!(
                "Failed to convert Python exchange to Rust exchange: {e:?}, py exchange: {exchange:?}"
            )
        })
        .map_err(PyErr::new::<PyAny, _>)?;
    Ok(exchange.create_cid(None))
}

pub fn depythonize<'a, T>(py_obj: &Bound<'a, PyAny>) -> Result<T>
where
    T: for<'de> Deserialize<'de>,
{
    let der = &mut Depythonizer::from_object(py_obj);
    let result = serde_path_to_error::deserialize(der)?;
    Ok(result)
}
