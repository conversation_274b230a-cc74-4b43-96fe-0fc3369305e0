use serde_json::Value;
use tokio;

use super::{
    Trader,
    utils::{get_first_key, py_serial},
};
use quant_common::base::traits::rest::Rest;
use quant_common::qerror;
use trader::{
    execution::ExecutionEngine,
    model::event::ex_command::{AsyncCommand, Command, ExecutionCommand, SyncCommand},
};

impl Trader {
    pub async fn handle_cmd(&self, cmd: ExecutionCommand) -> quant_common::Result<Value> {
        match cmd.cmd {
            Command::Sync(sync_command) => match sync_command {
                SyncCommand::Request(user_request) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.request(user_request).await
                        })
                        .await,
                ),
                SyncCommand::GetOrders((symbol, start, end)) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_orders(symbol, start, end).await
                        })
                        .await,
                ),
                SyncCommand::GetOrdersExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_orders_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::GetOpenOrders(symbol) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_open_orders(symbol).await
                        })
                        .await,
                ),
                SyncCommand::GetOpenOrdersExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_open_orders_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::GetAllOpenOrders => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_all_open_orders().await
                        })
                        .await,
                ),
                SyncCommand::GetAllOpenOrdersExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_all_open_orders_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::GetOrderById((symbol, id)) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_order_by_id(symbol, id).await
                        })
                        .await,
                ),
                SyncCommand::GetOrderByIdExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_order_by_id_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::PlaceOrder((order, params)) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.post_order(order, params).await
                        })
                        .await,
                ),
                SyncCommand::PlaceOrderExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.post_order_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::BatchPlaceOrder((orders, params)) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.post_batch_order(orders, params).await
                        })
                        .await,
                ),
                SyncCommand::BatchPlaceOrderExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.post_batch_order_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::AmendOrder(order) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.amend_order(order).await
                        })
                        .await,
                ),
                SyncCommand::AmendOrderExt(order) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.amend_order_ext(order).await
                        })
                        .await,
                ),
                SyncCommand::CancelOrder((order_id, symbol)) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.cancel_order(symbol, order_id).await
                        })
                        .await,
                ),
                SyncCommand::CancelOrderExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.cancel_order_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::BatchCancelOrder(symbol) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.batch_cancel_order(symbol).await
                        })
                        .await,
                ),
                SyncCommand::BatchCancelOrderExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.batch_cancel_order_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::BatchCancelOrderById((symbol, ids, cids)) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.batch_cancel_order_by_ids(symbol, ids, cids).await
                        })
                        .await,
                ),
                SyncCommand::BatchCancelOrderByIdExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.batch_cancel_order_by_ids_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::Position(symbol) => py_serial(match symbol {
                    Some(symbol) => {
                        self.execution
                            .execution_rest(cmd.account_id, move |rest| async move {
                                rest.get_position(symbol).await
                            })
                            .await
                    }
                    None => {
                        self.execution
                            .execution_rest(cmd.account_id, move |rest| async move {
                                rest.get_positions().await
                            })
                            .await
                    }
                }),
                SyncCommand::PositionExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_position_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::Positions => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_positions().await
                        })
                        .await,
                ),
                SyncCommand::PositionsExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_positions_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::MaxPosition((symbol, level)) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_max_position(symbol, level).await
                        })
                        .await,
                ),
                SyncCommand::MaxPositionExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_max_position_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::FundingFee((symbol, start, end)) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_funding_fee(symbol, start, end).await
                        })
                        .await,
                ),
                SyncCommand::FundingFeeExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_funding_fee_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::MaxLeverage(symbol) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_max_leverage(symbol).await
                        })
                        .await,
                ),
                SyncCommand::MaxLeverageExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_max_leverage_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::MarginMode((symbol, coin)) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_margin_mode(symbol, coin).await
                        })
                        .await,
                ),
                SyncCommand::MarginModeExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_margin_mode_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::SetMarginMode((symbol, coin, mode)) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.set_margin_mode(symbol, coin, mode).await
                        })
                        .await,
                ),
                SyncCommand::SetMarginModeExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.set_margin_mode_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::UsdtBalance => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_usdt_balance().await
                        })
                        .await,
                ),
                SyncCommand::UsdtBalanceExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_usdt_balance_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::Balance => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_balances().await
                        })
                        .await,
                ),
                SyncCommand::BalanceExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_balances_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::BalanceByCoin(coin) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_balance(&coin).await
                        })
                        .await,
                ),
                SyncCommand::BalanceByCoinExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_balance_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::FeeRate(symbol) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_fee_rate(symbol).await
                        })
                        .await,
                ),
                SyncCommand::FeeRateExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_fee_rate_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::SetLeverage((symbol, level)) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.set_leverage(symbol, level).await
                        })
                        .await,
                ),
                SyncCommand::SetLeverageExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.set_leverage_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::IsDualSidePosition => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.is_dual_side().await
                        })
                        .await,
                ),
                SyncCommand::IsDualSidePositionExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.is_dual_side_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::SetDualSidePosition(dual_side) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.set_dual_side(dual_side).await
                        })
                        .await,
                ),
                SyncCommand::SetDualSidePositionExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.set_dual_side_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::Transfer(transfer) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.transfer(transfer).await
                        })
                        .await,
                ),
                SyncCommand::TransferExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.transfer_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::SubTransfer(transfer) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.sub_transfer(transfer).await
                        })
                        .await,
                ),
                SyncCommand::SubTransferExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.sub_transfer_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::GetDepositAddress((coin, network, amount)) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_deposit_address(coin, network, amount).await
                        })
                        .await,
                ),
                SyncCommand::GetDepositAddressExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_deposit_address_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::Withdrawal(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.withdrawal(params).await
                        })
                        .await,
                ),
                SyncCommand::WithdrawalExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.withdrawal_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::GetAccountInfo => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_account_info().await
                        })
                        .await,
                ),
                SyncCommand::GetAccountInfoExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_account_info_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::GetAccountMode => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_account_mode().await
                        })
                        .await,
                ),
                SyncCommand::GetAccountModeExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_account_mode_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::SetAccountMode(mode) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.set_account_mode(mode).await
                        })
                        .await,
                ),
                SyncCommand::SetAccountModeExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.set_account_mode_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::GetUserId => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_user_id().await
                        })
                        .await,
                ),
                SyncCommand::GetUserIdExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_user_id_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::GetFeeDiscountInfo => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_fee_discount_info().await
                        })
                        .await,
                ),
                SyncCommand::GetFeeDiscountInfoExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_fee_discount_info_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::IsFeeDiscountEnabled => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.is_fee_discount_enabled().await
                        })
                        .await,
                ),
                SyncCommand::IsFeeDiscountEnabledExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.is_fee_discount_enabled_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::SetFeeDiscountEnabled(enabled) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.set_fee_discount_enabled(enabled).await
                        })
                        .await,
                ),
                SyncCommand::SetFeeDiscountEnabledExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.set_fee_discount_enabled_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::Borrow((coin, amount)) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.borrow_coin(coin, amount).await
                        })
                        .await,
                ),
                SyncCommand::BorrowExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.borrow_coin_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::GetBorrowed(coin) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_borrow(coin).await
                        })
                        .await,
                ),
                SyncCommand::GetBorrowedExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_borrow_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::Repay((coin, amount)) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.repay_coin(coin, amount).await
                        })
                        .await,
                ),
                SyncCommand::RepayExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.repay_coin_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::GetBorrowRate(coin) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_borrow_rate(coin).await
                        })
                        .await,
                ),
                SyncCommand::GetBorrowRateExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_borrow_rate_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::GetBorrowLimit((is_vip, coin)) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_borrow_limits(is_vip, coin).await
                        })
                        .await,
                ),
                SyncCommand::GetBorrowLimitExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_borrow_limits_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::Ticker(symbol) => match symbol {
                    Some(symbol) => py_serial(
                        self.execution
                            .execution_rest(cmd.account_id, move |rest| async move {
                                rest.get_ticker(symbol).await
                            })
                            .await,
                    ),
                    None => py_serial(
                        self.execution
                            .execution_rest(cmd.account_id, move |rest| async move {
                                rest.get_tickers().await
                            })
                            .await,
                    ),
                },
                SyncCommand::TickerExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_ticker_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::Tickers => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_tickers().await
                        })
                        .await,
                ),
                SyncCommand::TickersExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_tickers_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::Bbo(symbol) => match symbol {
                    Some(symbol) => py_serial(
                        self.execution
                            .execution_rest(cmd.account_id, move |rest| async move {
                                rest.get_bbo_ticker(symbol).await
                            })
                            .await,
                    ),
                    None => py_serial(
                        self.execution
                            .execution_rest(cmd.account_id, move |rest| async move {
                                rest.get_bbo_tickers().await
                            })
                            .await,
                    ),
                },
                SyncCommand::BboExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_bbo_ticker_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::BboTickers => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_bbo_tickers().await
                        })
                        .await,
                ),
                SyncCommand::BboTickersExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_bbo_tickers_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::Depth((symbol, limit)) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_depth(symbol, limit).await
                        })
                        .await,
                ),
                SyncCommand::DepthExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_depth_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::Instrument(symbol) => match symbol {
                    Some(symbol) => py_serial(
                        self.execution
                            .execution_rest(cmd.account_id, move |rest| async move {
                                rest.get_instrument(symbol).await
                            })
                            .await,
                    ),
                    None => py_serial(
                        self.execution
                            .execution_rest(cmd.account_id, move |rest| async move {
                                rest.get_instruments().await
                            })
                            .await,
                    ),
                },
                SyncCommand::InstrumentExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_instrument_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::Instruments => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_instruments().await
                        })
                        .await,
                ),
                SyncCommand::InstrumentsExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_instruments_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::FundingRate => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_funding_rates().await
                        })
                        .await,
                ),
                SyncCommand::FundingRateBySymbol(symbol) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_funding_rate(symbol).await
                        })
                        .await,
                ),
                SyncCommand::FundingRateBySymbolExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_funding_rate_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::FundingRateExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_funding_rates_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::MarkPrice(symbol) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_mark_price(symbol).await
                        })
                        .await,
                ),
                SyncCommand::MarkPriceExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_mark_price_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::KlineExt(params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_klines_ext(params).await
                        })
                        .await,
                ),
                SyncCommand::Dex(dex_command) => {
                    let dex_sync = self.dex_sync.get().unwrap();
                    dex_sync.set_sync(true);
                    let key = get_first_key(&dex_command)
                        .ok_or(qerror!("dex同步命令获取key失败, dex命令: {dex_command:?}"))?
                        .to_string();
                    match self
                        .dex_manager
                        .send_to_client(cmd.account_id, dex_command)
                        .await
                    {
                        Ok(_) => {
                            trace!("发送dex命令中...");
                        }
                        Err(e) => {
                            dex_sync.set_sync(false);
                            error!("发送dex命令失败: {}", e);
                            return Err(e);
                        }
                    }
                    match tokio::time::timeout(self.time_out, async {
                        // 持续接收直到找到匹配的key或超时
                        loop {
                            match dex_sync.rx.lock().await.recv().await {
                                Some(res) => {
                                    // 检查key是否匹配
                                    if res.0 == key {
                                        dex_sync.set_sync(false);
                                        return Ok(res.1);
                                    }
                                    // key不匹配则继续接收
                                    continue;
                                }
                                None => {
                                    dex_sync.set_sync(false);
                                    return Err(qerror!("接收dex命令响应失败,通道已关闭"));
                                }
                            }
                        }
                    })
                    .await
                    {
                        Ok(result) => result,
                        Err(_) => {
                            dex_sync.set_sync(false);
                            Err(qerror!("接收dex命令响应超时"))
                        }
                    }
                }
                SyncCommand::FundingRateHistory(get_funding_rate_history_params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_funding_rates_history_ext(get_funding_rate_history_params)
                                .await
                        })
                        .await,
                ),
                SyncCommand::PostStopOrder(post_stop_order_params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.post_stop_order(post_stop_order_params).await
                        })
                        .await,
                ),
                SyncCommand::GetStopOrders(get_stop_orders_params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_stop_orders(get_stop_orders_params).await
                        })
                        .await,
                ),
                SyncCommand::AmendStopOrder(amend_stop_order_params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.amend_stop_order(amend_stop_order_params).await
                        })
                        .await,
                ),
                SyncCommand::CancelStopOrder(cancel_stop_order_params) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.cancel_stop_order(cancel_stop_order_params).await
                        })
                        .await,
                ),
                SyncCommand::MarketCap(coin) => py_serial(
                    self.execution
                        .execution_rest(cmd.account_id, move |rest| async move {
                            rest.get_market_cap(coin).await
                        })
                        .await,
                ),
            },
            Command::Async(AsyncCommand::Dex(dex_cmd)) => {
                let res = self
                    .dex_manager
                    .send_to_client(cmd.account_id, dex_cmd)
                    .await;
                py_serial(res)
            }
            _ => {
                let res = self.execution.execution_async(cmd).await;
                py_serial(res)
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use quant_common::base::{
        Order,
        traits::{AmendOrderParams, CancelOrderParams, IsDualSideParams, SetDualSideParams},
    };

    use super::*;

    #[test]
    fn test_ext_cmd_serial() {
        let params = IsDualSideParams::new().extra_param("symbol".to_string(), "BTC_USDT");
        let cmd = SyncCommand::IsDualSidePositionExt(params);
        let res = serde_json::to_string_pretty(&cmd).unwrap();
        println!("{res}");

        let params = SetDualSideParams::new(true).extra_param("symbol".to_string(), "BTC_USDT");
        let cmd = SyncCommand::SetDualSidePositionExt(params);
        let res = serde_json::to_string_pretty(&cmd).unwrap();
        println!("{res}");

        let params =
            AmendOrderParams::new(Order::default()).extra_param("symbol".to_string(), "BTC_USDT");

        let cmd = SyncCommand::AmendOrderExt(params);
        let res = serde_json::to_string_pretty(&cmd).unwrap();
        println!("{res}");

        let params = CancelOrderParams::default().extra_param("symbol".to_string(), "BTC_USDT");
        let cmd = SyncCommand::CancelOrderExt(params);
        let res = serde_json::to_string_pretty(&cmd).unwrap();
        println!("{res}");
    }
}
