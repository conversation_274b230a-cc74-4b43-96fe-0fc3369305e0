use ::trader as trader_rs;
use pyo3::{Bound, PyErr, PyResult, Python, exceptions::PyTypeError, prelude::*, types::PyAny};
use pythonize::Depythonizer;
use pythonize::pythonize;
use quant_common::Result;
use quant_common::base::Exchange;
use serde_json::Value;
use std::collections::HashMap;
use std::str::FromStr;
use trader_rs::cache::Cache;
use trader_rs::model::event::ex_command::ExecutionCommand;

use super::nb8::WebClientWrapper;
use super::utils::depythonize;
use super::{core::Trader, utils::create_cid};

/// 为Trader结构体实现Python导出方法
/// 这些方法将被直接暴露给Python环境使用
#[pymethods]
impl Trader {
    /// 执行交易指令
    ///
    /// 接收Python传入的交易指令，反序列化后通过Rust引擎执行
    ///
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `cmd` - Python传入的命令对象
    ///
    /// # 返回
    /// * 指令执行结果，序列化为Python对象
    fn publish(&self, py: Python, cmd: Bound<'_, PyAny>) -> PyResult<Py<PyAny>> {
        // 将Python对象反序列化为Rust的ExecutionCommand结构
        let der = &mut Depythonizer::from_object(&cmd);
        let cmd: ExecutionCommand = serde_path_to_error::deserialize(der).map_err(|e| {
            PyTypeError::new_err(format!(
                "反序列化Python指令时出错, 错误字段: {e}, Python指令: {cmd:?}"
            ))
        })?;

        // 使用允许线程执行，防止阻塞Python GIL
        let v = py.allow_threads(move || {
            // 在Tokio运行时中执行指令
            self.handle
                .block_on(async move { self.handle_cmd(cmd).await })
                .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))
        })?;

        // 将Rust结果序列化为Python对象
        pythonize(py, &v)
            .map_err(|e| PyTypeError::new_err(e.to_string()))
            .map(Bound::unbind)
    }

    /// 批量执行交易指令
    ///
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `cmds` - 多个Python命令对象列表
    ///
    /// # 返回
    /// * 多个指令执行结果的列表
    fn batch_publish(&self, py: Python, cmds: Vec<Bound<'_, PyAny>>) -> PyResult<Vec<Py<PyAny>>> {
        let mut results = Vec::with_capacity(cmds.len());
        for cmd in cmds {
            results.push(self.publish(py, cmd)?);
        }
        Ok(results)
    }

    /// 记录日志到控制台和Python代码
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `msg` - 日志消息内容
    /// * `level` - 日志级别，可选，默认为INFO
    /// * `color` - 颜色，可选，默认为None
    /// * `web` - 是否发送到Web平台，可选，默认为true
    #[pyo3(signature = (msg, level=None, color=None, web=Some(true)))]
    pub fn log(
        &self,
        msg: &str,
        level: Option<&str>,
        color: Option<&str>,
        web: Option<bool>,
    ) -> PyResult<()> {
        // 设置默认值
        let level = level.unwrap_or("INFO");
        let web = web.unwrap_or(false);
        let msg = if let Some(color) = color {
            if web {
                WebClientWrapper::color_msg(msg, color)?
            } else {
                msg.to_string()
            }
        } else {
            msg.to_string()
        };

        // 定义日志记录宏，根据参数生成日志调用
        macro_rules! log_with_level {
            ($level:ident) => {
                if let Some(event) = None::<String> {
                    $level!(event = event, web = web, "{}", msg);
                } else {
                    $level!(web = web, "{}", msg);
                }
            };
        }

        // 根据日志级别调用对应的日志宏
        match level {
            "TRACE" | "trace" => log_with_level!(trace),
            "DEBUG" | "debug" => log_with_level!(debug),
            "INFO" | "info" => log_with_level!(info),
            "WARN" | "warn" => log_with_level!(warn),
            "ERROR" | "error" => log_with_level!(error),
            unknown => warn!("Unknown log level: {}", unknown),
        }
        Ok(())
    }

    /// 限频日志记录工具
    ///
    /// 每N秒最多打印一次指定标签的日志
    ///
    /// # 参数
    /// * `tag` - 日志标识
    /// * `msg` - 日志内容
    /// * `color` - 颜色，可选，默认为None
    /// * `interval` - 日志最小间隔（秒），默认为0
    /// * `level` - 日志级别，可选，默认为INFO
    /// * `query` - 是否只查询不更新时间，默认为false
    ///
    /// # 返回
    /// * `bool` - true表示已记录日志，false表示未记录日志
    #[allow(clippy::too_many_arguments)]
    #[pyo3(signature = (tag, msg, color=None, interval=0, level=None, query=false))]
    pub fn tlog(
        &self,
        tag: &str,
        msg: &str,
        color: Option<&str>,
        interval: i64,
        level: Option<&str>,
        query: bool,
    ) -> PyResult<bool> {
        // 检查是否应该记录日志
        let should_proceed = self.throttled_log(tag, interval, query);

        let web = Some(should_proceed);

        // 使用log方法记录日志
        self.log(msg, level, color, web)?;

        // 返回是否记录了日志
        Ok(should_proceed)
    }

    /// 指定时间记录日志
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `message` - 日志内容
    /// * `color` - 颜色，可选，默认为None
    /// * `time` - 日志时间
    /// * `level` - 日志级别
    /// * `web` - 是否发送到Web平台
    ///
    #[pyo3(signature = (message, time, color=None, level=None))]
    pub fn logt(
        &self,
        message: &str,
        time: i64,
        color: Option<&str>,
        level: Option<&str>,
    ) -> PyResult<()> {
        let level = level.unwrap_or("INFO");
        let web_client = self.get_web_client()?;
        let handle = self.handle.clone();
        let msg = if let Some(color) = color {
            WebClientWrapper::color_msg(message, color)?
        } else {
            message.to_string()
        };
        handle.block_on(async move { web_client.log_internal(&msg, level, Some(time)).await })
    }

    /// 保存缓存数据
    ///
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `data` - 要保存的数据字符串
    fn cache_save(&self, py: Python, data: &str) -> PyResult<()> {
        py.allow_threads(move || {
            self.cache
                .save(data.to_string())
                .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))
        })
    }

    /// 更新缓存数据
    ///
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `data` - 要更新的数据字符串
    fn cache_update(&self, py: Python, data: &str) -> PyResult<()> {
        py.allow_threads(move || {
            self.cache
                .update(data.to_string())
                .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))
        })
    }

    /// 加载缓存数据
    ///
    /// # 参数
    /// * `py` - Python解释器引用
    ///
    /// # 返回
    /// * 缓存数据字符串，如果没有则返回None
    fn cache_load(&self, py: Python) -> PyResult<Option<String>> {
        py.allow_threads(move || {
            self.cache
                .load()
                .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))
        })
    }

    /// 创建cid
    /// # 参数
    /// * `exchange` - 交易所名称
    /// # 返回
    /// * 创建的cid
    fn create_cid(&self, exchange: Bound<'_, PyAny>) -> PyResult<String> {
        let exchange: Exchange = depythonize(&exchange)
        .map_err(|e| {
            format!(
                "Failed to convert Python exchange to Rust exchange: {e:?}, py exchange: {exchange:?}"
            )
        })
        .map_err(PyErr::new::<PyAny, _>)?;
        Ok(exchange.create_cid(None))
    }

    /// 发送HTTP请求
    ///
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `url` - 请求URL
    /// * `method` - 请求方法（GET, POST等）
    /// * `body` - 请求体，可选
    /// * `headers` - 请求头，可选
    ///
    /// # 返回
    /// * 响应数据，转换为Python对象
    #[pyo3(signature = (url, method, body, headers=None))]
    fn request(
        &self,
        py: Python,
        url: &str,
        method: &str,
        body: Option<&str>,
        headers: Option<HashMap<String, String>>,
    ) -> PyResult<Py<PyAny>> {
        let res = py.allow_threads(move || {
            // 准备请求参数
            let headers = headers.unwrap_or_default();
            let method = method.to_string();
            let url = url.to_string();

            // 解析HTTP方法
            let method = reqwest::Method::from_str(&method)
                .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?;

            // 转换头信息为reqwest格式
            let headers =
                reqwest::header::HeaderMap::from_iter(headers.into_iter().map(|(k, v)| {
                    (
                        reqwest::header::HeaderName::from_str(&k).unwrap(),
                        reqwest::header::HeaderValue::from_str(&v).unwrap(),
                    )
                }));
            let body = body.unwrap_or("").to_string();

            // 创建HTTP客户端并发送请求
            let client = reqwest::Client::new();

            let res: Result<Value> = self.handle.block_on(async move {
                let res = client
                    .request(method, url)
                    .headers(headers)
                    .body(body)
                    .send()
                    .await?;
                let body = res.json::<Value>().await?;
                Ok(body)
            });
            res
        });
        pythonize(py, &res)
            .map_err(|e| PyTypeError::new_err(e.to_string()))
            .map(Bound::unbind)
    }

    /// 创建并初始化WebClient
    ///
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `config` - WebClient配置对象
    fn init_web_client(&mut self, _py: Python, config: Bound<'_, PyAny>) -> PyResult<()> {
        let web_client = WebClientWrapper::new(&config)?;
        self.web_client = Some(web_client);
        Ok(())
    }

    /// 启动WebClient
    ///
    /// 激活自动上传功能
    ///
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `upload_interval` - 上传间隔（秒），可选，默认为5
    #[pyo3(signature = (upload_interval=None))]
    fn start_web_client(&mut self, py: Python, upload_interval: Option<i32>) -> PyResult<()> {
        let receiver = self.log_receiver.take();
        let mut web_client = self.get_web_client_clone()?;
        web_client.start(py, upload_interval, receiver)
    }

    /// 停止WebClient
    ///
    /// 停止自动上传功能
    ///
    /// # 参数
    /// * `py` - Python解释器引用
    fn stop_web_client(&self) -> PyResult<()> {
        let web_client = self.get_web_client()?;
        web_client.stop_py()
    }

    /// 设置实盘强停状态
    /// # 参数
    /// * `force_stop` - 是否强停
    fn set_force_stop(&self, force_stop: bool) -> PyResult<()> {
        let web_client = self.get_web_client()?;
        web_client.set_force_stop(force_stop);
        Ok(())
    }

    /// 检查策略是否被标记为强停/暂停交易
    ///
    /// # 参数
    /// * `py` - Python解释器引用
    ///
    /// # 返回
    /// * 布尔值，表示是否强停/暂停交易
    fn is_web_force_stopped(&self, py: Python) -> PyResult<bool> {
        let web_client = self.get_web_client()?;
        web_client.is_stopped(py)
    }

    /// 检查交易是否被缓停
    ///
    /// # 参数
    /// * `py` - Python解释器引用
    ///
    /// # 返回
    /// * 布尔值，表示交易是否被缓停
    fn is_web_soft_stopped(&self, py: Python) -> PyResult<bool> {
        let web_client = self.get_web_client()?;
        web_client.is_trading_disabled(py)
    }

    /// 检查是否停止开仓
    ///
    /// # 参数
    /// * `py` - Python解释器引用
    ///
    /// # 返回
    /// * 布尔值，表示是否停止开仓
    fn is_web_opening_stopped(&self, py: Python) -> PyResult<bool> {
        let web_client = self.get_web_client()?;
        web_client.is_opening_disabled(py)
    }

    /// 检查是否强平
    ///
    /// # 参数
    /// * `py` - Python解释器引用
    ///
    /// # 返回
    /// * 布尔值，表示是否强平
    fn is_web_force_closing(&self, py: Python) -> PyResult<bool> {
        let web_client = self.get_web_client()?;
        web_client.is_closing_enabled(py)
    }

    /// 更新余额信息
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `primary_balance` - 主所余额
    /// * `secondary_balance` - 次所余额（可选）
    /// * `available_primary` - 主所可用余额（可选）
    /// * `available_secondary` - 次所可用余额（可选）
    #[pyo3(signature = (primary_balance, secondary_balance=None, available_primary=None, available_secondary=None))]
    fn update_total_balance(
        &self,
        py: Python,
        primary_balance: f64,
        secondary_balance: Option<f64>,
        available_primary: Option<f64>,
        available_secondary: Option<f64>,
    ) -> PyResult<()> {
        let web_client = self.get_web_client()?;
        web_client.update_total_balance(
            py,
            primary_balance,
            secondary_balance,
            available_primary,
            available_secondary,
        )
    }

    /// 更新所有节点持仓价值
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `total_value` - 总价值
    /// * `long_position_value` - 多头持仓价值
    /// * `short_position_value` - 空头持仓价值
    fn update_total_position_value(
        &self,
        py: Python,
        total_value: f64,
        long_position_value: f64,
        short_position_value: f64,
    ) -> PyResult<()> {
        let web_client = self.get_web_client()?;
        web_client.update_total_position_value(
            py,
            total_value,
            long_position_value,
            short_position_value,
        )
    }

    /// 更新当前节点持仓价值
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `total_value` - 总价值
    fn update_current_position_value(
        &self,
        py: Python,
        total_value: f64,
        long_position_value: f64,
        short_position_value: f64,
    ) -> PyResult<()> {
        let web_client = self.get_web_client()?;
        web_client.update_current_position_value(
            py,
            total_value,
            long_position_value,
            short_position_value,
        )
    }

    /// 添加资金费
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `primary_fee` - 主所资金费（可选）
    /// * `secondary_fee` - 次所资金费（可选）
    #[pyo3(signature = (primary_fee=None, secondary_fee=None))]
    fn add_funding_fee(
        &self,
        py: Python,
        primary_fee: Option<f64>,
        secondary_fee: Option<f64>,
    ) -> PyResult<()> {
        let web_client = self.get_web_client()?;
        web_client.add_funding_fee(py, primary_fee, secondary_fee)
    }

    /// 更新预测资金费
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `primary_fee` - 主所预测资金费（可选）
    /// * `secondary_fee` - 次所预测资金费（可选）
    #[pyo3(signature = (primary_fee=None, secondary_fee=None))]
    fn update_pred_funding(
        &self,
        py: Python,
        primary_fee: Option<f64>,
        secondary_fee: Option<f64>,
    ) -> PyResult<()> {
        let web_client = self.get_web_client()?;
        web_client.update_predicted_funding_fee(py, primary_fee, secondary_fee)
    }

    /// 更新浮动盈亏
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `floating_profit` - 浮动盈亏
    fn update_floating_profit(&self, py: Python, floating_profit: f64) -> PyResult<()> {
        let web_client = self.get_web_client()?;
        web_client.update_floating_profit(py, floating_profit)
    }

    /// 更新交易统计
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `maker_volume` - 挂单量
    /// * `taker_volume` - 吃单量
    /// * `profit` - 利润
    /// * `is_single_close` - 是否单腿平仓
    #[pyo3(signature = (maker_volume = 0., taker_volume = 0., profit = 0., is_single_close=false))]
    fn update_trade_stats(
        &self,
        py: Python,
        maker_volume: f64,
        taker_volume: f64,
        profit: f64,
        is_single_close: bool,
    ) -> PyResult<f64> {
        let web_client = self.get_web_client()?;
        let trade_volume = maker_volume + taker_volume;
        web_client.update_trade_stats(
            py,
            trade_volume,
            Some(maker_volume),
            Some(taker_volume),
            profit,
            is_single_close,
        )
    }

    /// 上传多个表格数据
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `tables` - 表格数据列表
    fn upload_tables(&self, py: Python, tables: &Bound<'_, PyAny>) -> PyResult<()> {
        let web_client = self.get_web_client()?;
        web_client.upload_tables(py, tables)
    }

    /// 记录利润
    /// # 参数
    /// * `py` - Python解释器引用
    /// * `profit` - 利润值
    fn log_profit(&self, py: Python, profit: f64) -> PyResult<()> {
        let web_client = self.get_web_client()?;
        web_client.log_profit(py, profit)
    }

    /// 获取统计数据
    ///
    /// 返回当前统计数据，使用原始字段名（非序列化别名）
    ///
    /// # 参数
    /// * `py` - Python解释器引用
    ///
    /// # 返回
    /// * 包含统计数据的Python字典对象
    fn get_stats(&self, py: Python) -> PyResult<Py<PyAny>> {
        // 检查WebClient是否已初始化
        let web_client = self.get_web_client()?;

        // 获取StatsManager并生成JSON
        let stats_json = py
            .allow_threads(move || {
                let fut = async {
                    let stats_manager = web_client.stats_manager.lock().await;
                    serde_json::to_value(&*stats_manager)
                };
                self.handle.block_on(fut)
            })
            .map_err(|e| PyErr::new::<PyAny, _>(e.to_string()))?;

        // 将JSON转换为Python对象
        pythonize(py, &stats_json)
            .map_err(|e| {
                PyErr::new::<pyo3::exceptions::PyValueError, _>(format!(
                    "Failed to pythonize stats: {e}"
                ))
            })
            .map(Bound::unbind)
    }
}

/// Python模块定义
///
/// 注册Trader类和其他工具函数到Python模块中
#[pymodule]
pub fn traderv1(m: &Bound<'_, PyModule>) -> PyResult<()> {
    // 注册Trader类
    m.add_class::<Trader>()?;
    // 注册创建CID的函数
    m.add_function(wrap_pyfunction!(create_cid, m)?)?;
    Ok(())
}
