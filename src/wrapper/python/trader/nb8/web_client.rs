use algo_common::logger::tracing_layer::LogMsg;
use futures::executor::block_on;
use pyo3::exceptions::PyValueError;
use pyo3::prelude::*;
use pythonize::depythonize;
use serde::{Deserialize, Serialize};
use serde_json::{Value, json};
use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};
use std::time::Duration;
use tokio::select;
use tokio::sync::Mutex;
use tokio::sync::mpsc::Receiver;
use tracing::{self, Level, debug, info};

use crate::web::client::{WebClient, get_global_http_client_ref};
use crate::wrapper::python::trader::nb8::clickhouse_client::{ClickHouseClient, ClickHouseConfig};
use crate::wrapper::python::trader::nb8::config::Config;
use crate::wrapper::python::trader::nb8::logger::Logger;
use crate::wrapper::python::trader::nb8::stats::StatsManager;

/// 用于任务的WebClient简化版本，避免循环引用
struct WebClientForTask {
    _server_name: String,
    logger: Arc<Mutex<Logger>>,
    stats_manager: Arc<Mutex<StatsManager>>,
    _http_client: Arc<WebClient>,
    config: Arc<Config>,
    clickhouse_client: Option<Arc<ClickHouseClient>>,
}

impl WebClientForTask {
    async fn upload_data(&self, mut stats: Value) -> PyResult<()> {
        // 上传日志
        let mut logger = self.logger.lock().await;
        if logger.force_stop {
            stats["stats"] = Value::String("账户强停".to_string());
        }
        let result = logger.upload_logs_internal(stats).await?;
        if let Some(result) = result {
            self.parse_response(result);
        }

        // 写入ClickHouse（如果启用）
        if let Some(clickhouse_client) = &self.clickhouse_client {
            let stats_manager = self.stats_manager.lock().await;
            if let Err(e) = clickhouse_client
                .insert_complete_stats(&*stats_manager)
                .await
            {
                tracing::warn!("ClickHouse写入失败: {}", e);
            }
        }

        // 如果没有日志或上传失败，返回成功
        Ok(())
    }

    fn parse_response(&self, response: Value) -> Option<()> {
        let stop = response.get("stop")?.as_bool()?;
        let stop_trade = response.get("stopTrade")?.as_bool()?;
        let stop_kai = response.get("stopKai")?.as_bool()?;
        let ping_cang = response.get("pingcang")?.as_bool()?;
        let _hua_zhuan = response.get("huazhuan")?.as_f64()?;

        let edit_yuer = response.get("editYuer")?.as_f64()?;

        // 这里需要异步获取初始余额，但 parse_response 是同步的
        // 我们暂时无法直接在这里使用 await，所以使用阻塞操作
        // 在实际使用中，应该重构这部分逻辑以使其完全异步
        let initial_balance = block_on(async { self.stats_manager.lock().await.initial_balance() });

        if edit_yuer != initial_balance {
            block_on(async {
                self.stats_manager
                    .lock()
                    .await
                    .update_initial_balance(edit_yuer);
            });
        }

        if stop {
            self.config.set_stopped(true);
        }

        if stop_trade {
            self.config.set_trade_stopped(true);
        }

        if stop_kai {
            self.config.set_opening_disabled(true);
        }

        if ping_cang {
            self.config.set_closing_enabled(true);
        }

        if response.get("kill")?.as_bool()? {
            info!("收到kill命令，正在关闭所有screen会话");
            std::process::exit(0);
        }

        if response.get("reboot")?.as_bool()? {
            info!("云端重启");
            std::process::exit(0);
        }
        Some(())
    }

    /// 检查并重置当日统计数据
    async fn check_and_reset_today_stats(&self) {
        let mut stats_manager = self.stats_manager.lock().await;
        stats_manager.check_and_reset_today_stats();
    }
}

/// Web客户端配置
#[derive(Clone, Serialize, Deserialize, Debug)]
pub struct WebClientWrapperConfig {
    /// 服务器名称
    pub server_name: Option<String>,
    /// 主所初始余额
    pub primary_balance: Option<f64>,
    /// 次所初始余额
    pub secondary_balance: Option<f64>,
    /// 开仓阈值，如0.14代表千1.4价差开仓
    pub open_threshold: Option<f64>,
    /// 单个币种最大持仓比例，如100代表100%，1x杠杆
    pub max_position_ratio: Option<f64>,
    /// 最大可开杠杆
    pub max_leverage: Option<i32>,
    /// 资金费率阈值，如0.1代表千1资金费率开仓
    pub funding_rate_threshold: Option<f64>,
    /// 成本 主所开平 副所开平，4次交易手续费，如0.0824 代表万8.24
    pub cost: Option<f64>,
    /// 主所maker手续费率
    pub primary_maker_fee_rate: Option<f64>,
    /// 主所taker手续费率
    pub primary_taker_fee_rate: Option<f64>,
    /// 主所返佣率，例如 0.0002 代表万2
    pub primary_rebate_rate: Option<f64>,
    /// 次所maker手续费率
    pub secondary_maker_fee_rate: Option<f64>,
    /// 次所taker手续费率
    pub secondary_taker_fee_rate: Option<f64>,
    /// 次所返佣率
    pub secondary_rebate_rate: Option<f64>,
    /// ClickHouse配置
    pub clickhouse_url: Option<String>,
    pub clickhouse_database: Option<String>,
    pub clickhouse_username: Option<String>,
    pub clickhouse_password: Option<String>,
    pub clickhouse_enabled: Option<bool>,
}

/// Web客户端类，用于向Web发送策略信息
#[derive(Clone)]
pub struct WebClientWrapper {
    pub server_name: String,
    pub config: Arc<Config>,
    pub http_client: Arc<WebClient>,
    pub logger: Arc<Mutex<Logger>>,
    pub stats_manager: Arc<Mutex<StatsManager>>,
    pub is_stopped: Arc<AtomicBool>,
    pub auto_upload_enabled: Arc<AtomicBool>,
    pub upload_interval: i32,
    pub upload_task_handle: Arc<Mutex<Option<tokio::task::JoinHandle<()>>>>,
    pub clickhouse_client: Option<Arc<ClickHouseClient>>,
}

impl WebClientWrapper {
    // 记录日志的内部方法
    pub async fn log_internal(
        &self,
        message: &str,
        level: &str,
        time: Option<i64>,
    ) -> PyResult<()> {
        let mut logger = self.logger.lock().await;
        logger.log_internal(message, Some(level), None, time)
    }

    // 上传数据的内部方法
    async fn upload_data_internal(&self) -> PyResult<Value> {
        // 上传日志
        let stats_json = {
            let stats = self.stats_manager.lock().await;
            stats.to_alias_json()
        };

        let mut logger = self.logger.lock().await;
        if let Ok(Some(result)) = logger.upload_logs_internal(stats_json).await {
            return Ok(result);
        }

        // 如果没有日志或上传失败，返回成功状态
        Ok(json!({"status": "success"}))
    }

    /// 启用自动上传 - 使用Tokio异步实现
    pub async fn enable_auto_upload_async(
        &mut self,
        interval: Option<i32>,
        log_receiver: Option<Receiver<LogMsg>>,
    ) -> PyResult<()> {
        if self.auto_upload_enabled.load(Ordering::Acquire) {
            return Ok(());
        }

        let interval = interval.unwrap_or(5);
        self.upload_interval = interval;
        self.auto_upload_enabled.store(true, Ordering::Relaxed);
        self.is_stopped.store(false, Ordering::Relaxed);

        // 克隆需要在任务中使用的对象
        let web_client_server_name = self.server_name.clone();
        let stopped = self.is_stopped.clone();
        let interval_secs = interval as u64;

        // 创建一个简化的WebClient以避免循环引用
        let web_client_for_task = WebClientForTask {
            _server_name: self.server_name.clone(),
            logger: self.logger.clone(),
            stats_manager: self.stats_manager.clone(),
            _http_client: self.http_client.clone(),
            config: self.config.clone(),
            clickhouse_client: self.clickhouse_client.clone(),
        };

        let mut log_receiver = log_receiver.ok_or(
            PyErr::new::<pyo3::exceptions::PyValueError, _>("log_receiver is None"),
        )?;

        // 使用 tokio 运行时执行异步任务
        let runtime = tokio::runtime::Handle::current();
        let task = runtime.spawn(async move {
            let sleep_time = Duration::from_secs(interval_secs);
            let mut auto_upload_ticker = tokio::time::interval(Duration::from_millis(100));
            // 每小时检查一次日期变更的定时器
            let mut daily_reset_checker = tokio::time::interval(Duration::from_secs(3600)); // 3600秒 = 1小时

            let is_sleep = Arc::new(AtomicBool::new(false));

            while !stopped.load(Ordering::SeqCst) {
                select! {
                    _ = auto_upload_ticker.tick() => {
                        if is_sleep.load(Ordering::Acquire) {
                            continue;
                        }
                        // 检查并重置今日数据
                        web_client_for_task.check_and_reset_today_stats().await;

                        // 尝试上传数据
                        let stats = {
                            let stats_manager = web_client_for_task.stats_manager.lock().await;
                            stats_manager.to_alias_json()
                        };

                        if let Err(e) = web_client_for_task.upload_data(stats).await {
                            tracing::error!(web = false, "自动上传错误 ({}): {:?}", web_client_server_name, e);
                        }

                        let is_sleep = is_sleep.clone();
                        tokio::spawn( async move {
                            is_sleep.store(true, Ordering::Release);
                            tokio::time::sleep(sleep_time).await;
                            is_sleep.store(false, Ordering::Release);
                        });
                    }
                    _ = daily_reset_checker.tick() => {
                        // 每小时强制检查一次日期变更，确保即使没有交易活动也能正确重置
                        tracing::debug!("执行定时日期变更检查");
                        web_client_for_task.check_and_reset_today_stats().await;
                    }
                    Some(log_msg) = log_receiver.recv() => {
                        // tracing::trace!(web=false, "收到日志: {:?}", log_msg);
                        match log_msg.level {
                            Level::ERROR => {
                                let mut logger = web_client_for_task.logger.lock().await;
                                if let Err(e) = logger.upload_error_internal(&log_msg.message).await {
                                    eprintln!("上传错误 ({web_client_server_name}): {e:?}");
                                    // tracing::error!(web = false, "上传错误 ({}): {:?}", web_client_server_name, e);
                                }
                                let red_msg = Self::color_msg(&log_msg.message, "red").unwrap_or_else(|e| e.to_string());
                                if let Err(e) = logger.log_internal(&red_msg, Some(&log_msg.level.to_string()), None, None) {
                                    eprintln!("上传错误 ({web_client_server_name}): {e:?}");
                                    // tracing::error!(web = false, "上传错误 ({}): {:?}", web_client_server_name, e);
                                }
                            }
                            _ => {
                                let mut logger = web_client_for_task.logger.lock().await;
                                if let Err(e) = logger.log_internal(&log_msg.message, Some(&log_msg.level.to_string()), None, None) {
                                    eprintln!("上传错误 ({web_client_server_name}): {e:?}");
                                    // tracing::error!(web = false, "上传错误 ({}): {:?}", web_client_server_name, e);
                                }
                            }
                        }
                    }
                }
            }

            // 收到停止信号，log_receiver持续接受 知道接收不到日志超过2s
            let mut timeout_break = tokio::time::interval(Duration::from_secs(2));
            loop {
                select! {
                    _ = timeout_break.tick() => {
                        break;
                    }
                    Some(log_msg) = log_receiver.recv() => {
                        timeout_break.reset();
                        match log_msg.level {
                            Level::ERROR => {
                                let mut logger = web_client_for_task.logger.lock().await;
                                if let Err(e) = logger.upload_error_internal(&log_msg.message).await {
                                    tracing::error!(web = false, "上传错误 ({}): {:?}", web_client_server_name, e);
                                }
                            }
                            _ => {
                                let mut logger = web_client_for_task.logger.lock().await;
                                if let Err(e) = logger.log_internal(&log_msg.message, Some(&log_msg.level.to_string()), None, None) {
                                    tracing::error!(web = false, "上传错误 ({}): {:?}", web_client_server_name, e);
                                }
                            }
                        }
                         // 尝试上传数据
                         let stats = {
                            let stats_manager = web_client_for_task.stats_manager.lock().await;
                            stats_manager.to_alias_json()
                        };

                        if let Err(e) = web_client_for_task.upload_data(stats).await {
                            tracing::error!(web = false, "自动上传错误 ({}): {:?}", web_client_server_name, e);
                        }
                    }
                }
            }
            tracing::info!("上传任务已停止 ({})", web_client_server_name);
        });

        // 保存任务句柄
        let mut task_handle = self.upload_task_handle.lock().await;
        *task_handle = Some(task);

        Ok(())
    }

    /// Python接口：启用自动上传
    pub fn enable_auto_upload(
        &mut self,
        py: Python,
        interval: Option<i32>,
        log_receiver: Option<Receiver<LogMsg>>,
    ) -> PyResult<()> {
        let runtime = tokio::runtime::Handle::current();
        py.allow_threads(|| {
            runtime.block_on(async { self.enable_auto_upload_async(interval, log_receiver).await })
        })
    }

    /// 禁用自动上传
    pub async fn disable_auto_upload(&self) -> PyResult<()> {
        if !self.auto_upload_enabled.load(Ordering::Acquire) {
            return Ok(());
        }

        self.auto_upload_enabled.store(false, Ordering::Relaxed);
        self.is_stopped.store(true, Ordering::Relaxed);

        // 等待任务结束
        let mut task_handle = self.upload_task_handle.lock().await;
        if let Some(handle) = task_handle.take() {
            // 尝试取消任务
            handle.abort();
        }

        // 添加日志
        self.log_internal("已禁用自动上传", "INFO", None).await?;
        Ok(())
    }

    /// Python接口：禁用自动上传
    pub fn disable_auto_upload_py(&self, py: Python) -> PyResult<()> {
        let runtime = tokio::runtime::Handle::current();
        py.allow_threads(|| runtime.block_on(async { self.disable_auto_upload().await }))
    }

    /// 停止上传服务
    pub async fn stop(&self) -> PyResult<()> {
        // 禁用自动上传
        self.disable_auto_upload().await?;

        // 添加停止日志
        self.log_internal("服务已停止", "INFO", None).await?;

        // 确保最后一次上传
        if let Err(e) = self.upload_data_internal().await {
            tracing::error!("最终上传失败: {:?}", e);
        }

        Ok(())
    }

    /// Python接口：停止上传服务
    pub fn stop_py(&self) -> PyResult<()> {
        let runtime = tokio::runtime::Handle::current();
        runtime.block_on(async { self.stop().await })
    }

    /// 异步创建WebClient实例
    pub async fn new_async(
        config: &Bound<'_, PyAny>,
        http_client: Arc<WebClient>,
    ) -> PyResult<Self> {
        let config: WebClientWrapperConfig = depythonize(config)?;

        // 从配置中提取参数，提供默认值
        let server_name = config.server_name.unwrap_or_else(|| "unknown".to_string());
        let primary_balance = config.primary_balance.unwrap_or(0.0);
        let secondary_balance = config.secondary_balance.unwrap_or(0.0);
        let open_threshold = config.open_threshold.unwrap_or(0.0);
        let max_position_ratio = config.max_position_ratio.unwrap_or(0.0);
        let max_leverage = config.max_leverage.unwrap_or(0);
        let funding_rate_threshold = config.funding_rate_threshold.unwrap_or(0.0);
        let cost = config.cost.unwrap_or(0.0);
        let primary_maker_fee_rate = config.primary_maker_fee_rate.unwrap_or(0.0);
        let primary_taker_fee_rate = config.primary_taker_fee_rate.unwrap_or(0.0);
        let primary_rebate_rate = config.primary_rebate_rate.unwrap_or(0.0);
        let secondary_maker_fee_rate = config.secondary_maker_fee_rate.unwrap_or(0.0);
        let secondary_taker_fee_rate = config.secondary_taker_fee_rate.unwrap_or(0.0);
        let secondary_rebate_rate = config.secondary_rebate_rate.unwrap_or(0.0);

        // ClickHouse配置
        let clickhouse_enabled = config.clickhouse_enabled.unwrap_or(false);
        let clickhouse_url = config
            .clickhouse_url
            .unwrap_or_else(|| "http://localhost:8123".to_string());
        let clickhouse_database = config
            .clickhouse_database
            .unwrap_or_else(|| "trading".to_string());
        let clickhouse_username = config.clickhouse_username;
        let clickhouse_password = config.clickhouse_password;

        debug!(
            "Initial balance: {}, Secondary initial balance: {}, Position roi: {}, Position ratio: {}, Max leverage: {}, Funding rate roi: {}, Primary fee rates: {}/{}, Secondary fee rates: {}/{}, ClickHouse enabled: {}",
            primary_balance,
            secondary_balance,
            open_threshold,
            max_position_ratio,
            max_leverage,
            funding_rate_threshold,
            primary_maker_fee_rate,
            primary_taker_fee_rate,
            secondary_maker_fee_rate,
            secondary_taker_fee_rate,
            clickhouse_enabled
        );

        // 创建配置对象
        let rust_config = Arc::new(Config::new(server_name.clone()));

        // 创建统计数据和表格管理器
        let stats_manager = Arc::new(Mutex::new(StatsManager::new(
            server_name.clone(),
            primary_balance,
            secondary_balance,
            open_threshold,
            max_position_ratio,
            max_leverage,
            funding_rate_threshold,
            cost,
            primary_maker_fee_rate,
            primary_taker_fee_rate,
            primary_rebate_rate,
            secondary_maker_fee_rate,
            secondary_taker_fee_rate,
            secondary_rebate_rate,
        )));

        // 启动时检查并重置当日数据
        {
            let mut stats = stats_manager.lock().await;
            stats.check_and_reset_today_stats();
            stats.validate_and_fix_consistency();
        }

        // 创建logger
        let logger = Arc::new(Mutex::new(Logger::new(
            rust_config.clone(),
            stats_manager.lock().await.time,
            http_client.clone(),
        )));

        // 创建ClickHouse客户端（如果启用）
        let clickhouse_client = if clickhouse_enabled {
            let clickhouse_config = ClickHouseConfig {
                url: clickhouse_url,
                database: clickhouse_database,
                username: clickhouse_username,
                password: clickhouse_password,
                stats_table: "stats_manager".to_string(),
                today_stats_table: "today_stats".to_string(),
            };

            match ClickHouseClient::new(clickhouse_config) {
                Ok(client) => {
                    let client = Arc::new(client);
                    // 尝试测试连接和创建表
                    if let Err(e) = client.test_connection().await {
                        tracing::warn!("ClickHouse连接测试失败: {}", e);
                    } else {
                        if let Err(e) = client.create_tables().await {
                            tracing::warn!("ClickHouse表创建失败: {}", e);
                        } else {
                            tracing::info!("ClickHouse客户端初始化成功");
                        }
                    }
                    Some(client)
                }
                Err(e) => {
                    tracing::error!("ClickHouse客户端创建失败: {}", e);
                    None
                }
            }
        } else {
            None
        };

        Ok(WebClientWrapper {
            server_name,
            config: rust_config,
            http_client,
            logger,
            stats_manager,
            is_stopped: Arc::new(AtomicBool::new(false)),
            auto_upload_enabled: Arc::new(AtomicBool::new(false)),
            upload_interval: 100,
            upload_task_handle: Arc::new(Mutex::new(None)),
            clickhouse_client,
        })
    }

    /// Python接口：创建WebClient实例
    pub fn new(config: &Bound<'_, PyAny>) -> PyResult<Self> {
        let http_client = get_global_http_client_ref().unwrap();
        let runtime = tokio::runtime::Handle::current();
        runtime.block_on(async { Self::new_async(config, http_client).await })
    }

    /// Python接口：设置实盘强停状态
    pub fn set_force_stop(&self, force_stop: bool) {
        let runtime = tokio::runtime::Handle::current();
        self.config.is_stopped.store(force_stop, Ordering::Relaxed);
        runtime.block_on(async {
            let mut logger = self.logger.lock().await;
            logger.force_stop = force_stop;
        })
    }

    /// Python接口：是否已停止
    pub fn is_stopped(&self, _py: Python) -> PyResult<bool> {
        Ok(self.config.is_stopped_internal())
    }

    /// Python接口：是否禁用交易
    pub fn is_trading_disabled(&self, _py: Python) -> PyResult<bool> {
        Ok(self.config.is_trade_stopped_internal())
    }

    /// Python接口：是否禁用开仓
    pub fn is_opening_disabled(&self, _py: Python) -> PyResult<bool> {
        Ok(self.config.is_opening_disabled_internal())
    }

    /// Python接口：是否允许平仓
    pub fn is_closing_enabled(&self, _py: Python) -> PyResult<bool> {
        Ok(self.config.is_closing_enabled_internal())
    }

    /// Python接口：更新总余额
    pub fn update_total_balance(
        &self,
        py: Python,
        primary_balance: f64,
        secondary_balance: Option<f64>,
        available_primary: Option<f64>,
        available_secondary: Option<f64>,
    ) -> PyResult<()> {
        let runtime = tokio::runtime::Handle::current();

        py.allow_threads(|| {
            runtime.block_on(async {
                // 更新 stats_manager 中的余额
                let mut stats_manager = self.stats_manager.lock().await;
                stats_manager.update_balance(
                    primary_balance,
                    secondary_balance,
                    available_primary,
                    available_secondary,
                );
                Ok(())
            })
        })
    }

    /// Python接口：更新所有节点持仓价值
    pub fn update_total_position_value(
        &self,
        py: Python,
        total_value: f64,
        long_position_value: f64,
        short_position_value: f64,
    ) -> PyResult<()> {
        let runtime = tokio::runtime::Handle::current();

        py.allow_threads(|| {
            runtime.block_on(async {
                let mut stats_manager = self.stats_manager.lock().await;
                stats_manager.update_total_position_value(
                    total_value,
                    long_position_value,
                    short_position_value,
                );
                Ok(())
            })
        })
    }

    /// Python接口：更新当前节点持仓价值
    pub fn update_current_position_value(
        &self,
        py: Python,
        total_value: f64,
        long_position_value: f64,
        short_position_value: f64,
    ) -> PyResult<()> {
        let runtime = tokio::runtime::Handle::current();

        py.allow_threads(|| {
            runtime.block_on(async {
                let mut stats_manager = self.stats_manager.lock().await;
                stats_manager.update_current_position_value(
                    total_value,
                    long_position_value,
                    short_position_value,
                );
                Ok(())
            })
        })
    }

    /// Python接口：添加已结算的资金费
    pub fn add_funding_fee(
        &self,
        py: Python,
        primary_fee: Option<f64>,
        secondary_fee: Option<f64>,
    ) -> PyResult<()> {
        let runtime = tokio::runtime::Handle::current();

        py.allow_threads(|| {
            runtime.block_on(async {
                let primary_fee = primary_fee.unwrap_or(0.0);
                let secondary_fee = secondary_fee.unwrap_or(0.0);

                let mut stats_manager = self.stats_manager.lock().await;
                stats_manager.add_funding_fee(primary_fee, secondary_fee);

                Ok(())
            })
        })
    }

    /// Python接口：更新未结算的预测资金费
    pub fn update_predicted_funding_fee(
        &self,
        py: Python,
        primary_fee: Option<f64>,
        secondary_fee: Option<f64>,
    ) -> PyResult<()> {
        let runtime = tokio::runtime::Handle::current();

        py.allow_threads(|| {
            runtime.block_on(async {
                let primary_fee = primary_fee.unwrap_or(0.0);
                let secondary_fee = secondary_fee.unwrap_or(0.0);

                let mut stats_manager = self.stats_manager.lock().await;
                stats_manager.update_predicted_funding_fee(primary_fee, secondary_fee);

                Ok(())
            })
        })
    }

    /// Python接口：更新浮动盈亏
    pub fn update_floating_profit(&self, py: Python, floating_profit: f64) -> PyResult<()> {
        let runtime = tokio::runtime::Handle::current();

        py.allow_threads(|| {
            runtime.block_on(async {
                let mut stats_manager = self.stats_manager.lock().await;
                stats_manager.update_floating_profit(floating_profit);

                Ok(())
            })
        })
    }

    /// Python接口：更新交易统计
    pub fn update_trade_stats(
        &self,
        py: Python,
        trade_volume: f64,
        maker_volume: Option<f64>,
        taker_volume: Option<f64>,
        profit: f64,
        is_single_close: bool,
    ) -> PyResult<f64> {
        let runtime = tokio::runtime::Handle::current();

        py.allow_threads(|| {
            runtime.block_on(async {
                let maker_volume = maker_volume.unwrap_or(0.0);
                let taker_volume = taker_volume.unwrap_or(0.0);

                let mut stats_manager = self.stats_manager.lock().await;

                // 更新前先检查日期变更
                stats_manager.check_and_reset_today_stats();

                // 更新交易统计
                stats_manager.update_trade_stats(
                    trade_volume,
                    maker_volume,
                    taker_volume,
                    profit,
                    is_single_close,
                );

                // 验证数据一致性
                stats_manager.validate_and_fix_consistency();

                let total_profit = stats_manager.total_profit();

                // 写入ClickHouse（如果启用且有实际交易）
                if profit != 0.0 {
                    if let Some(clickhouse_client) = &self.clickhouse_client {
                        if let Err(e) = clickhouse_client
                            .insert_complete_stats(&*stats_manager)
                            .await
                        {
                            tracing::warn!("ClickHouse写入失败: {}", e);
                        }
                    }
                }

                Ok(total_profit)
            })
        })
    }

    /// Python接口：上传表格
    pub fn upload_table(&self, py: Python, table: &Bound<'_, PyAny>) -> PyResult<()> {
        // 将Python表格转换为JSON
        let table: Value = depythonize(table)?;

        let runtime = tokio::runtime::Handle::current();

        py.allow_threads(|| {
            runtime.block_on(async {
                // 将表格数据记录到logger中
                let mut logger = self.logger.lock().await;
                logger.log_table_internal(table);

                Ok(())
            })
        })
    }

    /// Python接口：一次性上传多个表格
    pub fn upload_tables(&self, py: Python, tables: &Bound<'_, PyAny>) -> PyResult<()> {
        // 将Python表格列表转换为JSON数组
        let tables: Vec<Value> = depythonize(tables)?;

        let runtime = tokio::runtime::Handle::current();

        py.allow_threads(|| {
            runtime.block_on(async {
                // 获取logger锁
                let mut logger = self.logger.lock().await;

                // 逐个将表格数据记录到logger中
                for table in tables {
                    logger.log_table_internal(table);
                }

                Ok(())
            })
        })
    }

    /// Python接口：记录利润
    pub fn log_profit(&self, py: Python, profit: f64) -> PyResult<()> {
        let runtime = tokio::runtime::Handle::current();

        py.allow_threads(|| {
            runtime.block_on(async {
                // 记录利润日志
                let mut logger = self.logger.lock().await;
                logger.log_profit_internal(profit);

                Ok(())
            })
        })
    }

    /// Python接口: 启动客户端
    pub fn start(
        &mut self,
        py: Python,
        upload_interval: Option<i32>,
        log_receiver: Option<Receiver<LogMsg>>,
    ) -> PyResult<()> {
        self.enable_auto_upload(py, upload_interval, log_receiver)
    }

    /// Python接口：颜色消息
    pub fn color_msg(msg: &str, color: &str) -> PyResult<String> {
        let corlor = match color {
            // 基础颜色
            "red" => "#FF4500",    // 红色 - 错误、警告、负面信息
            "green" => "#32CD32",  // 绿色 - 成功、正面信息
            "blue" => "#0000FF",   // 蓝色 - 一般信息、通知
            "yellow" => "#FFC000", // 黄色 - 警告、提醒

            // 如果是#开头，则认为是十六进制颜色
            _ if color.starts_with("#") => color,
            // 返回错误
            _ => {
                return Err(PyErr::new::<PyValueError, _>(format!("无效的颜色 {color}")));
            }
        };
        Ok(format!("{msg} {corlor}"))
    }
}
