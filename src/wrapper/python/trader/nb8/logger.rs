use crate::wrapper::python::trader::nb8::config::Config;
use crate::wrapper::python::trader::nb8::utils::get_current_timestamp_us;
use crate::{web::client::WebClient, wrapper::python::trader::nb8::WebClientWrapper};
use pyo3::prelude::*;
use quant_common::time_us;
use serde_json::{Value, json};
use std::sync::Arc;

/// 日志类 - 处理日志记录和管理（内部使用，不暴露给Python）
pub struct Logger {
    config: Arc<Config>,
    http_client: Arc<WebClient>,
    log_queue: Vec<Value>,
    tables: Vec<Value>,
    pub force_stop: bool,
    run_time: i64,
}

impl Logger {
    // 创建Logger实例
    pub fn new(config: Arc<Config>, run_time: i64, http_client: Arc<WebClient>) -> Self {
        Logger {
            config,
            http_client,
            log_queue: Vec::new(),
            tables: Vec::new(),
            force_stop: false,
            run_time,
        }
    }

    // 内部纯Rust方法，记录日志
    pub fn log_internal(
        &mut self,
        msg: &str,
        _level: Option<&str>,
        msg_color: Option<&str>,
        time: Option<i64>,
    ) -> PyResult<()> {
        let timestamp = time.unwrap_or(get_current_timestamp_us());

        // web输出处理
        let mut web_msg = msg.replace("\n", "<br>");

        // 为web消息添加颜色
        if let Some(color) = msg_color {
            web_msg = WebClientWrapper::color_msg(msg, color)?;
        }
        self.log_queue.push(json!({
            "name": "log",
            "time": timestamp,
            "data": web_msg
        }));
        Ok(())
    }

    // 内部纯Rust方法，记录表格数据
    pub fn log_table_internal(&mut self, table_data: Value) {
        // 检查是否有title字段
        if let Some(title) = table_data.get("title") {
            // 查找是否已存在相同title的表格
            let mut existing_index = None;
            for (i, table) in self.tables.iter().enumerate() {
                if let Some(existing_title) = table.get("title")
                    && existing_title == title
                {
                    existing_index = Some(i);
                    break;
                }
            }

            // 如果找到相同title的表格，则替换它
            if let Some(index) = existing_index {
                self.tables[index] = table_data;
            } else {
                // 否则添加新表格
                self.tables.push(table_data);
            }
        } else {
            // 如果没有title字段，直接添加
            self.tables.push(table_data);
        }
    }

    // 内部纯Rust方法，记录利润数据
    pub fn log_profit_internal(&mut self, profit: f64) {
        let timestamp = get_current_timestamp_us();

        self.log_queue.push(json!({
            "name": "profit",
            "time": timestamp,
            "data": profit
        }));
    }

    // 内部纯Rust方法，上传日志到服务器
    pub async fn upload_logs_internal(&mut self, stats: Value) -> PyResult<Option<Value>> {
        if self.log_queue.is_empty() && self.tables.is_empty() {
            return Ok(None);
        }

        // 准备日志数据
        let mut logs = Vec::new();

        // 添加常规日志
        if !self.log_queue.is_empty() {
            logs.extend(self.log_queue.clone());
            self.log_queue.clear();
        }

        // 添加表格数据
        if self.tables.is_empty() {
            let tables = json!({
                "time": time_us(),
                "name": "table",
                "data": [
                    json!(self.tables.clone()),
                    stats
                ]
            });
            logs.push(tables);
            self.tables.clear();
        }

        // 调用HTTP客户端上传日志
        let server_name = &self.config.server_name;

        let result = self
            .http_client
            .upload_log_internal(server_name, self.run_time, 0, json!(logs), None)
            .await;
        match result {
            Ok(result) => Ok(Some(result)),
            Err(e) => {
                tracing::error!(web = false, "上传日志失败: {}", e);
                Ok(None)
            }
        }
    }

    /// 上传错误日志的内部方法
    pub async fn upload_error_internal(&mut self, error: &str) -> PyResult<Option<Value>> {
        let server_name = &self.config.server_name;
        let error_msg = error.replace("\n", "<br>");
        let result = self
            .http_client
            .upload_error_internal(server_name, &error_msg)
            .await;
        match result {
            Ok(result) => Ok(Some(result)),
            Err(e) => {
                tracing::error!(web = false, "上传错误日志失败: {}", e);
                Ok(None)
            }
        }
    }
}
