# NB8 SDK Rust实现

这是Python nb8 SDK的Rust Pyo3实现，提供更高性能和更低资源占用的SDK。

## 功能特点

- 基于Rust实现，通过Pyo3绑定到Python
- 保持与原Python SDK相同的API和用法
- 暴露WebClient类和主要方法
- 线程安全并支持异步操作
- 内存占用更少，性能更好

## 模块结构

模块采用了清晰的分层结构，每个组件负责不同的功能：

- `config.rs` - 配置管理模块，存储全局配置和状态
- `http_client.rs` - HTTP客户端模块，处理所有的HTTP请求
- `logger.rs` - 日志处理模块，负责日志记录和格式化
- `web_client.rs` - Web客户端模块，封装了与Web服务的交互逻辑
- `utils.rs` - 工具函数模块，提供通用功能
- `mod.rs` - 模块入口文件，导出所有组件

## 安装

```bash
# 确保已安装rust和cargo
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# 克隆存储库
git clone https://github.com/your-repo/open_quant.git
cd open_quant

# 构建Python包
./build_nb8_sdk.sh
```

## 使用方法

```python
from open_quant import nb8

# 创建WebClient实例
client = nb8.WebClient.create(
    server_name="策略名称",
    initial_balance=10000.0,
    is_production=False
)

# 启动客户端
client.start(auto_upload=True, upload_interval=5)

# 记录日志
client.log("策略启动成功", level="INFO")
client.color_log("这是红色的日志", color="red")

# 更新余额信息
client.update_total_balance(
    balance=10100.0,
    coinex_now=100.0
)

# 检查状态
if client.is_stopped():
    print("策略已停止")

# 停止客户端
client.stop()
```

## 主要API

### WebClient

- `WebClient.create(server_name, initial_balance=10000.0, coinex_balance=0.0, is_production=True, open_roe=0.0, kai_bili=0.0, gang_gan=10, feilv_roe=0.0)` - 创建WebClient实例
- `start(auto_upload=True, upload_interval=1)` - 启动客户端
- `stop()` - 停止客户端
- `log(message, level="INFO", web=True, msg_color=None)` - 记录日志
- `color_log(msg, color, level="INFO")` - 记录彩色日志
- `update_total_balance(balance, coinex_now=None, keyong=None, coinex_keyong=None)` - 更新总余额
- `is_stopped()` - 检查是否停止
- `is_trade_stopped()` - 检查是否停止交易
- `is_kai_stopped()` - 检查是否停止开仓

## 示例

查看 `examples/nb8_example.py` 文件，了解完整使用示例。

## 性能优势

相比原始Python SDK，Rust实现有以下优势：

1. **更快的执行速度**：Rust代码编译为机器码执行，比Python解释执行快数倍到数十倍
2. **更低的内存占用**：Rust的内存管理更精确，没有GC开销
3. **线程安全保证**：Rust的所有权系统在编译时就能保证线程安全
4. **高并发处理能力**：更适合处理大量并发请求

## 开发注意

- 使用Rust 2021版本编写
- 使用Pyo3框架与Python交互
- 依赖reqwest进行HTTP请求
- 依赖serde和serde_json处理JSON数据
- 依赖chrono处理时间
