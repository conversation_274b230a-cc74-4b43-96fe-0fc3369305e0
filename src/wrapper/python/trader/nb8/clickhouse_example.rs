use super::clickhouse_client::{<PERSON>lickHouseClient, ClickHouseConfig};
use super::stats::StatsManager;
use std::error::Error;

/// ClickHouse客户端使用示例
pub async fn example_usage() -> Result<(), Box<dyn Error>> {
    // 1. 创建ClickHouse配置
    let config = ClickHouseConfig {
        url: "http://localhost:8123".to_string(),
        database: "trading".to_string(),
        username: Some("default".to_string()),
        password: Some("".to_string()),
        stats_table: "stats_manager".to_string(),
        today_stats_table: "today_stats".to_string(),
    };

    // 2. 创建ClickHouse客户端
    let client = ClickHouseClient::new(config)?;

    // 3. 测试连接
    client.test_connection().await?;
    println!("ClickHouse连接测试成功！");

    // 4. 创建数据库表
    client.create_tables().await?;
    println!("数据库表创建成功！");

    // 5. 创建示例统计数据
    let stats = create_sample_stats();

    // 6. 写入单个记录
    client.insert_complete_stats(&stats).await?;
    println!("单个记录写入成功！");

    // 7. 批量写入示例
    let stats_list = vec![&stats, &stats]; // 示例：写入两条相同的记录
    client.batch_insert_stats_manager(&stats_list).await?;
    println!("批量写入成功！");

    Ok(())
}

/// 创建示例统计数据
fn create_sample_stats() -> StatsManager {
    let mut stats = StatsManager::new(
        "test_server".to_string(),
        10000.0, // initial_balance
        5000.0,  // secondary_balance
        0.14,    // open_threshold
        100.0,   // max_position_ratio
        10,      // max_leverage
        0.1,     // funding_rate_threshold
        0.1,     // cost
        0.02,    // primary_maker_fee_rate
        0.04,    // primary_taker_fee_rate
        0.01,    // primary_rebate_rate
        0.02,    // secondary_maker_fee_rate
        0.04,    // secondary_taker_fee_rate
        0.01,    // secondary_rebate_rate
    );

    // 更新一些示例数据
    stats.update_balance(10500.0, Some(5200.0), Some(10500.0), Some(5200.0));
    stats.update_trade_stats(50000.0, 30000.0, 20000.0, 700.0, false);
    stats.update_floating_profit(100.0);
    stats.add_funding_fee(30.0, 20.0);

    stats
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_clickhouse_config_default() {
        let config = ClickHouseConfig::default();
        assert_eq!(config.url, "http://localhost:8123");
        assert_eq!(config.database, "trading");
        assert_eq!(config.stats_table, "stats_manager");
        assert_eq!(config.today_stats_table, "today_stats");
    }

    #[tokio::test]
    async fn test_clickhouse_client_creation() {
        let config = ClickHouseConfig::default();
        let client = ClickHouseClient::new(config);
        assert!(client.is_ok());
    }

    #[test]
    fn test_sample_stats_creation() {
        let stats = create_sample_stats();
        assert_eq!(stats.server_name, "test_server");
        assert_eq!(stats.initial_balance, 10000.0);
        assert_eq!(stats.today.count, 10.0);
    }
}
