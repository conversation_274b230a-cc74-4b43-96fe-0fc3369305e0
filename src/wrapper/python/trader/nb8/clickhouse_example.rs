use super::clickhouse_client::{<PERSON>lick<PERSON>ouseClient, ClickHouseConfig};
use super::stats::{StatsManager, TodayStats};
use std::error::Error;

/// ClickHouse客户端使用示例
pub async fn example_usage() -> Result<(), Box<dyn Error>> {
    // 1. 创建ClickHouse配置
    let config = ClickHouseConfig {
        url: "http://localhost:8123".to_string(),
        database: "trading".to_string(),
        username: Some("default".to_string()),
        password: Some("".to_string()),
        stats_table: "stats_manager".to_string(),
        today_stats_table: "today_stats".to_string(),
    };

    // 2. 创建ClickHouse客户端
    let client = ClickHouseClient::new(config)?;

    // 3. 测试连接
    client.test_connection().await?;
    println!("ClickHouse连接测试成功！");

    // 4. 创建数据库表
    client.create_tables().await?;
    println!("数据库表创建成功！");

    // 5. 创建示例统计数据
    let stats = create_sample_stats();

    // 6. 写入单个记录
    client.insert_complete_stats(&stats).await?;
    println!("单个记录写入成功！");

    // 7. 批量写入示例
    let stats_list = vec![&stats, &stats]; // 示例：写入两条相同的记录
    client.batch_insert_stats_manager(&stats_list).await?;
    println!("批量写入成功！");

    Ok(())
}

/// 创建示例统计数据
fn create_sample_stats() -> StatsManager {
    let today = TodayStats {
        time: chrono::Utc::now().timestamp(),
        initial_balance: 10000.0,
        current_balance: 10500.0,
        available_balance: 10500.0,
        secondary_initial_balance: 5000.0,
        secondary_current_balance: 5200.0,
        secondary_available_balance: 5200.0,
        volume: 50000.0,
        maker_volume: 30000.0,
        taker_volume: 20000.0,
        count: 10.0,
        win_rate: 70.0,
        total_profit: 700.0,
        success_count: 7.0,
        success_profit: 1000.0,
        failure_count: 3.0,
        failure_loss: -300.0,
        funding_fee: 50.0,
        primary_funding_fee: 30.0,
        secondary_funding_fee: 20.0,
    };

    StatsManager {
        time: chrono::Utc::now().timestamp(),
        server_name: "test_server".to_string(),
        initial_balance: 10000.0,
        current_balance: 10500.0,
        available_balance: 10500.0,
        secondary_initial_balance: 5000.0,
        secondary_current_balance: 5200.0,
        secondary_available_balance: 5200.0,
        volume: 50000.0,
        maker_volume: 30000.0,
        taker_volume: 20000.0,
        count: 10,
        win_rate: 70.0,
        total_profit: 700.0,
        success_count: 7,
        success_profit: 1000.0,
        failure_count: 3,
        failure_loss: -300.0,
        unrealized_pnl: 100.0,
        single_close_count: 2,
        single_close_profit: 200.0,
        funding_fee: 50.0,
        primary_funding_fee: 30.0,
        secondary_funding_fee: 20.0,
        cost: 0.1,
        open_threshold: 0.14,
        funding_rate_threshold: 0.1,
        max_position_ratio: 100.0,
        max_leverage: 10,
        primary_maker_fee_rate: 0.02,
        primary_taker_fee_rate: 0.04,
        primary_rebate_rate: 0.01,
        secondary_maker_fee_rate: 0.02,
        secondary_taker_fee_rate: 0.04,
        secondary_rebate_rate: 0.01,
        today,
        data_file_path: std::path::PathBuf::new(), // 示例中不需要文件路径
        // 其他字段使用默认值
        ..Default::default()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_clickhouse_config_default() {
        let config = ClickHouseConfig::default();
        assert_eq!(config.url, "http://localhost:8123");
        assert_eq!(config.database, "trading");
        assert_eq!(config.stats_table, "stats_manager");
        assert_eq!(config.today_stats_table, "today_stats");
    }

    #[tokio::test]
    async fn test_clickhouse_client_creation() {
        let config = ClickHouseConfig::default();
        let client = ClickHouseClient::new(config);
        assert!(client.is_ok());
    }

    #[test]
    fn test_sample_stats_creation() {
        let stats = create_sample_stats();
        assert_eq!(stats.server_name, "test_server");
        assert_eq!(stats.initial_balance, 10000.0);
        assert_eq!(stats.today.count, 10.0);
    }
}
