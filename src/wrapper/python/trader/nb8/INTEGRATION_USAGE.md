# ClickHouse集成使用指南

ClickHouse客户端已经成功集成到WebClient中，现在可以在更新数据时自动写入ClickHouse数据库。

## 功能特性

- **自动数据同步**: 当WebClient更新统计数据时，会自动写入ClickHouse
- **异步写入**: 使用tokio异步运行时，不会阻塞主要业务逻辑
- **错误容错**: ClickHouse写入失败不会影响主要功能，只会记录警告日志
- **可配置**: 可以通过配置启用或禁用ClickHouse功能

## 配置方法

在创建WebClient时，可以通过配置参数启用ClickHouse功能：

### Python配置示例

```python
config = {
    "server_name": "trading_server_1",
    "primary_balance": 10000.0,
    "secondary_balance": 5000.0,
    "open_threshold": 0.14,
    "max_position_ratio": 100.0,
    "max_leverage": 10,
    "funding_rate_threshold": 0.1,
    "cost": 0.1,
    "primary_maker_fee_rate": 0.02,
    "primary_taker_fee_rate": 0.04,
    "primary_rebate_rate": 0.01,
    "secondary_maker_fee_rate": 0.02,
    "secondary_taker_fee_rate": 0.04,
    "secondary_rebate_rate": 0.01,
    
    # ClickHouse配置
    "clickhouse_enabled": True,
    "clickhouse_url": "http://localhost:8123",
    "clickhouse_database": "trading",
    "clickhouse_username": "default",
    "clickhouse_password": "",
}

# 创建WebClient
web_client = WebClientWrapper.new(config)
```

### 配置参数说明

- `clickhouse_enabled`: 是否启用ClickHouse功能（默认: False）
- `clickhouse_url`: ClickHouse服务器地址（默认: "http://localhost:8123"）
- `clickhouse_database`: 数据库名称（默认: "trading"）
- `clickhouse_username`: 用户名（可选）
- `clickhouse_password`: 密码（可选）

## 自动写入时机

ClickHouse客户端会在以下情况自动写入数据：

1. **交易统计更新**: 当调用`update_trade_stats`且有实际利润时
2. **定期上传**: 在自动上传任务中定期写入完整统计数据
3. **数据同步**: 确保ClickHouse中的数据与本地统计数据保持同步

## 数据表结构

系统会自动创建两个表：

### stats_manager表
存储主要的交易统计数据，包括：
- 时间戳和服务器名称
- 账户余额信息（主所和次所）
- 交易统计（交易量、胜率、利润等）
- 交易参数（手续费率、杠杆等）
- 资金费用信息

### today_stats表
存储当日的交易统计数据，字段与stats_manager类似但专注于当日数据。

## 错误处理

- ClickHouse连接失败不会影响主要功能
- 写入失败会记录警告日志，但不会中断程序
- 系统启动时会自动测试连接和创建表

## 日志输出

系统会输出相关的日志信息：

```
INFO ClickHouse客户端初始化成功
WARN ClickHouse连接测试失败: Connection refused
WARN ClickHouse写入失败: Table doesn't exist
```

## 查询数据

可以直接在ClickHouse中查询数据：

```sql
-- 查看最新的统计数据
SELECT * FROM trading.stats_manager 
ORDER BY timestamp DESC 
LIMIT 10;

-- 查看今日统计
SELECT * FROM trading.today_stats 
WHERE server_name = 'trading_server_1'
ORDER BY timestamp DESC 
LIMIT 10;

-- 按服务器统计利润
SELECT 
    server_name,
    SUM(total_profit) as total_profit,
    AVG(win_rate) as avg_win_rate,
    COUNT(*) as record_count
FROM trading.stats_manager 
GROUP BY server_name;
```

## 性能考虑

- 写入操作是异步的，不会阻塞主线程
- 批量写入功能可以提高性能（如果需要）
- ClickHouse的列式存储对时序数据查询非常高效

## 故障排除

1. **连接失败**: 检查ClickHouse服务是否运行，网络是否可达
2. **认证失败**: 检查用户名和密码是否正确
3. **表创建失败**: 检查用户是否有创建表的权限
4. **写入失败**: 检查数据格式和表结构是否匹配

## 禁用ClickHouse

如果不需要ClickHouse功能，只需要在配置中设置：

```python
config = {
    # ... 其他配置
    "clickhouse_enabled": False,  # 或者不设置此字段
}
```

这样系统就不会初始化ClickHouse客户端，也不会进行任何写入操作。
