# ClickHouse客户端

这是一个用于将交易统计数据写入ClickHouse数据库的Rust客户端。

## 功能特性

- 支持异步操作，基于tokio运行时
- 使用HTTP协议与ClickHouse通信，无需额外的原生客户端库
- 支持单条和批量数据写入
- 自动创建数据库表结构
- 完整的错误处理和日志记录
- 支持认证和自定义配置

## 数据结构

### StatsManager表
存储主要的交易统计数据，包括：
- 账户余额信息（主所和次所）
- 交易统计（交易量、胜率、利润等）
- 交易参数（手续费率、杠杆等）
- 资金费用信息

### TodayStats表
存储当日的交易统计数据，字段与StatsManager类似但专注于当日数据。

## 使用方法

### 1. 基本配置

```rust
use crate::wrapper::python::trader::nb8::clickhouse_client::{ClickHouseClient, ClickHouseConfig};

// 创建配置
let config = ClickHouseConfig {
    url: "http://localhost:8123".to_string(),
    database: "trading".to_string(),
    username: Some("default".to_string()),
    password: Some("password".to_string()),
    stats_table: "stats_manager".to_string(),
    today_stats_table: "today_stats".to_string(),
};

// 创建客户端
let client = ClickHouseClient::new(config)?;
```

### 2. 测试连接

```rust
// 测试ClickHouse连接
client.test_connection().await?;
```

### 3. 创建数据库表

```rust
// 自动创建所需的数据库表
client.create_tables().await?;
```

### 4. 写入数据

#### 单条记录写入

```rust
// 写入完整的统计数据（包括StatsManager和TodayStats）
client.insert_complete_stats(&stats_manager).await?;

// 或者分别写入
client.insert_stats_manager(&stats_manager).await?;
client.insert_today_stats(&today_stats, "server_name").await?;
```

#### 批量写入

```rust
// 批量写入StatsManager数据
let stats_list = vec![&stats1, &stats2, &stats3];
client.batch_insert_stats_manager(&stats_list).await?;

// 批量写入TodayStats数据
let today_stats_list = vec![(&today_stats1, "server1"), (&today_stats2, "server2")];
client.batch_insert_today_stats(&today_stats_list).await?;
```

## 配置选项

### ClickHouseConfig

- `url`: ClickHouse服务器地址（默认: "http://localhost:8123"）
- `database`: 数据库名称（默认: "trading"）
- `username`: 用户名（可选）
- `password`: 密码（可选）
- `stats_table`: StatsManager表名（默认: "stats_manager"）
- `today_stats_table`: TodayStats表名（默认: "today_stats"）

## 表结构

### stats_manager表

```sql
CREATE TABLE trading.stats_manager (
    timestamp UInt32,
    server_name String,
    initial_balance Float64,
    current_balance Float64,
    available_balance Float64,
    secondary_initial_balance Float64,
    secondary_current_balance Float64,
    secondary_available_balance Float64,
    volume Float64,
    maker_volume Float64,
    taker_volume Float64,
    count Int32,
    win_rate Float64,
    total_profit Float64,
    success_count Int32,
    success_profit Float64,
    failure_count Int32,
    failure_loss Float64,
    unrealized_pnl Float64,
    single_close_count Int32,
    single_close_profit Float64,
    funding_fee Float64,
    primary_funding_fee Float64,
    secondary_funding_fee Float64,
    cost Float64,
    open_threshold Float64,
    funding_rate_threshold Float64,
    max_position_ratio Float64,
    max_leverage Int32,
    primary_maker_fee_rate Float64,
    primary_taker_fee_rate Float64,
    primary_rebate_rate Float64,
    secondary_maker_fee_rate Float64,
    secondary_taker_fee_rate Float64,
    secondary_rebate_rate Float64
) ENGINE = MergeTree()
ORDER BY (server_name, timestamp);
```

### today_stats表

```sql
CREATE TABLE trading.today_stats (
    timestamp UInt32,
    server_name String,
    time Int64,
    initial_balance Float64,
    current_balance Float64,
    available_balance Float64,
    secondary_initial_balance Float64,
    secondary_current_balance Float64,
    secondary_available_balance Float64,
    volume Float64,
    maker_volume Float64,
    taker_volume Float64,
    count Float64,
    win_rate Float64,
    total_profit Float64,
    success_count Float64,
    success_profit Float64,
    failure_count Float64,
    failure_loss Float64,
    funding_fee Float64,
    primary_funding_fee Float64,
    secondary_funding_fee Float64
) ENGINE = MergeTree()
ORDER BY (server_name, timestamp);
```

## 错误处理

所有异步方法都返回`Result<(), Box<dyn Error>>`，可以使用标准的Rust错误处理模式：

```rust
match client.insert_stats_manager(&stats).await {
    Ok(()) => println!("数据写入成功"),
    Err(e) => eprintln!("写入失败: {}", e),
}
```

## 日志记录

客户端使用`tracing`库进行日志记录，会输出操作状态和错误信息。确保在应用中初始化日志系统。

## 依赖

- `reqwest`: HTTP客户端
- `serde`: 序列化支持
- `tracing`: 日志记录
- `chrono`: 时间处理
- `tokio`: 异步运行时

## 注意事项

1. 确保ClickHouse服务器正在运行并且可以访问
2. 数据库和表会自动创建，但需要确保有相应的权限
3. 批量写入时建议控制批次大小，避免单次写入过多数据
4. 所有时间戳都使用UTC时间
5. 字符串字段会自动转义以防止SQL注入
