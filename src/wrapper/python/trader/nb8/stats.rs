use chrono::{FixedOffset, TimeZone, Utc};
use quant_common::time;
use serde::{Deserialize, Serialize};
use serde_json::{Value, json};
use std::fs::File;
use std::io::{Read, Write};
use std::path::{Path, PathBuf};

/// 今日统计数据结构体
#[derive(Serialize, Deserialize, Debug, Clone, Default)]
pub struct TodayStats {
    pub time: i64,
    pub initial_balance: f64,
    pub current_balance: f64,
    pub available_balance: f64,
    pub secondary_initial_balance: f64,
    pub secondary_current_balance: f64,
    pub secondary_available_balance: f64,
    pub volume: f64,
    pub maker_volume: f64,
    pub taker_volume: f64,
    pub count: f64,
    pub win_rate: f64,
    pub total_profit: f64,
    pub success_count: f64,
    pub success_profit: f64,
    pub failure_count: f64,
    pub failure_loss: f64,
    pub funding_fee: f64,           // 未结算的预测资金费总额
    pub primary_funding_fee: f64,   // 未结算的预测主所资金费
    pub secondary_funding_fee: f64, // 未结算的预测次所资金费
}

/// 今日统计数据别名结构体（用于序列化为别名）
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct TodayStatsAlias {
    pub time: i64,
    #[serde(rename = "yuer")]
    pub initial_balance: f64,
    #[serde(rename = "nowYuer")]
    pub current_balance: f64,
    #[serde(rename = "keyong")]
    pub available_balance: f64,
    #[serde(rename = "coinex")]
    pub secondary_initial_balance: f64,
    #[serde(rename = "coinexNow")]
    pub secondary_current_balance: f64,
    #[serde(rename = "coinexKeyong")]
    pub secondary_available_balance: f64,
    pub volume: f64,
    #[serde(rename = "maker_volume")]
    pub maker_volume: f64,
    #[serde(rename = "taker_volume")]
    pub taker_volume: f64,
    pub count: f64,
    #[serde(rename = "shenglv")]
    pub win_rate: f64,
    #[serde(rename = "m")]
    pub total_profit: f64,
    #[serde(rename = "ok_count")]
    pub success_count: f64,
    #[serde(rename = "ok_m")]
    pub success_profit: f64,
    #[serde(rename = "no_count")]
    pub failure_count: f64,
    #[serde(rename = "no_m")]
    pub failure_loss: f64,
    #[serde(rename = "feilv")]
    pub funding_fee: f64,
    #[serde(rename = "bian_feilv")]
    pub primary_funding_fee: f64,
    #[serde(rename = "coinex_feilv")]
    pub secondary_funding_fee: f64,
}

impl TodayStats {
    /// 转换为别名结构体，用于序列化为别名
    pub fn to_alias(&self) -> TodayStatsAlias {
        TodayStatsAlias {
            time: self.time,
            initial_balance: self.initial_balance,
            current_balance: self.current_balance,
            available_balance: self.available_balance,
            secondary_initial_balance: self.secondary_initial_balance,
            secondary_current_balance: self.secondary_current_balance,
            secondary_available_balance: self.secondary_available_balance,
            volume: self.volume,
            maker_volume: self.maker_volume,
            taker_volume: self.taker_volume,
            count: self.count,
            win_rate: self.win_rate,
            total_profit: self.total_profit,
            success_count: self.success_count,
            success_profit: self.success_profit,
            failure_count: self.failure_count,
            failure_loss: self.failure_loss,
            funding_fee: self.funding_fee,
            primary_funding_fee: self.primary_funding_fee,
            secondary_funding_fee: self.secondary_funding_fee,
        }
    }
}

/// 统计数据管理结构体 (内部使用，不暴露给Python)
#[derive(Serialize, Deserialize, Debug, Clone, Default)]
pub struct StatsManager {
    pub time: i64,           // 起始时间
    pub server_name: String, // 服务器名称
    // 账户余额信息
    pub initial_balance: f64,             // 主所起始余额
    pub current_balance: f64,             // 主所当前余额
    pub secondary_initial_balance: f64,   // 次所初始余额
    pub secondary_current_balance: f64,   // 次所实时余额
    pub available_balance: f64,           // 主所可用余额
    pub secondary_available_balance: f64, // 次所可用余额

    // 交易统计
    pub volume: f64,              // 累计交易量
    pub maker_volume: f64,        // 累计maker交易量
    pub taker_volume: f64,        // 累计taker交易量
    pub count: i32,               // 总套利次数
    pub win_rate: f64,            // 胜率
    pub total_profit: f64,        // 总利润
    pub success_count: i32,       // 成功次数
    pub success_profit: f64,      // 成功利润
    pub failure_count: i32,       // 失败次数
    pub failure_loss: f64,        // 失败亏损
    pub unrealized_pnl: f64,      // 盈亏
    pub single_close_count: i32,  // 单腿平仓次数
    pub single_close_profit: f64, // 单腿平仓金额

    // 持仓和杠杆信息
    pub total_position_value: f64,         // 所有节点持仓杠杆
    pub total_long_position_value: f64,    // 所有节点多仓杠杆
    pub total_short_position_value: f64,   // 所有节点空仓杠杆
    pub current_position_value: f64,       // 当前节点持仓杠杆
    pub current_long_position_value: f64,  // 当前节点多仓杠杆
    pub current_short_position_value: f64, // 当前节点空仓杠杆

    // 资金费率与回报率
    pub funding_fee: f64,           // 当日已结算的资金费总额
    pub primary_funding_fee: f64,   // 当日已结算的主所资金费
    pub secondary_funding_fee: f64, // 当日已结算的次所资金费

    // 交易参数
    pub cost: f64,                   // 成本
    pub open_threshold: f64,         // 开仓阈值，如0.14代表千1.4价差开仓
    pub funding_rate_threshold: f64, // 资金费率阈值，如0.1代表千1资金费率开仓
    pub max_position_ratio: f64,     // 单个币种最大持仓比例，如100代表100%，1x杠杆
    pub fund_transfer: f64,          // 资金划转
    pub max_leverage: i32,           // 最大可开杠杆
    // 手续费率及返佣率
    pub primary_maker_fee_rate: f64,   // 主所maker手续费率
    pub primary_taker_fee_rate: f64,   // 主所taker手续费率
    pub primary_rebate_rate: f64,      // 主所返佣率
    pub secondary_maker_fee_rate: f64, // 次所maker手续费率
    pub secondary_taker_fee_rate: f64, // 次所taker手续费率
    pub secondary_rebate_rate: f64,    // 次所返佣率

    // 今日数据
    pub today: TodayStats,

    // 数据文件路径
    #[serde(skip)]
    data_file_path: PathBuf,
}

/// 统计数据管理别名结构体（用于序列化为别名）
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct StatsManagerAlias {
    pub server_name: String,
    // 账户余额信息
    #[serde(rename = "yuer")]
    pub initial_balance: f64, // 主所起始余额
    #[serde(rename = "nowYuer")]
    pub current_balance: f64, // 主所当前余额
    #[serde(rename = "coinex")]
    pub secondary_initial_balance: f64, // 次所初始余额
    #[serde(rename = "coinexNow")]
    pub secondary_current_balance: f64, // 次所实时余额
    #[serde(rename = "keyong")]
    pub available_balance: f64, // 主所可用余额
    #[serde(rename = "coinexKeyong")]
    pub secondary_available_balance: f64, // 次所可用余额

    // 交易统计
    pub volume: f64, // 累计交易量
    #[serde(rename = "maker_volume")]
    pub maker_volume: f64, // 累计maker交易量
    #[serde(rename = "taker_volume")]
    pub taker_volume: f64, // 累计taker交易量
    pub count: i32,  // 总套利次数
    #[serde(rename = "shenglv")]
    pub win_rate: f64, // 胜率
    #[serde(rename = "m")]
    pub total_profit: f64, // 总利润
    #[serde(rename = "ok_count")]
    pub success_count: i32, // 成功次数
    #[serde(rename = "ok_m")]
    pub success_profit: f64, // 成功利润
    #[serde(rename = "no_count")]
    pub failure_count: i32, // 失败次数
    #[serde(rename = "no_m")]
    pub failure_loss: f64, // 失败亏损
    #[serde(rename = "yingkui")]
    pub unrealized_pnl: f64, // 盈亏
    #[serde(rename = "dantui")]
    pub single_close_count: i32, // 单腿平仓次数
    #[serde(rename = "dantui_m")]
    pub single_close_profit: f64, // 单腿平仓金额

    // 持仓和杠杆信息
    #[serde(rename = "pos_jiazhi")]
    pub total_position_value: f64, // 所有节点持仓杠杆
    #[serde(rename = "pos_jiazhi_buy")]
    pub total_long_position_value: f64, // 所有节点多仓杠杆
    #[serde(rename = "pos_jiazhi_sell")]
    pub total_short_position_value: f64, // 所有节点空仓杠杆
    #[serde(rename = "pos_jiazhi2")]
    pub current_position_value: f64, // 当前节点持仓杠杆
    #[serde(rename = "pos_jiazhi2_buy")]
    pub current_long_position_value: f64, // 当前节点多仓杠杆
    #[serde(rename = "pos_jiazhi2_sell")]
    pub current_short_position_value: f64, // 当前节点空仓杠杆

    // 资金费率与回报率
    #[serde(rename = "feilv")]
    pub funding_fee: f64, // 资金费
    #[serde(rename = "bian_feilv")]
    pub primary_funding_fee: f64, // 币安资金费
    #[serde(rename = "coinex_feilv")]
    pub secondary_funding_fee: f64, // 其他交易所资金费

    // 交易参数
    #[serde(rename = "chengben")]
    pub cost: f64, // 成本
    #[serde(rename = "OpenRoe")]
    pub open_threshold: f64, // 开仓阈值，如0.14代表千1.4价差开仓
    #[serde(rename = "FeilvRoe")]
    pub funding_rate_threshold: f64, // 资金费率阈值，如0.1代表千1资金费率开仓
    #[serde(rename = "Kai_Bili")]
    pub max_position_ratio: f64, // 单个币种最大持仓比例，如100代表100%，1x杠杆
    #[serde(rename = "huazhuan")]
    pub fund_transfer: f64, // 资金划转
    pub time: i64, // 起始时间
    #[serde(rename = "GangGan")]
    pub max_leverage: i32, // 最大可开杠杆
    pub primary_maker_fee_rate: f64,
    pub primary_taker_fee_rate: f64,
    pub primary_rebate_rate: f64,
    pub secondary_maker_fee_rate: f64,
    pub secondary_taker_fee_rate: f64,
    pub secondary_rebate_rate: f64,

    // 今日数据
    pub today: TodayStatsAlias,
}

impl StatsManager {
    /// 创建新的统计管理器
    #[allow(clippy::too_many_arguments)]
    pub fn new(
        server_name: String,
        initial_balance: f64,
        secondary_balance: f64,
        open_threshold: f64,
        max_position_ratio: f64,
        max_leverage: i32,
        funding_rate_threshold: f64,
        cost: f64,
        primary_maker_fee_rate: f64,
        primary_taker_fee_rate: f64,
        primary_rebate_rate: f64,
        secondary_maker_fee_rate: f64,
        secondary_taker_fee_rate: f64,
        secondary_rebate_rate: f64,
    ) -> Self {
        // 检查服务器名称是否为空
        let server_name = if server_name.is_empty() {
            let default_name = "default_server".to_string();
            tracing::warn!("服务器名称为空，使用默认名称: {}", default_name);
            default_name
        } else {
            server_name
        };

        // 检查是否有现有数据可以加载
        let data_dir = Self::get_data_directory();
        let file_path = data_dir.join(format!("{server_name}.json"));

        tracing::info!("使用数据文件路径: {}", file_path.display());

        // 尝试从文件加载现有数据
        if let Ok(existing_stats) = Self::load_from_file(&file_path) {
            let mut stats = existing_stats;
            stats.data_file_path = file_path.clone();
            stats.max_leverage = max_leverage;
            stats.open_threshold = open_threshold;
            stats.max_position_ratio = max_position_ratio;
            stats.funding_rate_threshold = funding_rate_threshold;
            stats.cost = cost;
            stats.primary_maker_fee_rate = primary_maker_fee_rate;
            stats.primary_taker_fee_rate = primary_taker_fee_rate;
            stats.primary_rebate_rate = primary_rebate_rate;
            stats.secondary_maker_fee_rate = secondary_maker_fee_rate;
            stats.secondary_taker_fee_rate = secondary_taker_fee_rate;
            stats.secondary_rebate_rate = secondary_rebate_rate;

            tracing::info!("成功加载现有统计数据: {}", file_path.display());

            // 检查日期变更
            stats.check_and_reset_today_stats();
            return stats;
        } else {
            tracing::info!("未找到现有统计数据，创建新实例: {}", file_path.display());
        }

        // 如果没有现有数据，创建新的实例
        let current_time = time() + 8 * 3600;

        let today = TodayStats {
            time: current_time,
            initial_balance,
            current_balance: initial_balance,
            secondary_initial_balance: secondary_balance,
            secondary_current_balance: secondary_balance,
            available_balance: initial_balance,
            secondary_available_balance: secondary_balance,
            ..Default::default()
        };

        let stats = StatsManager {
            server_name,
            initial_balance,
            current_balance: initial_balance,
            secondary_initial_balance: secondary_balance,
            secondary_current_balance: secondary_balance,
            available_balance: initial_balance,
            secondary_available_balance: secondary_balance,
            time: current_time,
            max_leverage,
            today,
            data_file_path: file_path,
            open_threshold,
            funding_rate_threshold,
            max_position_ratio,
            cost,
            primary_maker_fee_rate,
            primary_taker_fee_rate,
            primary_rebate_rate,
            secondary_maker_fee_rate,
            secondary_taker_fee_rate,
            secondary_rebate_rate,
            ..Default::default()
        };

        // 保存初始数据
        stats.save_data();

        tracing::info!("创建新的统计管理器，基准时间：UTC+8 {}", current_time);

        stats
    }

    /// 转换为别名结构体，用于序列化为别名
    pub fn to_alias(&self) -> StatsManagerAlias {
        StatsManagerAlias {
            server_name: self.server_name.clone(),
            initial_balance: self.initial_balance,
            current_balance: self.current_balance,
            secondary_initial_balance: self.secondary_initial_balance,
            secondary_current_balance: self.secondary_current_balance,
            available_balance: self.available_balance,
            secondary_available_balance: self.secondary_available_balance,
            volume: self.volume,
            maker_volume: self.maker_volume,
            taker_volume: self.taker_volume,
            count: self.count,
            win_rate: self.win_rate,
            total_profit: self.total_profit,
            success_count: self.success_count,
            success_profit: self.success_profit,
            failure_count: self.failure_count,
            failure_loss: self.failure_loss,
            unrealized_pnl: self.unrealized_pnl,
            single_close_count: self.single_close_count,
            single_close_profit: self.single_close_profit,
            total_position_value: self.total_position_value,
            total_long_position_value: self.total_long_position_value,
            total_short_position_value: self.total_short_position_value,
            current_position_value: self.current_position_value,
            current_long_position_value: self.current_long_position_value,
            current_short_position_value: self.current_short_position_value,
            funding_fee: self.funding_fee,
            primary_funding_fee: self.primary_funding_fee,
            secondary_funding_fee: self.secondary_funding_fee,
            cost: self.cost,
            open_threshold: self.open_threshold,
            funding_rate_threshold: self.funding_rate_threshold,
            max_position_ratio: self.max_position_ratio,
            fund_transfer: self.fund_transfer,
            time: self.time,
            max_leverage: self.max_leverage,
            primary_maker_fee_rate: self.primary_maker_fee_rate,
            primary_taker_fee_rate: self.primary_taker_fee_rate,
            primary_rebate_rate: self.primary_rebate_rate,
            secondary_maker_fee_rate: self.secondary_maker_fee_rate,
            secondary_taker_fee_rate: self.secondary_taker_fee_rate,
            secondary_rebate_rate: self.secondary_rebate_rate,
            today: self.today.to_alias(),
        }
    }

    /// 获取数据目录路径
    fn get_data_directory() -> PathBuf {
        std::env::current_dir().unwrap()
    }

    /// 从文件加载统计数据
    fn load_from_file<P: AsRef<Path>>(path: P) -> Result<Self, Box<dyn std::error::Error>> {
        let path_ref = path.as_ref();
        tracing::info!("尝试从文件加载数据: {}", path_ref.display());

        // 确保文件存在
        if !path_ref.exists() {
            return Err(format!("文件不存在: {}", path_ref.display()).into());
        }

        let mut file = File::open(path_ref)?;
        let mut contents = String::new();
        file.read_to_string(&mut contents)?;

        let mut stats: StatsManager = serde_json::from_str(&contents)?;

        // 确保数据文件路径设置正确
        stats.data_file_path = path_ref.to_path_buf();

        // 加载后检查并修复数据一致性
        stats.validate_and_fix_consistency();

        tracing::info!("成功从文件加载数据: {}", path_ref.display());
        Ok(stats)
    }

    /// 安全地保存统计数据到文件（先写入临时文件，再覆盖）
    pub fn save_data(&self) {
        let path = &self.data_file_path;

        // 确认路径是否为空
        if path.as_os_str().is_empty() {
            let default_dir = Self::get_data_directory();
            let default_file = default_dir.join(format!("default_{}.json", self.server_name));
            tracing::error!("数据文件路径为空，使用默认路径: {}", default_file.display());

            // 创建一个临时StatsManager副本用于保存
            let mut stats_copy = self.clone();
            stats_copy.data_file_path = default_file;
            stats_copy.save_data();
            return;
        }

        // 生成临时文件路径
        let temp_path = path.with_extension("json.tmp");

        // 序列化数据
        let json_data = match serde_json::to_string_pretty(&self) {
            Ok(data) => data,
            Err(e) => {
                tracing::error!("序列化数据失败: {}", e);
                return;
            }
        };

        // 写入临时文件
        let write_result = (|| -> Result<(), std::io::Error> {
            let mut temp_file = File::create(&temp_path)?;
            temp_file.write_all(json_data.as_bytes())?;
            temp_file.sync_all()?; // 确保数据写入磁盘
            Ok(())
        })();

        if let Err(e) = write_result {
            tracing::error!("写入临时文件失败: {}, 错误: {}", temp_path.display(), e);
            return;
        }

        // 重命名临时文件覆盖原文件
        if let Err(e) = std::fs::rename(&temp_path, path) {
            tracing::error!(
                "重命名文件失败: {} -> {}, 错误: {}",
                temp_path.display(),
                path.display(),
                e
            );
            // 尝试删除临时文件
            let _ = std::fs::remove_file(&temp_path);
        }
    }

    /// 更新余额
    pub fn update_balance(
        &mut self,
        primary_balance: f64,
        secondary_balance: Option<f64>,
        available_primary: Option<f64>,
        available_secondary: Option<f64>,
    ) {
        self.current_balance = primary_balance;
        self.secondary_current_balance =
            secondary_balance.unwrap_or(self.secondary_current_balance);
        self.available_balance = available_primary.unwrap_or(primary_balance);
        self.secondary_available_balance =
            available_secondary.unwrap_or(self.secondary_current_balance);

        // 更新今日数据
        self.today.current_balance = primary_balance;
        self.today.secondary_current_balance = self.secondary_current_balance;
        self.today.available_balance = self.available_balance;
        self.today.secondary_available_balance = self.secondary_available_balance;

        // 保存数据
        self.save_data();
    }

    /// 更新当前节点持仓价值
    pub fn update_current_position_value(
        &mut self,
        total_value: f64,
        long_position_value: f64,
        short_position_value: f64,
    ) {
        // 更新当前节点持仓值
        self.current_position_value = total_value;
        self.current_long_position_value = long_position_value;
        self.current_short_position_value = short_position_value;

        // 保存数据
        self.save_data();
    }

    /// 更新所有节点持仓值
    pub fn update_total_position_value(
        &mut self,
        total_value: f64,
        long_position_value: f64,
        short_position_value: f64,
    ) {
        self.total_position_value = total_value;
        self.total_long_position_value = long_position_value;
        self.total_short_position_value = short_position_value;
    }

    /// 添加已结算的资金费
    pub fn add_funding_fee(&mut self, primary_fee: f64, secondary_fee: f64) {
        // 累加已结算资金费
        self.primary_funding_fee += primary_fee;
        self.secondary_funding_fee += secondary_fee;
        self.funding_fee = self.primary_funding_fee + self.secondary_funding_fee;

        // 保存数据
        self.save_data();
    }

    /// 更新未结算的预测资金费
    pub fn update_predicted_funding_fee(&mut self, primary_fee: f64, secondary_fee: f64) {
        // 更新今日未结算预测资金费
        self.today.primary_funding_fee = primary_fee;
        self.today.secondary_funding_fee = secondary_fee;
        self.today.funding_fee = primary_fee + secondary_fee;

        // 保存数据
        self.save_data();
    }

    /// 更新交易统计, profit为0则只改变volume
    pub fn update_trade_stats(
        &mut self,
        trade_volume: f64,
        maker_volume: f64,
        taker_volume: f64,
        profit: f64,
        is_single_close: bool,
    ) {
        if is_single_close {
            self.single_close_count += 1;
            self.single_close_profit += profit;
        }

        // 累计交易量
        self.volume += trade_volume;
        self.maker_volume += maker_volume;
        self.taker_volume += taker_volume;

        if profit == 0.0 {
            // 检查日期变化
            self.check_and_reset_today_stats();

            // 更新今日交易量
            self.today.volume += trade_volume;
            self.today.maker_volume += maker_volume;
            self.today.taker_volume += taker_volume;

            // 保存数据
            self.save_data();
            return;
        }

        // 判断是否盈利
        let is_win = profit > 0.0;

        // 更新计数
        self.count += 1;

        if is_win {
            self.success_count += 1;
            self.success_profit += profit;
        } else {
            self.failure_count += 1;
            self.failure_loss += profit;
        }

        // 计算净利润
        self.total_profit += profit;

        // 计算胜率
        if self.count > 0 {
            self.win_rate = (self.success_count as f64 / self.count as f64) * 100.0;
        }

        // 检查日期变化
        self.check_and_reset_today_stats();

        // 更新今日统计
        self.today.volume += trade_volume;
        self.today.maker_volume += maker_volume;
        self.today.taker_volume += taker_volume;
        self.today.count += 1.0;
        self.today.total_profit += profit;

        if is_win {
            self.today.success_count += 1.0;
            self.today.success_profit += profit;
        } else {
            self.today.failure_count += 1.0;
            self.today.failure_loss += profit;
        }

        // 计算今日胜率
        if self.today.count > 0.0 {
            self.today.win_rate = (self.today.success_count / self.today.count) * 100.0;
        }

        // 验证数据一致性
        self.validate_and_fix_consistency();

        // 保存数据
        self.save_data();
    }

    pub fn total_profit(&self) -> f64 {
        self.total_profit
    }

    /// 更新浮动盈亏
    pub fn update_floating_profit(&mut self, floating_profit: f64) {
        self.unrealized_pnl = floating_profit;

        // 保存数据
        self.save_data();
    }

    /// 转换为JSON格式（使用别名字段）
    pub fn to_alias_json(&self) -> Value {
        // 使用别名结构体进行序列化
        json!(self.to_alias())
    }

    pub fn initial_balance(&self) -> f64 {
        self.initial_balance + self.secondary_initial_balance
    }

    pub fn update_initial_balance(&mut self, balance: f64) {
        // 按比例更新 保留两位小数
        let ratio = self.initial_balance / self.initial_balance();
        self.initial_balance = balance * ratio;
        self.initial_balance = self.initial_balance.round();
        self.secondary_initial_balance = balance * (1.0 - ratio);
        self.secondary_initial_balance = self.secondary_initial_balance.round();

        // 保存数据
        self.save_data();
    }

    /// 检查并在必要时重置当日数据
    pub fn check_and_reset_today_stats(&mut self) {
        let current_time = time();

        // 创建东八区时区对象
        let tz_utc8 = FixedOffset::east_opt(8 * 3600).unwrap_or(FixedOffset::east_opt(0).unwrap());

        // 获取当前UTC+8时间的日期
        let current_date = match Utc.timestamp_opt(current_time, 0) {
            chrono::LocalResult::Single(dt) => {
                tz_utc8.from_utc_datetime(&dt.naive_utc()).date_naive()
            }
            _ => tz_utc8
                .from_utc_datetime(&Utc::now().naive_utc())
                .date_naive(),
        };

        // 获取统计数据中记录的日期（转换为UTC+8时区）
        let stats_date = match Utc.timestamp_opt(self.today.time, 0) {
            chrono::LocalResult::Single(dt) => {
                tz_utc8.from_utc_datetime(&dt.naive_utc()).date_naive()
            }
            _ => tz_utc8
                .from_utc_datetime(&Utc::now().naive_utc())
                .date_naive(),
        };

        // 如果日期不同，重置今日数据
        if current_date != stats_date {
            tracing::info!(
                "检测到日期变更: {} -> {}, 重置当日统计数据",
                stats_date,
                current_date
            );
            self.reset_today_stats();
        }
    }

    /// 重置当日统计数据
    fn reset_today_stats(&mut self) {
        let current_time = time();

        // 创建东八区时区对象
        let tz_utc8 = FixedOffset::east_opt(8 * 3600).unwrap_or(FixedOffset::east_opt(0).unwrap());

        // 获取当前UTC+8时间的日期
        let current_date = match Utc.timestamp_opt(current_time, 0) {
            chrono::LocalResult::Single(dt) => {
                tz_utc8.from_utc_datetime(&dt.naive_utc()).date_naive()
            }
            _ => tz_utc8
                .from_utc_datetime(&Utc::now().naive_utc())
                .date_naive(),
        };

        // 计算当天零点的UTC+8时间戳
        let today_start = if let Some(start_of_day) = current_date.and_hms_opt(0, 0, 0) {
            let utc_time = tz_utc8.from_local_datetime(&start_of_day).unwrap();
            utc_time.timestamp()
        } else {
            // 如果无法计算，则使用当前时间
            current_time
        };

        // 保留当前余额信息，重置其他统计数据
        self.today = TodayStats {
            time: today_start,
            initial_balance: self.current_balance, // 使用当前余额作为新一天的初始余额
            current_balance: self.current_balance,
            available_balance: self.available_balance,
            secondary_initial_balance: self.secondary_current_balance,
            secondary_current_balance: self.secondary_current_balance,
            secondary_available_balance: self.secondary_available_balance,
            volume: 0.0,
            maker_volume: 0.0,
            taker_volume: 0.0,
            count: 0.0,
            win_rate: 0.0,
            total_profit: 0.0,
            success_count: 0.0,
            success_profit: 0.0,
            failure_count: 0.0,
            failure_loss: 0.0,
            funding_fee: 0.0,
            primary_funding_fee: 0.0,
            secondary_funding_fee: 0.0,
        };

        // 保存数据更新
        self.save_data();

        tracing::info!("已重置当日统计数据，基准时间：UTC+8 {}", current_date);
    }

    /// 验证并修复统计数据的一致性
    pub fn validate_and_fix_consistency(&mut self) {
        // 确保今日数据不超过总数据
        self.today.volume = self.today.volume.min(self.volume);
        self.today.maker_volume = self.today.maker_volume.min(self.maker_volume);
        self.today.taker_volume = self.today.taker_volume.min(self.taker_volume);

        // 确保成功次数和失败次数等于总次数
        if (self.success_count + self.failure_count) != self.count {
            tracing::warn!("发现统计数据不一致，进行修复");
            self.count = self.success_count + self.failure_count;
        }

        // 确保今日成功次数和失败次数等于总次数
        if (self.today.success_count + self.today.failure_count) != self.today.count {
            tracing::warn!("发现今日统计数据不一致，进行修复");
            self.today.count = self.today.success_count + self.today.failure_count;
        }

        // 重新计算胜率
        if self.count > 0 {
            self.win_rate = (self.success_count as f64 / self.count as f64) * 100.0;
        }

        if self.today.count > 0.0 {
            self.today.win_rate = (self.today.success_count / self.today.count) * 100.0;
        }

        // 修复后保存数据
        self.save_data();
    }
}
