use std::sync::atomic::{AtomicBool, Ordering};

/// 配置管理类（内部使用，不暴露给Python）
pub struct Config {
    pub server_name: String,
    /// 是否停止策略
    pub is_stopped: AtomicBool,
    /// 是否禁用交易功能
    pub is_trading_disabled: AtomicBool,
    /// 是否禁用开仓功能
    pub is_opening_disabled: AtomicBool,
    /// 开始平仓
    pub is_closing_enabled: AtomicBool,
}

impl Config {
    /// 创建配置实例
    pub fn new(server_name: String) -> Self {
        Config {
            server_name,
            is_stopped: AtomicBool::new(false),
            is_trading_disabled: AtomicBool::new(false),
            is_opening_disabled: AtomicBool::new(false),
            is_closing_enabled: AtomicBool::new(false),
        }
    }

    /// 检查是否已停止
    pub fn is_stopped_internal(&self) -> bool {
        self.is_stopped.load(Ordering::Relaxed)
    }

    /// 检查是否已停止交易
    pub fn is_trade_stopped_internal(&self) -> bool {
        self.is_trading_disabled
            .load(std::sync::atomic::Ordering::Relaxed)
    }

    /// 检查是否已停止开仓
    pub fn is_opening_disabled_internal(&self) -> bool {
        self.is_opening_disabled
            .load(std::sync::atomic::Ordering::Relaxed)
    }

    /// 检查是否已开始平仓
    pub fn is_closing_enabled_internal(&self) -> bool {
        self.is_closing_enabled
            .load(std::sync::atomic::Ordering::Relaxed)
    }

    /// 设置停止状态
    pub fn set_stopped(&self, stopped: bool) {
        self.is_stopped.store(stopped, Ordering::Relaxed);
    }

    /// 设置停止交易状态
    pub fn set_trade_stopped(&self, stopped: bool) {
        self.is_trading_disabled.store(stopped, Ordering::Relaxed);
    }

    /// 设置停止开仓状态
    pub fn set_opening_disabled(&self, stopped: bool) {
        self.is_opening_disabled.store(stopped, Ordering::Relaxed);
    }

    /// 设置开始平仓状态
    pub fn set_closing_enabled(&self, enabled: bool) {
        self.is_trading_disabled.store(enabled, Ordering::Relaxed);
        self.is_closing_enabled.store(enabled, Ordering::Relaxed);
    }
}
