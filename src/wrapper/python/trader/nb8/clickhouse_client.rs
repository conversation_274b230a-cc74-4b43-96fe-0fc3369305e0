use clickhouse::{Client, Row};
use serde::{Deserialize, Serialize};
use std::error::Error;
use tracing::{error, info, warn};
use url::Url;

use super::stats::{StatsManager, TodayStats};

/// ClickHouse客户端配置
#[derive(Debug, <PERSON>lone)]
pub struct ClickHouseConfig {
    pub url: String,
    pub database: String,
    pub username: Option<String>,
    pub password: Option<String>,
    pub stats_table: String,
    pub today_stats_table: String,
}

impl Default for ClickHouseConfig {
    fn default() -> Self {
        Self {
            url: "http://localhost:8123".to_string(),
            database: "trading".to_string(),
            username: None,
            password: None,
            stats_table: "stats_manager".to_string(),
            today_stats_table: "today_stats".to_string(),
        }
    }
}

/// ClickHouse中的StatsManager表结构
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Row)]
pub struct StatsManagerRow {
    pub timestamp: u32,
    pub server_name: String,
    pub initial_balance: f64,
    pub current_balance: f64,
    pub available_balance: f64,
    pub secondary_initial_balance: f64,
    pub secondary_current_balance: f64,
    pub secondary_available_balance: f64,
    pub volume: f64,
    pub maker_volume: f64,
    pub taker_volume: f64,
    pub count: i32,
    pub win_rate: f64,
    pub total_profit: f64,
    pub success_count: i32,
    pub success_profit: f64,
    pub failure_count: i32,
    pub failure_loss: f64,
    pub unrealized_pnl: f64,
    pub single_close_count: i32,
    pub single_close_profit: f64,
    pub funding_fee: f64,
    pub primary_funding_fee: f64,
    pub secondary_funding_fee: f64,
    pub cost: f64,
    pub open_threshold: f64,
    pub funding_rate_threshold: f64,
    pub max_position_ratio: f64,
    pub max_leverage: i32,
    pub primary_maker_fee_rate: f64,
    pub primary_taker_fee_rate: f64,
    pub primary_rebate_rate: f64,
    pub secondary_maker_fee_rate: f64,
    pub secondary_taker_fee_rate: f64,
    pub secondary_rebate_rate: f64,
}

/// ClickHouse中的TodayStats表结构
#[derive(Debug, Clone, Serialize, Deserialize, Row)]
pub struct TodayStatsRow {
    pub timestamp: u32,
    pub server_name: String,
    pub time: i64,
    pub initial_balance: f64,
    pub current_balance: f64,
    pub available_balance: f64,
    pub secondary_initial_balance: f64,
    pub secondary_current_balance: f64,
    pub secondary_available_balance: f64,
    pub volume: f64,
    pub maker_volume: f64,
    pub taker_volume: f64,
    pub count: f64,
    pub win_rate: f64,
    pub total_profit: f64,
    pub success_count: f64,
    pub success_profit: f64,
    pub failure_count: f64,
    pub failure_loss: f64,
    pub funding_fee: f64,
    pub primary_funding_fee: f64,
    pub secondary_funding_fee: f64,
}

/// ClickHouse客户端
pub struct ClickHouseClient {
    client: Client,
    config: ClickHouseConfig,
}

impl ClickHouseClient {
    /// 创建新的ClickHouse客户端
    pub fn new(config: ClickHouseConfig) -> Result<Self, Box<dyn Error>> {
        let mut url = Url::parse(&config.url)?;

        // 设置数据库
        url.set_path(&format!("/{}", config.database));

        // 设置认证信息
        if let (Some(username), Some(password)) = (&config.username, &config.password) {
            url.set_username(username).map_err(|_| "Invalid username")?;
            url.set_password(Some(password))
                .map_err(|_| "Invalid password")?;
        }

        let client = Client::with_url(url.as_str());

        info!("ClickHouse客户端已创建，连接到: {}", config.url);

        Ok(Self { client, config })
    }

    /// 测试连接
    pub async fn test_connection(&self) -> Result<(), Box<dyn Error>> {
        let result: String = self
            .client
            .query("SELECT 'connection_test'")
            .fetch_one()
            .await?;
        if result == "connection_test" {
            info!("ClickHouse连接测试成功");
            Ok(())
        } else {
            Err("连接测试失败".into())
        }
    }

    /// 创建数据库表
    pub async fn create_tables(&self) -> Result<(), Box<dyn Error>> {
        // 创建StatsManager表
        let stats_table_sql = format!(
            r#"
            CREATE TABLE IF NOT EXISTS {}.{} (
                timestamp UInt32,
                server_name String,
                initial_balance Float64,
                current_balance Float64,
                available_balance Float64,
                secondary_initial_balance Float64,
                secondary_current_balance Float64,
                secondary_available_balance Float64,
                volume Float64,
                maker_volume Float64,
                taker_volume Float64,
                count Int32,
                win_rate Float64,
                total_profit Float64,
                success_count Int32,
                success_profit Float64,
                failure_count Int32,
                failure_loss Float64,
                unrealized_pnl Float64,
                single_close_count Int32,
                single_close_profit Float64,
                funding_fee Float64,
                primary_funding_fee Float64,
                secondary_funding_fee Float64,
                cost Float64,
                open_threshold Float64,
                funding_rate_threshold Float64,
                max_position_ratio Float64,
                max_leverage Int32,
                primary_maker_fee_rate Float64,
                primary_taker_fee_rate Float64,
                primary_rebate_rate Float64,
                secondary_maker_fee_rate Float64,
                secondary_taker_fee_rate Float64,
                secondary_rebate_rate Float64
            ) ENGINE = MergeTree()
            ORDER BY (server_name, timestamp)
            "#,
            self.config.database, self.config.stats_table
        );

        self.client.query(&stats_table_sql).execute().await?;
        info!("StatsManager表创建成功: {}", self.config.stats_table);

        // 创建TodayStats表
        let today_stats_table_sql = format!(
            r#"
            CREATE TABLE IF NOT EXISTS {}.{} (
                timestamp UInt32,
                server_name String,
                time Int64,
                initial_balance Float64,
                current_balance Float64,
                available_balance Float64,
                secondary_initial_balance Float64,
                secondary_current_balance Float64,
                secondary_available_balance Float64,
                volume Float64,
                maker_volume Float64,
                taker_volume Float64,
                count Float64,
                win_rate Float64,
                total_profit Float64,
                success_count Float64,
                success_profit Float64,
                failure_count Float64,
                failure_loss Float64,
                funding_fee Float64,
                primary_funding_fee Float64,
                secondary_funding_fee Float64
            ) ENGINE = MergeTree()
            ORDER BY (server_name, timestamp)
            "#,
            self.config.database, self.config.today_stats_table
        );

        self.client.query(&today_stats_table_sql).execute().await?;
        info!("TodayStats表创建成功: {}", self.config.today_stats_table);

        Ok(())
    }

    /// 将StatsManager转换为ClickHouse行格式
    fn stats_manager_to_row(&self, stats: &StatsManager, timestamp: u32) -> StatsManagerRow {
        StatsManagerRow {
            timestamp,
            server_name: stats.server_name.clone(),
            initial_balance: stats.initial_balance,
            current_balance: stats.current_balance,
            available_balance: stats.available_balance,
            secondary_initial_balance: stats.secondary_initial_balance,
            secondary_current_balance: stats.secondary_current_balance,
            secondary_available_balance: stats.secondary_available_balance,
            volume: stats.volume,
            maker_volume: stats.maker_volume,
            taker_volume: stats.taker_volume,
            count: stats.count,
            win_rate: stats.win_rate,
            total_profit: stats.total_profit,
            success_count: stats.success_count,
            success_profit: stats.success_profit,
            failure_count: stats.failure_count,
            failure_loss: stats.failure_loss,
            unrealized_pnl: stats.unrealized_pnl,
            single_close_count: stats.single_close_count,
            single_close_profit: stats.single_close_profit,
            funding_fee: stats.funding_fee,
            primary_funding_fee: stats.primary_funding_fee,
            secondary_funding_fee: stats.secondary_funding_fee,
            cost: stats.cost,
            open_threshold: stats.open_threshold,
            funding_rate_threshold: stats.funding_rate_threshold,
            max_position_ratio: stats.max_position_ratio,
            max_leverage: stats.max_leverage,
            primary_maker_fee_rate: stats.primary_maker_fee_rate,
            primary_taker_fee_rate: stats.primary_taker_fee_rate,
            primary_rebate_rate: stats.primary_rebate_rate,
            secondary_maker_fee_rate: stats.secondary_maker_fee_rate,
            secondary_taker_fee_rate: stats.secondary_taker_fee_rate,
            secondary_rebate_rate: stats.secondary_rebate_rate,
        }
    }

    /// 将TodayStats转换为ClickHouse行格式
    fn today_stats_to_row(
        &self,
        today_stats: &TodayStats,
        server_name: &str,
        timestamp: u32,
    ) -> TodayStatsRow {
        TodayStatsRow {
            timestamp,
            server_name: server_name.to_string(),
            time: today_stats.time,
            initial_balance: today_stats.initial_balance,
            current_balance: today_stats.current_balance,
            available_balance: today_stats.available_balance,
            secondary_initial_balance: today_stats.secondary_initial_balance,
            secondary_current_balance: today_stats.secondary_current_balance,
            secondary_available_balance: today_stats.secondary_available_balance,
            volume: today_stats.volume,
            maker_volume: today_stats.maker_volume,
            taker_volume: today_stats.taker_volume,
            count: today_stats.count,
            win_rate: today_stats.win_rate,
            total_profit: today_stats.total_profit,
            success_count: today_stats.success_count,
            success_profit: today_stats.success_profit,
            failure_count: today_stats.failure_count,
            failure_loss: today_stats.failure_loss,
            funding_fee: today_stats.funding_fee,
            primary_funding_fee: today_stats.primary_funding_fee,
            secondary_funding_fee: today_stats.secondary_funding_fee,
        }
    }

    /// 写入单个StatsManager记录
    pub async fn insert_stats_manager(&self, stats: &StatsManager) -> Result<(), Box<dyn Error>> {
        let timestamp = chrono::Utc::now().timestamp() as u32;
        let row = self.stats_manager_to_row(stats, timestamp);

        let mut insert = self.client.insert(&self.config.stats_table)?;
        insert.write(&row).await?;
        insert.end().await?;

        info!("成功写入StatsManager记录: {}", stats.server_name);
        Ok(())
    }

    /// 写入单个TodayStats记录
    pub async fn insert_today_stats(
        &self,
        today_stats: &TodayStats,
        server_name: &str,
    ) -> Result<(), Box<dyn Error>> {
        let timestamp = chrono::Utc::now().timestamp() as u32;
        let row = self.today_stats_to_row(today_stats, server_name, timestamp);

        let mut insert = self.client.insert(&self.config.today_stats_table)?;
        insert.write(&row).await?;
        insert.end().await?;

        info!("成功写入TodayStats记录: {}", server_name);
        Ok(())
    }

    /// 批量写入StatsManager记录
    pub async fn batch_insert_stats_manager(
        &self,
        stats_list: &[&StatsManager],
    ) -> Result<(), Box<dyn Error>> {
        if stats_list.is_empty() {
            warn!("StatsManager列表为空，跳过写入");
            return Ok(());
        }

        let timestamp = chrono::Utc::now().timestamp() as u32;
        let mut insert = self.client.insert(&self.config.stats_table)?;

        for stats in stats_list {
            let row = self.stats_manager_to_row(stats, timestamp);
            insert.write(&row).await?;
        }

        insert.end().await?;
        info!("成功批量写入{}条StatsManager记录", stats_list.len());
        Ok(())
    }

    /// 批量写入TodayStats记录
    pub async fn batch_insert_today_stats(
        &self,
        today_stats_list: &[(&TodayStats, &str)],
    ) -> Result<(), Box<dyn Error>> {
        if today_stats_list.is_empty() {
            warn!("TodayStats列表为空，跳过写入");
            return Ok(());
        }

        let timestamp = chrono::Utc::now().timestamp() as u32;
        let mut insert = self.client.insert(&self.config.today_stats_table)?;

        for (today_stats, server_name) in today_stats_list {
            let row = self.today_stats_to_row(today_stats, server_name, timestamp);
            insert.write(&row).await?;
        }

        insert.end().await?;
        info!("成功批量写入{}条TodayStats记录", today_stats_list.len());
        Ok(())
    }

    /// 写入完整的StatsManager数据（包括today_stats）
    pub async fn insert_complete_stats(&self, stats: &StatsManager) -> Result<(), Box<dyn Error>> {
        // 写入StatsManager主记录
        self.insert_stats_manager(stats).await?;

        // 写入TodayStats记录
        self.insert_today_stats(&stats.today, &stats.server_name)
            .await?;

        info!("成功写入完整的统计数据: {}", stats.server_name);
        Ok(())
    }

    /// 获取配置信息
    pub fn get_config(&self) -> &ClickHouseConfig {
        &self.config
    }
}
