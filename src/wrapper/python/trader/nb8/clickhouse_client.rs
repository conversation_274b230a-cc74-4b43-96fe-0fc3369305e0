use reqwest::{Client, Url};
use std::error::Error;
use tracing::info;

use super::stats::{StatsManager, TodayStats};

/// ClickHouse客户端配置
#[derive(Debug, Clone)]
pub struct ClickHouseConfig {
    pub url: String,
    pub database: String,
    pub username: Option<String>,
    pub password: Option<String>,
    pub stats_table: String,
    pub today_stats_table: String,
}

impl Default for ClickHouseConfig {
    fn default() -> Self {
        Self {
            url: "http://localhost:8123".to_string(),
            database: "trading".to_string(),
            username: None,
            password: None,
            stats_table: "stats_manager".to_string(),
            today_stats_table: "today_stats".to_string(),
        }
    }
}

/// ClickHouse客户端
pub struct ClickHouseClient {
    client: Client,
    config: ClickHouseConfig,
}

impl ClickHouseClient {
    /// 创建新的ClickHouse客户端
    pub fn new(config: ClickHouseConfig) -> Result<Self, Box<dyn Error>> {
        let client = Client::new();

        info!("ClickHouse客户端已创建，连接到: {}", config.url);

        Ok(Self { client, config })
    }

    /// 构建请求URL
    fn build_url(&self, query: &str) -> Result<String, Box<dyn Error>> {
        let mut url = Url::parse(&self.config.url)?;

        // 添加查询参数
        url.query_pairs_mut()
            .append_pair("query", query)
            .append_pair("database", &self.config.database);

        // 添加认证信息
        if let (Some(username), Some(password)) = (&self.config.username, &self.config.password) {
            url.query_pairs_mut()
                .append_pair("user", username)
                .append_pair("password", password);
        }

        Ok(url.to_string())
    }

    /// 执行SQL查询
    async fn execute_query(&self, query: &str) -> Result<String, Box<dyn Error>> {
        let url = self.build_url(query)?;

        let response = self
            .client
            .post(&url)
            .header("Content-Type", "text/plain")
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(format!("ClickHouse查询失败: {}", error_text).into());
        }

        Ok(response.text().await?)
    }

    /// 测试连接
    pub async fn test_connection(&self) -> Result<(), Box<dyn Error>> {
        let result = self.execute_query("SELECT 'connection_test'").await?;
        if result.trim() == "connection_test" {
            info!("ClickHouse连接测试成功");
            Ok(())
        } else {
            Err("连接测试失败".into())
        }
    }

    /// 创建数据库表
    pub async fn create_tables(&self) -> Result<(), Box<dyn Error>> {
        // 创建StatsManager表
        let stats_table_sql = format!(
            r#"
            CREATE TABLE IF NOT EXISTS {}.{} (
                timestamp UInt32,
                server_name String,
                initial_balance Float64,
                current_balance Float64,
                available_balance Float64,
                secondary_initial_balance Float64,
                secondary_current_balance Float64,
                secondary_available_balance Float64,
                volume Float64,
                maker_volume Float64,
                taker_volume Float64,
                count Int32,
                win_rate Float64,
                total_profit Float64,
                success_count Int32,
                success_profit Float64,
                failure_count Int32,
                failure_loss Float64,
                unrealized_pnl Float64,
                single_close_count Int32,
                single_close_profit Float64,
                funding_fee Float64,
                primary_funding_fee Float64,
                secondary_funding_fee Float64,
                cost Float64,
                open_threshold Float64,
                funding_rate_threshold Float64,
                max_position_ratio Float64,
                max_leverage Int32,
                primary_maker_fee_rate Float64,
                primary_taker_fee_rate Float64,
                primary_rebate_rate Float64,
                secondary_maker_fee_rate Float64,
                secondary_taker_fee_rate Float64,
                secondary_rebate_rate Float64
            ) ENGINE = MergeTree()
            ORDER BY (server_name, timestamp)
            "#,
            self.config.database, self.config.stats_table
        );

        self.execute_query(&stats_table_sql).await?;
        info!("StatsManager表创建成功: {}", self.config.stats_table);

        // 创建TodayStats表
        let today_stats_table_sql = format!(
            r#"
            CREATE TABLE IF NOT EXISTS {}.{} (
                timestamp UInt32,
                server_name String,
                time Int64,
                initial_balance Float64,
                current_balance Float64,
                available_balance Float64,
                secondary_initial_balance Float64,
                secondary_current_balance Float64,
                secondary_available_balance Float64,
                volume Float64,
                maker_volume Float64,
                taker_volume Float64,
                count Float64,
                win_rate Float64,
                total_profit Float64,
                success_count Float64,
                success_profit Float64,
                failure_count Float64,
                failure_loss Float64,
                funding_fee Float64,
                primary_funding_fee Float64,
                secondary_funding_fee Float64
            ) ENGINE = MergeTree()
            ORDER BY (server_name, timestamp)
            "#,
            self.config.database, self.config.today_stats_table
        );

        self.execute_query(&today_stats_table_sql).await?;
        info!("TodayStats表创建成功: {}", self.config.today_stats_table);

        Ok(())
    }

    /// 写入StatsManager数据
    pub async fn insert_stats_manager(&self, stats: &StatsManager) -> Result<(), Box<dyn Error>> {
        let timestamp = chrono::Utc::now().timestamp() as u32;

        let insert_sql = format!(
            r#"
            INSERT INTO {}.{} VALUES (
                {}, '{}', {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}
            )
            "#,
            self.config.database,
            self.config.stats_table,
            timestamp,
            stats.server_name,
            stats.initial_balance,
            stats.current_balance,
            stats.available_balance,
            stats.secondary_initial_balance,
            stats.secondary_current_balance,
            stats.secondary_available_balance,
            stats.volume,
            stats.maker_volume,
            stats.taker_volume,
            stats.count,
            stats.win_rate,
            stats.total_profit,
            stats.success_count,
            stats.success_profit,
            stats.failure_count,
            stats.failure_loss,
            stats.unrealized_pnl,
            stats.single_close_count,
            stats.single_close_profit,
            stats.funding_fee,
            stats.primary_funding_fee,
            stats.secondary_funding_fee,
            stats.cost,
            stats.open_threshold,
            stats.funding_rate_threshold,
            stats.max_position_ratio,
            stats.max_leverage,
            stats.primary_maker_fee_rate,
            stats.primary_taker_fee_rate,
            stats.primary_rebate_rate,
            stats.secondary_maker_fee_rate,
            stats.secondary_taker_fee_rate,
            stats.secondary_rebate_rate
        );

        self.execute_query(&insert_sql).await?;
        info!("成功写入StatsManager记录: {}", stats.server_name);
        Ok(())
    }

    /// 写入TodayStats数据
    pub async fn insert_today_stats(
        &self,
        today_stats: &TodayStats,
        server_name: &str,
    ) -> Result<(), Box<dyn Error>> {
        let timestamp = chrono::Utc::now().timestamp() as u32;

        let insert_sql = format!(
            r#"
            INSERT INTO {}.{} VALUES (
                {}, '{}', {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}
            )
            "#,
            self.config.database,
            self.config.today_stats_table,
            timestamp,
            server_name,
            today_stats.time,
            today_stats.initial_balance,
            today_stats.current_balance,
            today_stats.available_balance,
            today_stats.secondary_initial_balance,
            today_stats.secondary_current_balance,
            today_stats.secondary_available_balance,
            today_stats.volume,
            today_stats.maker_volume,
            today_stats.taker_volume,
            today_stats.count,
            today_stats.win_rate,
            today_stats.total_profit,
            today_stats.success_count,
            today_stats.success_profit,
            today_stats.failure_count,
            today_stats.failure_loss,
            today_stats.funding_fee,
            today_stats.primary_funding_fee,
            today_stats.secondary_funding_fee
        );

        self.execute_query(&insert_sql).await?;
        info!("成功写入TodayStats记录: {}", server_name);
        Ok(())
    }

    /// 写入完整的StatsManager数据（包括today_stats）
    pub async fn insert_complete_stats(&self, stats: &StatsManager) -> Result<(), Box<dyn Error>> {
        // 写入StatsManager主记录
        self.insert_stats_manager(stats).await?;

        // 写入TodayStats记录
        self.insert_today_stats(&stats.today, &stats.server_name)
            .await?;

        info!("成功写入完整的统计数据: {}", stats.server_name);
        Ok(())
    }

    /// 批量写入StatsManager数据
    pub async fn batch_insert_stats_manager(
        &self,
        stats_list: &[&StatsManager],
    ) -> Result<(), Box<dyn Error>> {
        if stats_list.is_empty() {
            info!("StatsManager列表为空，跳过写入");
            return Ok(());
        }

        let timestamp = chrono::Utc::now().timestamp() as u32;
        let mut values = Vec::new();

        for stats in stats_list {
            let value = format!(
                "({}, '{}', {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {})",
                timestamp,
                stats.server_name,
                stats.initial_balance,
                stats.current_balance,
                stats.available_balance,
                stats.secondary_initial_balance,
                stats.secondary_current_balance,
                stats.secondary_available_balance,
                stats.volume,
                stats.maker_volume,
                stats.taker_volume,
                stats.count,
                stats.win_rate,
                stats.total_profit,
                stats.success_count,
                stats.success_profit,
                stats.failure_count,
                stats.failure_loss,
                stats.unrealized_pnl,
                stats.single_close_count,
                stats.single_close_profit,
                stats.funding_fee,
                stats.primary_funding_fee,
                stats.secondary_funding_fee,
                stats.cost,
                stats.open_threshold,
                stats.funding_rate_threshold,
                stats.max_position_ratio,
                stats.max_leverage,
                stats.primary_maker_fee_rate,
                stats.primary_taker_fee_rate,
                stats.primary_rebate_rate,
                stats.secondary_maker_fee_rate,
                stats.secondary_taker_fee_rate,
                stats.secondary_rebate_rate
            );
            values.push(value);
        }

        let insert_sql = format!(
            "INSERT INTO {}.{} VALUES {}",
            self.config.database,
            self.config.stats_table,
            values.join(", ")
        );

        self.execute_query(&insert_sql).await?;
        info!("成功批量写入{}条StatsManager记录", stats_list.len());
        Ok(())
    }

    /// 批量写入TodayStats数据
    pub async fn batch_insert_today_stats(
        &self,
        today_stats_list: &[(&TodayStats, &str)],
    ) -> Result<(), Box<dyn Error>> {
        if today_stats_list.is_empty() {
            info!("TodayStats列表为空，跳过写入");
            return Ok(());
        }

        let timestamp = chrono::Utc::now().timestamp() as u32;
        let mut values = Vec::new();

        for (today_stats, server_name) in today_stats_list {
            let value = format!(
                "({}, '{}', {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {})",
                timestamp,
                server_name,
                today_stats.time,
                today_stats.initial_balance,
                today_stats.current_balance,
                today_stats.available_balance,
                today_stats.secondary_initial_balance,
                today_stats.secondary_current_balance,
                today_stats.secondary_available_balance,
                today_stats.volume,
                today_stats.maker_volume,
                today_stats.taker_volume,
                today_stats.count,
                today_stats.win_rate,
                today_stats.total_profit,
                today_stats.success_count,
                today_stats.success_profit,
                today_stats.failure_count,
                today_stats.failure_loss,
                today_stats.funding_fee,
                today_stats.primary_funding_fee,
                today_stats.secondary_funding_fee
            );
            values.push(value);
        }

        let insert_sql = format!(
            "INSERT INTO {}.{} VALUES {}",
            self.config.database,
            self.config.today_stats_table,
            values.join(", ")
        );

        self.execute_query(&insert_sql).await?;
        info!("成功批量写入{}条TodayStats记录", today_stats_list.len());
        Ok(())
    }

    /// 获取配置信息
    pub fn get_config(&self) -> &ClickHouseConfig {
        &self.config
    }
}
