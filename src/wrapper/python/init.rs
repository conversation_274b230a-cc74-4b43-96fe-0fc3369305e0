use std::{env::current_dir, path::PathBuf, sync::Arc, time::Duration};

use dashmap::DashMap;
use pyo3::{
    Python,
    ffi::c_str,
    types::{PyAnyMethods, PyList, PyListMethods, PyModule},
};
use pythonize::pythonize;
use quant_common::Result;

use tokio::runtime::Handle;
use trader::{config::LauncherMode, launcher::Launcher};

use crate::{
    config::model::TraderVersion,
    wrapper::python::{
        code::get_strategy_info,
        strategy::PyStrategyWrapper,
        trader::{
            Trader,
            python_v2::{TraderV2, traderv2},
            traderv1,
        },
    },
};

pub async fn get_strategy(
    launcher: &mut Launcher,
    handle: Handle,
    trader_version: TraderVersion,
    strategy_path: &str,
) -> Result<PyStrategyWrapper> {
    let strategy_info = get_strategy_info(launcher, strategy_path)?;
    let log_receiver = launcher.log_receiver();
    let config = launcher.config();

    let ex_configs = config.exchanges.clone();
    let dex_configs = config.dex.exchanges.clone();

    let dex_manager = launcher.dex_manager();
    let arc_dex_sync = launcher.arc_dex_sync();
    let execution_engine = launcher.execution_engine();

    pyo3::append_to_inittab!(traderv1);
    pyo3::append_to_inittab!(traderv2);

    let strategy = Python::with_gil(|py| -> Result<PyStrategyWrapper> {
        let version = py.version_info();
        info!(
            "当前Python版本: {}.{}.{}",
            version.major, version.minor, version.patch
        );

        // 设置SIGINT为默认行为
        let signal = py.import("signal")?;
        // Set SIGINT to have the default action
        signal
            .getattr("signal")?
            .call1((signal.getattr("SIGINT")?, signal.getattr("SIG_IGN")?))?;

        let mut paths = vec![current_dir()?];

        if config.launcher_mode == LauncherMode::Bin
            && let Some(parent) = PathBuf::from(strategy_path).parent()
        {
            paths.push(parent.to_path_buf());
        }

        debug!("添加到sys.path的目录: {:?}", paths);

        // 导入base_strategy模块
        let code = include_str!("../../../misc/base_strategy.py");
        let code_cstr = std::ffi::CString::new(code)?;
        PyModule::from_code(
            py,
            code_cstr.as_c_str(),
            c_str!("base_strategy.py"),
            c_str!("base_strategy"),
        )?;
        info!("预加载base_strategy模块成功");

        let syspath = py
            .import("sys")?
            .getattr("path")?
            .downcast_into::<PyList>()?;
        for path in paths {
            syspath.insert(0, path.to_str().unwrap())?;
        }

        let traderv1 = Trader {
            dex_manager: dex_manager.clone(),
            dex_sync: arc_dex_sync.clone(),
            execution: execution_engine.clone(),
            time_out: Duration::from_secs(config.dex.sync_timeout),
            rate_limit: config.log.rate_limit.clone(),
            cache: launcher.cache(),
            handle,
            log_receiver,
            web_client: None,
            log_timestamps: Arc::new(DashMap::new()),
        };

        let traderv2 = TraderV2(traderv1);

        for module in strategy_info.modules {
            PyModule::from_code(
                py,
                module.code.as_c_str(),
                module.file_name.as_c_str(),
                module.name.as_c_str(),
            )?;
        }

        info!("初始化Python策略...");
        let strategy_module = PyModule::from_code(
            py,
            strategy_info.strategy.as_c_str(),
            c_str!(""),
            c_str!(""),
        )?;
        info!("初始化策略模块成功");

        let py_strategy = match trader_version {
            TraderVersion::V1 => strategy_module.getattr("Strategy")?.call1((
                pythonize(py, &ex_configs)?,
                pythonize(py, &dex_configs)?,
                pythonize(py, &strategy_info.config)?,
                traderv2.0,
            ))?,
            TraderVersion::V2 => strategy_module.getattr("Strategy")?.call1((
                pythonize(py, &ex_configs)?,
                pythonize(py, &dex_configs)?,
                pythonize(py, &strategy_info.config)?,
                traderv2,
            ))?,
        };
        Ok(PyStrategyWrapper::new(
            py_strategy.into(),
            execution_engine.clone(),
            trader_version,
        ))
    })?;
    Ok(strategy)
}
