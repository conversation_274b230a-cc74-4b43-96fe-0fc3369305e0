//! Python策略代码加载和处理模块
//!
//! 该模块负责:
//! - 加载Python策略文件
//! - 处理Python模块的依赖关系
//! - 维护Python模块的命名空间
//! - 支持多种配置格式（TOML、JSON、YAML）

// use algo_common::msg::StrategyFile;
use quant_common::{Result, qerror};
use sonic_rs::Value;
use std::{ffi::CString, fs::read_to_string, path::PathBuf};
use trader::launcher::Launcher;

/// 支持的配置文件格式
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum ConfigFormat {
    Toml,
    Json,
    Yaml,
    Csv,
}

impl ConfigFormat {
    /// 根据文件扩展名识别配置格式
    pub fn from_extension(path: &str) -> Option<Self> {
        let path = path.to_lowercase();
        if path.ends_with(".toml") {
            Some(ConfigFormat::Toml)
        } else if path.ends_with(".json") {
            Some(ConfigFormat::Json)
        } else if path.ends_with(".yaml") || path.ends_with(".yml") {
            Some(ConfigFormat::Yaml)
        } else if path.ends_with(".csv") {
            Some(ConfigFormat::Csv)
        } else {
            None
        }
    }
}

/// Python策略信息
#[derive(Debug)]
pub struct PyStrategyInfo {
    /// 主策略代码
    pub strategy: CString,
    /// 依赖的Python模块列表
    pub modules: Vec<PyModuleInfo>,
    /// 策略配置
    pub config: Value,
}

/// Python模块信息
#[derive(Debug)]
pub struct PyModuleInfo {
    /// Python模块名 (如 "utils.foo")
    pub name: CString,
    /// 文件名 (如 "utils/foo.py")
    pub file_name: CString,
    /// 模块代码
    pub code: CString,
}

/// 解析配置文件内容
///
/// # Arguments
///
/// * `content` - 配置文件内容
/// * `format` - 配置格式
///
/// # Returns
///
/// 返回解析后的配置值
fn parse_config_content(content: &str, format: ConfigFormat) -> Result<Value> {
    match format {
        ConfigFormat::Toml => {
            let toml_value: toml::Value =
                toml::from_str(content).map_err(|e| qerror!("解析TOML配置失败: {}", e))?;
            sonic_rs::to_value(&toml_value).map_err(|e| qerror!("转换TOML配置到Value失败: {}", e))
        }
        ConfigFormat::Json => {
            let json_value: serde_json::Value =
                serde_json::from_str(content).map_err(|e| qerror!("解析JSON配置失败: {}", e))?;
            sonic_rs::to_value(&json_value).map_err(|e| qerror!("转换JSON配置到Value失败: {}", e))
        }
        ConfigFormat::Yaml => {
            let yaml_value: serde_yaml::Value =
                serde_yaml::from_str(content).map_err(|e| qerror!("解析YAML配置失败: {}", e))?;
            sonic_rs::to_value(&yaml_value).map_err(|e| qerror!("转换YAML配置到Value失败: {}", e))
        }
        ConfigFormat::Csv => {
            let mut rdr = csv::ReaderBuilder::new()
                .has_headers(true)
                .from_reader(content.as_bytes());
            let headers = rdr
                .headers()
                .map_err(|e| qerror!("读取CSV头失败: {}", e))?
                .clone();
            let mut rows = Vec::new();
            for result in rdr.records() {
                let record = result.map_err(|e| qerror!("读取CSV记录失败: {}", e))?;
                let mut obj = serde_json::Map::new();
                for (i, field) in record.iter().enumerate() {
                    let key = headers.get(i).unwrap_or("");
                    obj.insert(
                        key.to_string(),
                        serde_json::Value::String(field.to_string()),
                    );
                }
                rows.push(serde_json::Value::Object(obj));
            }
            sonic_rs::to_value(&rows).map_err(|e| qerror!("转换CSV配置到Value失败: {}", e))
        }
    }
}

/// 获取策略相关信息
///
/// # Arguments
///
/// * `launcher` - 策略启动器
///
/// # Returns
///
/// 返回包含策略代码、依赖模块和配置的信息结构
pub fn get_strategy_info(launcher: &mut Launcher, strategy_path: &str) -> Result<PyStrategyInfo> {
    match launcher.config().launcher_mode {
        trader::config::LauncherMode::Bin => load_from_files(launcher, strategy_path),
        trader::config::LauncherMode::AlgoRunner => unimplemented!(),
    }
}

/// 从本地文件加载策略
fn load_from_files(launcher: &Launcher, strategy_path: &str) -> Result<PyStrategyInfo> {
    let config = launcher.config();
    let path = PathBuf::from(strategy_path);

    // 加载主策略代码
    let strategy_code = read_to_string(&path)
        .map_err(|e| qerror!("读取python策略文件{}失败: {}", path.display(), e))?;
    let strategy_code = CString::new(strategy_code)?;

    // 加载策略配置
    let config: Value = match config.strategy_config_path.as_ref() {
        Some(path) => {
            if path.is_empty() {
                Value::new_object()
            } else {
                // 根据文件扩展名识别配置格式
                let format = ConfigFormat::from_extension(path).unwrap_or_else(|| {
                    // 默认使用TOML格式
                    qerror!("无法识别配置文件格式，默认使用TOML格式: {}", path);
                    ConfigFormat::Toml
                });

                let config_str = read_to_string(path)
                    .map_err(|e| qerror!("读取python策略配置文件{}失败: {}", path, e))?;

                parse_config_content(&config_str, format)?
            }
        }
        None => Value::new_object(),
    };

    Ok(PyStrategyInfo {
        strategy: strategy_code,
        modules: vec![],
        config,
    })
}

// 从AlgoRunner加载策略
// fn load_from_algo_runner(launcher: &mut Launcher) -> Result<PyStrategyInfo> {
//     let mut strategy = launcher
//         .strategy
//         .take()
//         .ok_or_else(|| qerror!("strategy is not initialized"))?;

//     let config = strategy.config.strategy;
//     let codes = strategy
//         .code
//         .take()
//         .ok_or_else(|| qerror!("code is not initialized"))?;

//     let mut modules = Vec::new();
//     let mut strategy_code = None;

//     // 处理所有Python文件
//     for file in codes {
//         if let Some(content) = process_file(&file, &mut modules)? {
//             strategy_code = Some(content);
//         }
//     }

//     let strategy_code = strategy_code.ok_or_else(|| qerror!("strategy.py not found"))?;

//     Ok(PyStrategyInfo {
//         strategy: CString::new(strategy_code)?,
//         modules,
//         config,
//     })
// }

// /// 处理单个Python文件或目录
// ///
// /// # Arguments
// ///
// /// * `file` - 策略文件信息
// /// * `parent_path` - 父模块路径
// /// * `modules` - 模块列表
// ///
// /// # Returns
// ///
// /// 如果是strategy.py则返回其内容，否则返回None
// fn process_file(file: &StrategyFile, modules: &mut Vec<PyModuleInfo>) -> Result<Option<String>> {
//     match file.is_file {
//         true => process_python_file(file, modules),
//         false => process_directory(file, modules),
//     }
// }

// /// 处理Python源文件
// fn process_python_file(
//     file: &StrategyFile,
//     _modules: &mut [PyModuleInfo],
// ) -> Result<Option<String>> {
//     if file.name == "strategy.py" {
//         Ok(Some(file.content.clone()))
//     } else if file.name.ends_with(".py") {
//         // 根据文件路径及名称写入文件
//         let path = &file.path;
//         let file_path = PathBuf::from(path);
//         if let Some(parent) = file_path.parent() {
//             std::fs::create_dir_all(parent)?;
//         }
//         std::fs::write(file_path, &file.content)?;

//         // 将文件路径转换为模块路径 删除第一级
//         // let path = &file.path;
//         // let path = path.split_once('/').map(|(_, rest)| rest).unwrap_or(path);
//         // let path = path.trim_end_matches(".py");
//         // let module_path = path.replace('/', ".");
//         // modules.push(PyModuleInfo {
//         //     name: CString::new(module_path)?,
//         //     file_name: CString::new(file.name.clone())?,
//         //     code: CString::new(file.content.clone())?,
//         // });
//         Ok(None)
//     } else {
//         Ok(None)
//     }
// }

// 处理目录
// fn process_directory(
//     dir: &StrategyFile,
//     modules: &mut Vec<PyModuleInfo>,
// ) -> Result<Option<String>> {
//     for file in &dir.sub_files {
//         if let Some(content) = process_file(file, modules)? {
//             return Ok(Some(content));
//         }
//     }
//     Ok(None)
// }

#[cfg(test)]
mod tests {
    use super::*;
    use sonic_rs::{JsonContainerTrait, JsonValueTrait};

    #[test]
    fn test_config_format_detection() {
        assert_eq!(
            ConfigFormat::from_extension("config.toml"),
            Some(ConfigFormat::Toml)
        );
        assert_eq!(
            ConfigFormat::from_extension("config.json"),
            Some(ConfigFormat::Json)
        );
        assert_eq!(
            ConfigFormat::from_extension("config.yaml"),
            Some(ConfigFormat::Yaml)
        );
        assert_eq!(
            ConfigFormat::from_extension("config.yml"),
            Some(ConfigFormat::Yaml)
        );
        assert_eq!(
            ConfigFormat::from_extension("config.csv"),
            Some(ConfigFormat::Csv)
        );
        assert_eq!(ConfigFormat::from_extension("config.txt"), None);
        assert_eq!(
            ConfigFormat::from_extension("CONFIG.TOML"),
            Some(ConfigFormat::Toml)
        );
        assert_eq!(
            ConfigFormat::from_extension("CONFIG.JSON"),
            Some(ConfigFormat::Json)
        );
    }

    #[test]
    fn test_parse_toml_config() {
        let content = r#"
            min_seconds_between_triggers = 10
            symbols = ["BTC_USDT", "ETH_USDT"]
            trigger_imbalance_ratio = 0.8
        "#;
        let result = parse_config_content(content, ConfigFormat::Toml);
        assert!(result.is_ok());

        let value = result.unwrap();
        assert_eq!(
            value
                .get("min_seconds_between_triggers")
                .unwrap()
                .as_i64()
                .unwrap(),
            10
        );
        assert_eq!(
            value
                .get("trigger_imbalance_ratio")
                .unwrap()
                .as_f64()
                .unwrap(),
            0.8
        );
    }

    #[test]
    fn test_parse_json_config() {
        let content = r#"{
            "min_seconds_between_triggers": 10,
            "symbols": ["BTC_USDT", "ETH_USDT"],
            "trigger_imbalance_ratio": 0.8
        }"#;
        let result = parse_config_content(content, ConfigFormat::Json);
        assert!(result.is_ok());

        let value = result.unwrap();
        assert_eq!(
            value
                .get("min_seconds_between_triggers")
                .unwrap()
                .as_i64()
                .unwrap(),
            10
        );
        assert_eq!(
            value
                .get("trigger_imbalance_ratio")
                .unwrap()
                .as_f64()
                .unwrap(),
            0.8
        );
    }

    #[test]
    fn test_parse_yaml_config() {
        let content = r#"
            min_seconds_between_triggers: 10
            symbols:
              - BTC_USDT
              - ETH_USDT
            trigger_imbalance_ratio: 0.8
        "#;
        let result = parse_config_content(content, ConfigFormat::Yaml);
        assert!(result.is_ok());

        let value = result.unwrap();
        assert_eq!(
            value
                .get("min_seconds_between_triggers")
                .unwrap()
                .as_i64()
                .unwrap(),
            10
        );
        assert_eq!(
            value
                .get("trigger_imbalance_ratio")
                .unwrap()
                .as_f64()
                .unwrap(),
            0.8
        );
    }

    #[test]
    fn test_parse_csv_config() {
        let content = r#"name,age,city
Alice,30,New York
Bob,25,Los Angeles"#;
        let result = parse_config_content(content, ConfigFormat::Csv);
        assert!(result.is_ok());

        let value = result.unwrap();
        // CSV解析后返回数组，每个元素是一个对象
        assert!(value.is_array());
        let array = value.as_array().unwrap();
        assert_eq!(array.len(), 2); // 两行数据

        // 检查第一行数据
        let first_row = array.first().unwrap().as_object().unwrap();
        assert_eq!(
            first_row
                .get(&"name".to_string())
                .unwrap()
                .as_str()
                .unwrap(),
            "Alice"
        );
        assert_eq!(
            first_row.get(&"age".to_string()).unwrap().as_str().unwrap(),
            "30"
        );
        assert_eq!(
            first_row
                .get(&"city".to_string())
                .unwrap()
                .as_str()
                .unwrap(),
            "New York"
        );

        // 检查第二行数据
        let second_row = array.get(1).unwrap().as_object().unwrap();
        assert_eq!(
            second_row
                .get(&"name".to_string())
                .unwrap()
                .as_str()
                .unwrap(),
            "Bob"
        );
        assert_eq!(
            second_row
                .get(&"age".to_string())
                .unwrap()
                .as_str()
                .unwrap(),
            "25"
        );
        assert_eq!(
            second_row
                .get(&"city".to_string())
                .unwrap()
                .as_str()
                .unwrap(),
            "Los Angeles"
        );
    }

    #[test]
    fn test_invalid_toml_config() {
        let content = "invalid toml content";
        let result = parse_config_content(content, ConfigFormat::Toml);
        assert!(result.is_err());
    }

    #[test]
    fn test_invalid_json_config() {
        let content = "invalid json content";
        let result = parse_config_content(content, ConfigFormat::Json);
        assert!(result.is_err());
    }
}
