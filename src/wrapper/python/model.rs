use std::sync::Arc;

use quant_common::qerror;
use serde::Deserialize;
use serde_json::Value;
use tokio::task::spawn;
use trader::{
    execution::{ExecutionEngine, StaticExecutionEngine},
    model::event::ex_command::ExecutionCommand,
};

#[derive(Deserialize, Debug, Default)]
pub struct PythonReturn {
    #[serde(default)]
    pub cmds: Vec<ExecutionCommand>,
    #[serde(default)]
    pub logs: Vec<String>,
}

impl PythonReturn {
    #[inline(always)]
    pub async fn handle(self, execution: &Arc<StaticExecutionEngine>) -> quant_common::Result<()> {
        for cmd in self.cmds {
            execution.execution_async(cmd).await?;
        }
        if !self.logs.is_empty() {
            spawn(async move {
                tokio::task::yield_now().await;
                for log in self.logs {
                    info!("{}", log);
                }
            });
        }
        Ok(())
    }

    /// v2版本反序列化，ExecutionCommand先反序列化为Value，然后调用ExecutionCommand的from_flatten的方法
    pub fn from_flatten(mut value: Value) -> quant_common::Result<Self> {
        match value.as_object_mut() {
            Some(value) => {
                let cmds = match value.remove("cmds") {
                    Some(Value::Array(cmds_values)) => cmds_values
                        .into_iter()
                        .map(ExecutionCommand::from_flatten)
                        .collect::<quant_common::Result<Vec<_>>>()?,
                    _ => return Err(qerror!("return cmds is not an array")),
                };

                let logs = match value.remove("logs") {
                    Some(Value::Array(logs_values)) => logs_values
                        .into_iter()
                        .map(|log| log.as_str().unwrap_or_default().to_string())
                        .collect(),
                    _ => vec![],
                };

                Ok(Self { cmds, logs })
            }
            None => Ok(Self::default()),
        }
    }
}
