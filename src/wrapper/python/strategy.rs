use async_trait::async_trait;
use pyo3::{Py, PyAny, PyErr, PyObject, Python};
use pythonize::{depythonize, pythonize};
use quant_common::base::traits::instrument::InstrumentHandler;
use quant_common::base::{AsyncCmdResult, Exchange, Instrument, OrderSource, Symbol};
use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};
use trader::model::context::Context;
use trader::strategy::Strategy;

use crate::config::model::TraderVersion;

use super::model::PythonReturn;
use algo_common::msg::AsyncCmd;
use quant_common::{Result, qerror};
use tokio::runtime::Handle;
use trader::execution::StaticExecutionEngine;
use trader::model::data_source::StrategySubscribe;
use trader::model::event::account::{AccountEvent, AccountEventInner};
use trader::model::event::ex_command::ExecutionAsyncCommandResult;
use trader::model::event::extras::ExtrasEvent;
use trader::model::event::market::{MarketEvent, MarketEventInner};
use trader::model::event::net::{NetEvent, NetEventInner};
use trader::model::event::timer::TimerEvent;
use trader::model::event::{Event, SystemCommand};

#[derive(Clone)]
pub struct PyStrategyWrapper {
    strategy: Arc<PyObject>,
    execution: Arc<StaticExecutionEngine>,
    inited: Arc<AtomicBool>,
    version: TraderVersion,
}

impl PyStrategyWrapper {
    pub fn new(
        strategy: PyObject,
        execution: Arc<StaticExecutionEngine>,
        version: TraderVersion,
    ) -> Self {
        Self {
            strategy: Arc::new(strategy),
            execution,
            inited: Arc::new(AtomicBool::new(false)),
            version,
        }
    }
}

#[async_trait]
#[allow(unused_variables)]
impl Strategy for PyStrategyWrapper {
    fn name(&self) -> &'static str {
        // 获取策略的名称，通过调用 Python 对象的 `name` 方法
        let strategy = self.strategy.clone();
        Python::with_gil(|py| {
            let name: String = match self.strategy.call_method0(py, "name") {
                Ok(py_name) => py_name
                    .extract(py)
                    .unwrap_or_else(|_| "py_strategy".to_string()),
                Err(_) => "py_strategy".to_string(),
            };
            Box::leak(name.into_boxed_str())
        })
    }

    async fn start(&self) -> Result<()> {
        let strategy = self.strategy.clone();
        let py_return = Handle::current()
            .spawn_blocking(move || {
                // 调用 Python 对象的 `start` 方法
                Python::with_gil(|py| {
                    let py_return = strategy.call_method0(py, "start").map_err(|e| {
                        e.print(py);
                        qerror!("Failed to execute start: {}", e)
                    })?;
                    // 判断是否为None
                    if py_return.is_none(py) {
                        return Ok(PythonReturn::default());
                    }
                    depythonize(py_return.bind(py)).map_err(|e| {
                        qerror!(
                    "Failed to convert Python cmds to Rust cmds error: {:?}, py cmds: {py_return}",
                    e
                )
                    })
                })
            })
            .await??;
        py_return.handle(&self.execution).await?;
        Ok(())
    }

    async fn subscribes(&self) -> Result<Vec<StrategySubscribe>> {
        let strategy = self.strategy.clone();
        // 从 Python 获取订阅的策略列表
        let res = Handle::current()
            .spawn_blocking(move || {
                Python::with_gil(|py| {
                    let binding = strategy.call_method0(py, "subscribes").map_err(|e| {
                        e.print(py);
                        qerror!("Failed to execute subscribes: {}", e)
                    })?;
                    let subscribes = binding.bind(py);
                    depythonize(subscribes)
                        .map_err(|e| {
                            qerror!(
                                "Failed to convert Python subscribes to Rust subscribes: {:?}, py subscribes: {subscribes:?}",
                                e
                            )
                        })
                })}
            ).await?;
        self.inited.store(true, Ordering::Release);
        res
    }

    async fn handle_event(&self, event: Event) -> Result<()> {
        if self.inited.load(Ordering::Acquire) {
            self.handle_event_inner(event).await
        } else {
            Ok(())
        }
    }
}

impl PyStrategyWrapper {
    #[inline(always)]
    async fn handle_event_inner(&self, event: Event) -> Result<()> {
        let strategy = self.strategy.clone();
        let version = self.version.clone();

        let inited_clone = self.inited.clone();
        let spawn_res = Handle::current()
            .spawn_blocking(move || {
                let version = version;
                Python::with_gil(|py| {
                    let res = match event {
                        Event::Market(market_event) => {
                            Self::handle_market_event_inner(strategy, py, market_event).map_err(
                                |e| {
                                    e.print(py);
                                    qerror!("Failed to handle market event: {}", e)
                                },
                            )
                        }
                        Event::Account(account_event) => {
                            Self::handle_account_event_inner(strategy, py, account_event).map_err(
                                |e| {
                                    e.print(py);
                                    qerror!("Failed to handle account event: {}", e)
                                },
                            )
                        }
                        Event::ExecutionResult(result) => {
                            Self::handle_excmd_result_inner(strategy, py, result).map_err(|e| {
                                e.print(py);
                                qerror!("Failed to handle execution result: {}", e)
                            })
                        }
                        Event::Timer(timer_event) => {
                            Self::handle_timer_event_inner(strategy, py, timer_event).map_err(|e| {
                                e.print(py);
                                qerror!("Failed to handle timer event: {}", e)
                            })
                        }
                        Event::Net(net_event) => {
                            Self::handle_net_event_inner(strategy, py, net_event).map_err(|e| {
                                e.print(py);
                                qerror!("Failed to handle net event: {}", e)
                            })
                        }
                        Event::Command(command) => {
                            Self::handle_command_inner(strategy, py, command).map_err(|e| {
                                e.print(py);
                                qerror!("Failed to handle command: {}", e)
                            })
                        }
                        Event::Extras(extras_event) => {
                            Self::handle_extras_event_inner(strategy, py, extras_event).map_err(
                                |e| {
                                    e.print(py);
                                    qerror!("Failed to handle extras event: {}", e)
                                },
                            )
                        }
                        Event::System(system_command) => Self::handle_system_command_inner(
                            strategy,
                            py,
                            system_command,
                            inited_clone,
                        )
                        .map_err(|e| {
                            e.print(py);
                            qerror!("Failed to handle system command: {}", e)
                        }),
                        Event::Execution(_) => {
                            warn!("Execution event is not supported in Python strategy");
                            Ok(Python::None(py))
                        }
                    }?;
                    // 判断是否为None
                    if res.is_none(py) {
                        return Ok(PythonReturn::default());
                    }

                    match version {
                        TraderVersion::V1 => {
                            // 使用原来的反序列化方法
                            depythonize::<PythonReturn>(res.bind(py)).map_err(|e| {
                                qerror!(
                                    "Failed to convert Python cmds to Rust cmds error: {:?}, py cmds: {res}",
                                    e
                                )
                            })
                        },
                        TraderVersion::V2 => {
                            // 先反序列化为Value，然后调用from_flatten方法
                            depythonize::<serde_json::Value>(res.bind(py))
                                .map_err(|e| qerror!("Failed to convert Python to Value: {}", e))
                                .and_then(PythonReturn::from_flatten)
                                .map_err(|e| {
                                    qerror!(
                                        "Failed to convert Python cmds to Rust cmds error: {:?}, py cmds: {res}",
                                        e
                                    )
                                })
                        }
                    }
                })
            })
            .await;
        match spawn_res {
            Ok(res) => {
                let py_return = res?;
                py_return.handle(&self.execution).await?;
            }
            Err(e) => {
                if e.is_cancelled() {
                    return Ok(());
                }
                return Err(qerror!("Failed to handle event: {}", e));
            }
        }

        Ok(())
    }

    #[inline(always)]
    fn handle_system_command_inner(
        strategy: Arc<PyObject>,
        py: Python,
        event: SystemCommand,
        inited: Arc<AtomicBool>,
    ) -> std::result::Result<Py<PyAny>, PyErr> {
        let res = match event {
            SystemCommand::Stop => {
                inited.store(false, Ordering::Release);
                strategy.call_method0(py, "on_stop")
            }
            SystemCommand::HotUpdate(value) => {
                strategy.call_method1(py, "on_config_update", (pythonize(py, &value)?,))
            }
        }?;
        Ok(res)
    }

    #[inline(always)]
    fn handle_extras_event_inner(
        strategy: Arc<PyObject>,
        py: Python,
        event: ExtrasEvent,
    ) -> std::result::Result<Py<PyAny>, PyErr> {
        let res = match event {
            ExtrasEvent::Latency(l) => strategy.call_method1(
                py,
                "on_latency",
                (pythonize(py, &l.0)?, pythonize(py, &l.1)?),
            ),
        }?;
        Ok(res)
    }

    #[inline(always)]
    fn handle_command_inner(
        strategy: Arc<PyObject>,
        py: Python,
        event: AsyncCmd,
    ) -> std::result::Result<Py<PyAny>, PyErr> {
        let res = strategy.call_method1(py, "on_cmd", (pythonize(py, &event)?,))?;
        Ok(res)
    }

    #[inline(always)]
    fn handle_net_event_inner(
        strategy: Arc<PyObject>,
        py: Python,
        event: NetEvent,
    ) -> std::result::Result<Py<PyAny>, PyErr> {
        let res = match event.event {
            NetEventInner::WsConnected => strategy.call_method1(
                py,
                "on_ws_connected",
                (
                    pythonize(py, &event.exchange)?,
                    pythonize(py, &event.account_id)?,
                ),
            ),
            NetEventInner::WsDisconnected => strategy.call_method1(
                py,
                "on_ws_disconnected",
                (
                    pythonize(py, &event.exchange)?,
                    pythonize(py, &event.account_id)?,
                ),
            ),
        }?;
        Ok(res)
    }

    #[inline(always)]
    fn handle_timer_event_inner(
        strategy: Arc<PyObject>,
        py: Python,
        event: TimerEvent,
    ) -> std::result::Result<Py<PyAny>, PyErr> {
        let res =
            strategy.call_method1(py, "on_timer_subscribe", (pythonize(py, &event.name)?,))?;
        Ok(res)
    }

    #[inline(always)]
    fn handle_excmd_result_inner(
        strategy: Arc<PyObject>,
        py: Python,
        event: ExecutionAsyncCommandResult,
    ) -> std::result::Result<Py<PyAny>, PyErr> {
        let res = match event.result {
            AsyncCmdResult::PlaceOrder(res) => strategy.call_method1(
                py,
                "on_order_submitted",
                (
                    pythonize(py, &event.account_id)?,
                    pythonize(py, &res.result)?,
                    pythonize(py, &res.order)?,
                ),
            ),
            AsyncCmdResult::BatchPlaceOrder(batch_order_rsp) => strategy.call_method1(
                py,
                "on_batch_order_submitted",
                (
                    pythonize(py, &event.account_id)?,
                    pythonize(py, &batch_order_rsp)?,
                ),
            ),
            AsyncCmdResult::AmendOrder(res) => strategy.call_method1(
                py,
                "on_order_amended",
                (
                    pythonize(py, &event.account_id)?,
                    pythonize(py, &res.result)?,
                    pythonize(py, &res.order)?,
                ),
            ),
            AsyncCmdResult::CancelOrder(res) => strategy.call_method1(
                py,
                "on_order_canceled",
                (
                    pythonize(py, &event.account_id)?,
                    pythonize(py, &res.result)?,
                    pythonize(py, &res.order_id)?,
                    pythonize(py, &res.symbol)?,
                ),
            ),
            AsyncCmdResult::BatchCancelOrder(batch_order_rsp) => strategy.call_method1(
                py,
                "on_batch_order_canceled",
                (
                    pythonize(py, &event.account_id)?,
                    pythonize(py, &batch_order_rsp)?,
                ),
            ),
            AsyncCmdResult::BatchCancelOrderByIds(batch_order_rsp) => strategy.call_method1(
                py,
                "on_batch_order_canceled_by_ids",
                (
                    pythonize(py, &event.account_id)?,
                    pythonize(py, &batch_order_rsp)?,
                ),
            ),
        }?;
        Ok(res)
    }

    #[inline(always)]
    fn handle_account_event_inner(
        strategy: Arc<PyObject>,
        py: Python,
        event: AccountEvent,
    ) -> std::result::Result<Py<PyAny>, PyErr> {
        let res = match event.event {
            AccountEventInner::Position(vec) => strategy.call_method1(
                py,
                "on_position",
                (pythonize(py, &event.account_id)?, pythonize(py, &vec)?),
            ),
            AccountEventInner::Balance(vec) => strategy.call_method1(
                py,
                "on_balance",
                (pythonize(py, &event.account_id)?, pythonize(py, &vec)?),
            ),
            AccountEventInner::Order(order) => strategy.call_method1(
                py,
                "on_order",
                (pythonize(py, &event.account_id)?, pythonize(py, &order)?),
            ),
            AccountEventInner::OrderAndFill(mut order) => {
                let order_obj = if order.source == OrderSource::UserTrade {
                    // 把可能导致策略出错的字段设置为None
                    order.pos_side = None;
                    order.price = None;
                    order.amount = None;
                    order.quote_amount = None;
                    pythonize(py, &order)?
                } else {
                    pythonize(py, &order)?
                };

                strategy.call_method1(
                    py,
                    "on_order_and_fill",
                    (pythonize(py, &event.account_id)?, order_obj),
                )
            }
            AccountEventInner::FudingFee(vec) => strategy.call_method1(
                py,
                "on_funding_fee",
                (pythonize(py, &event.account_id)?, pythonize(py, &vec)?),
            ),
            AccountEventInner::Dex(data) => strategy.call_method1(
                py,
                "on_dex_data",
                (pythonize(py, &event.account_id)?, pythonize(py, &data)?),
            ),
        }?;

        Ok(res)
    }

    #[inline(always)]
    fn handle_market_event_inner(
        strategy: Arc<PyObject>,
        py: Python,
        market_event: MarketEvent,
    ) -> std::result::Result<Py<PyAny>, PyErr> {
        let res = match market_event.event {
            MarketEventInner::BboTicker(bbo) => strategy.call_method1(
                py,
                "on_bbo",
                (pythonize(py, &market_event.exchange)?, pythonize(py, &bbo)?),
            ),
            MarketEventInner::Depth(depth) => strategy.call_method1(
                py,
                "on_depth",
                (
                    pythonize(py, &market_event.exchange)?,
                    pythonize(py, &depth)?,
                ),
            ),
            MarketEventInner::Ticker(ticker) => strategy.call_method1(
                py,
                "on_ticker",
                (
                    pythonize(py, &market_event.exchange)?,
                    pythonize(py, &ticker)?,
                ),
            ),
            MarketEventInner::Funding(vec) => strategy.call_method1(
                py,
                "on_funding",
                (pythonize(py, &market_event.exchange)?, pythonize(py, &vec)?),
            ),
            MarketEventInner::Instrument(vec) => strategy.call_method1(
                py,
                "on_instrument",
                (pythonize(py, &market_event.exchange)?, pythonize(py, &vec)?),
            ),
            MarketEventInner::Trade(trade) => strategy.call_method1(
                py,
                "on_trade",
                (
                    pythonize(py, &market_event.exchange)?,
                    pythonize(py, &trade)?,
                ),
            ),
            MarketEventInner::MarkPrice(mark_price) => strategy.call_method1(
                py,
                "on_mark_price",
                (
                    pythonize(py, &market_event.exchange)?,
                    pythonize(py, &mark_price)?,
                ),
            ),
            MarketEventInner::InstrumentUpdated(instruments) => strategy.call_method1(
                py,
                "on_instrument_updated",
                (
                    pythonize(py, &market_event.exchange)?,
                    pythonize(py, &instruments)?,
                ),
            ),
            MarketEventInner::InstrumentAdded(instruments) => strategy.call_method1(
                py,
                "on_instrument_added",
                (
                    pythonize(py, &market_event.exchange)?,
                    pythonize(py, &instruments)?,
                ),
            ),
            MarketEventInner::InstrumentRemoved(instruments) => strategy.call_method1(
                py,
                "on_instrument_removed",
                (
                    pythonize(py, &market_event.exchange)?,
                    pythonize(py, &instruments)?,
                ),
            ),
            MarketEventInner::Kline(kline) => strategy.call_method1(
                py,
                "on_kline",
                (
                    pythonize(py, &market_event.exchange)?,
                    pythonize(py, &kline)?,
                ),
            ),
        }?;

        Ok(res)
    }
}

#[async_trait]
impl InstrumentHandler for PyStrategyWrapper {
    async fn on_instrument_updated(
        &self,
        ex: Exchange,
        instruments: Vec<Instrument>,
    ) -> Result<()> {
        let event = MarketEvent {
            exchange: ex,
            event: MarketEventInner::InstrumentUpdated(instruments),
            context: Context::default(),
        };
        let event = Event::Market(event);
        self.handle_event(event).await
    }
    async fn on_instrument_removed(&self, ex: Exchange, instruments: Vec<Symbol>) -> Result<()> {
        let event = MarketEvent {
            exchange: ex,
            event: MarketEventInner::InstrumentRemoved(instruments),
            context: Context::default(),
        };
        let event = Event::Market(event);
        self.handle_event(event).await
    }
    async fn on_instrument_added(&self, ex: Exchange, instruments: Vec<Instrument>) -> Result<()> {
        let event = MarketEvent {
            exchange: ex,
            event: MarketEventInner::InstrumentAdded(instruments),
            context: Context::default(),
        };
        let event = Event::Market(event);
        self.handle_event(event).await
    }
}
