use quant_common::Result;
use serde::{Deserialize, Serialize};
use std::{
    fs::{self, File},
    path::{Path, PathBuf},
    process,
    time::{SystemTime, UNIX_EPOCH},
};
use tokio::fs as tokio_fs;
use tracing::debug;

#[derive(Debug, Serialize, Deserialize)]
struct ProcessInfo {
    pid: u32,
    created_at: u64,
}

pub struct IpcManager {
    ipc_path: PathBuf,
    lock_path: PathBuf,
    #[allow(dead_code)]
    process_info: ProcessInfo,
}

impl IpcManager {
    /// 创建新的 IPC 管理器
    pub async fn new(base_dir: &Path) -> Result<Self> {
        let ipcs_dir = base_dir.join("ipcs");
        tokio_fs::create_dir_all(&ipcs_dir).await?;

        // 生成唯一的 IPC 文件名
        let uuid = uuid::Uuid::new_v4();
        let ipc_path = ipcs_dir.join(format!("dex-{uuid}.ipc"));
        let lock_path = ipcs_dir.join(format!("dex-{uuid}.lock"));

        // 创建进程信息
        let process_info = ProcessInfo {
            pid: process::id(),
            created_at: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
        };

        // 写入锁文件
        let mut file = File::create(&lock_path)?;
        serde_json::to_writer(&mut file, &process_info)?;
        file.sync_all()?;

        Ok(Self {
            ipc_path,
            lock_path,
            process_info,
        })
    }

    /// 清理过期的 IPC 文件
    pub async fn cleanup_stale_files(base_dir: &Path) -> Result<()> {
        let ipcs_dir = base_dir.join("ipcs");
        if !ipcs_dir.exists() {
            return Ok(());
        }

        let mut entries = tokio_fs::read_dir(&ipcs_dir).await?;
        while let Some(entry) = entries.next_entry().await? {
            let path = entry.path();
            if let Some(ext) = path.extension()
                && ext == "lock"
            {
                // 检查锁文件
                if let Ok(content) = fs::read_to_string(&path)
                    && let Ok(info) = serde_json::from_str::<ProcessInfo>(&content)
                {
                    // 检查进程是否还在运行
                    if !Self::is_process_running(info.pid) {
                        debug!("清理过期的IPC文件: {:?}", path);
                        // 删除对应的 .ipc 和 .lock 文件
                        let ipc_path = path.with_extension("ipc");
                        let _ = tokio_fs::remove_file(&ipc_path).await;
                        let _ = tokio_fs::remove_file(&path).await;
                    }
                }
            }
        }
        Ok(())
    }

    /// 获取 IPC 地址
    pub fn get_ipc_addr(&self) -> String {
        format!("ipc://{}", self.ipc_path.display())
    }

    /// 获取 IPC 文件路径
    pub fn get_ipc_path(&self) -> PathBuf {
        self.ipc_path.clone()
    }

    /// 检查进程是否在运行
    #[cfg(unix)]
    fn is_process_running(pid: u32) -> bool {
        use nix::sys::signal;

        signal::kill(nix::unistd::Pid::from_raw(pid as i32), None).is_ok()
    }

    #[cfg(windows)]
    fn is_process_running(pid: u32) -> bool {
        use windows::Win32::Foundation::{CloseHandle, HANDLE};
        use windows::Win32::System::Threading::{OpenProcess, PROCESS_QUERY_INFORMATION};

        unsafe {
            let handle = OpenProcess(PROCESS_QUERY_INFORMATION, false, pid);
            if handle.is_invalid() {
                return false;
            }
            CloseHandle(handle);
            true
        }
    }
}

impl Drop for IpcManager {
    fn drop(&mut self) {
        // 清理 IPC 和锁文件
        let _ = fs::remove_file(&self.ipc_path);
        let _ = fs::remove_file(&self.lock_path);
    }
}
