use async_trait::async_trait;
use quant_common::Result;
use serde::{Deserialize, Serialize};
use serde_json::Value;

#[async_trait]
pub trait DexDataHandler: Send + Sync + 'static {
    async fn handle_data(&self, client_id: usize, typ: String, data: Value) -> Result<()>;
}

#[derive(Serialize, Deserialize, Debug)]
pub(crate) struct ZmqPayload {
    #[serde(rename = "type")]
    pub msg_type: String,
    pub data: Value,
}
