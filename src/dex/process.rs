use algo_common::msg::DexExParams;
use quant_common::Result;
use std::path::PathBuf;
use tokio::process::{Child, Command};

pub(crate) async fn start_process(
    exchange: &DexExParams,
    bin_path: &PathBuf,
    log_file: std::fs::File,
    ipc_path: &str,
    index: usize,
) -> Result<Child> {
    let mut cmd = Command::new(bin_path);
    cmd.stdin(std::process::Stdio::null())
        .stdout(log_file.try_clone()?)
        .stderr(log_file)
        .kill_on_drop(true)
        .env("IPC", ipc_path)
        .env("ID", index.to_string())
        .env("RUST_BACKTRACE", "1");

    for (key, value) in &exchange.params {
        cmd.env(key, value);
    }

    Ok(cmd.spawn()?)
}
