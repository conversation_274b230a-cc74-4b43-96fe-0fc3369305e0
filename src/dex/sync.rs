use std::sync::atomic::{AtomicBool, Ordering};

use async_trait::async_trait;
use quant_common::Result;
use serde_json::Value;
use tokio::sync::{
    Mutex,
    mpsc::{Receiver, Sender, channel},
};

use crate::{
    model::{
        context::Context,
        event::{
            Event,
            account::{AccountEvent, AccountEventInner},
        },
    },
    strategy::Strategy,
};

use super::DexDataHandler;

pub struct DexSync {
    pub sync: AtomicBool,
    pub tx: Sender<(String, Value)>,
    pub rx: Mutex<Receiver<(String, Value)>>,
    pub strategy: Box<dyn Strategy>,
}

#[async_trait]
impl DexDataHandler for DexSync {
    async fn handle_data(&self, client_id: usize, typ: String, data: Value) -> Result<()> {
        if !self.sync.load(Ordering::Acquire) {
            let event = Event::Account(AccountEvent {
                account_id: client_id,
                context: Context::default(),
                event: AccountEventInner::Dex((typ, data)),
            });
            self.strategy.handle_event(event).await?;
        } else {
            self.tx.send((typ, data)).await?;
        }
        Ok(())
    }
}

impl DexSync {
    pub fn new(strategy: Box<dyn Strategy>) -> Self {
        let (tx, rx) = channel(1024);
        Self {
            sync: AtomicBool::new(false),
            tx,
            rx: Mutex::new(rx),
            strategy,
        }
    }

    pub fn set_sync(&self, sync: bool) {
        self.sync.store(sync, Ordering::Release);
    }
}
