use chrono::Local;
use quant_common::Result;
use std::path::Path;
use tokio::fs;
use tracing::info;

pub(crate) async fn cleanup_old_logs(
    base_dir: &Path,
    dex_name: &str,
    version: &str,
    max_log_size: u64,
    max_log_files: usize,
) -> Result<()> {
    let log_dir = base_dir.join("logs").join(dex_name);

    // 确保目录存在
    fs::create_dir_all(&log_dir).await?;

    // 使用 tokio::fs::read_dir 读取目录内容
    let mut entries = match fs::read_dir(&log_dir).await {
        Ok(entries) => entries,
        Err(_) => {
            return Ok(());
        }
    };

    let mut total_size = 0u64;
    let mut log_files = Vec::new();

    // 读取所有日志文件
    while let Ok(Some(entry)) = entries.next_entry().await {
        let path = entry.path();
        if let Some(path_str) = path.to_str()
            && path.is_file()
            && path_str.contains(&format!("{dex_name}-{version}_"))
            && let Ok(metadata) = entry.metadata().await
            && let Ok(modified) = metadata.modified()
        {
            total_size += metadata.len();
            log_files.push((path, modified, metadata.len()));
        }
    }

    // 如果没有找到任何日志文件，直接返回
    if log_files.is_empty() {
        return Ok(());
    }

    // 按修改时间排序
    log_files.sort_by_key(|(_, time, _)| *time);

    // 如果总大小超过限制，从最旧的文件开始删除
    while total_size > max_log_size && log_files.len() > 1 {
        if let Some((path, _, size)) = log_files.first() {
            info!("总日志大小超过限制，删除旧文件: {:?}", path);
            if let Err(e) = fs::remove_file(path).await {
                warn!("删除文件失败: {}", e);
            } else {
                total_size -= size;
                log_files.remove(0);
            }
        } else {
            break;
        }
    }

    // 如果文件数量超过限制，删除最旧的文件
    while log_files.len() > max_log_files {
        if let Some((path, _, _)) = log_files.first() {
            info!("日志文件数量超过限制，删除最旧的文件: {:?}", path);
            if let Err(e) = fs::remove_file(path).await {
                warn!("删除文件失败: {}", e);
            }
            log_files.remove(0);
        }
    }

    Ok(())
}

pub(crate) async fn setup_log_file(
    dex_name: &str,
    version: &str,
    log_dir: &Path,
) -> Result<std::fs::File> {
    let log_dir = log_dir.join(dex_name);
    fs::create_dir_all(&log_dir).await?;

    let now = Local::now();
    let log_file = format!(
        "{}-{}_{}.log",
        dex_name,
        version,
        now.format("%Y%m%d_%H%M%S")
    );
    let log_path = log_dir.join(log_file);

    Ok(std::fs::File::create(log_path)?)
}

#[cfg(test)]
mod tests {
    use std::path::PathBuf;

    use super::*;

    #[tokio::test]
    async fn test_setup_log_file() {
        let log_dir = PathBuf::from("dexs");
        let _ = setup_log_file("test", "1.0.0", &log_dir).await.unwrap();
    }

    #[tokio::test]
    async fn test_cleanup_old_logs() {
        let base_dir = PathBuf::from("dexs");
        cleanup_old_logs(&base_dir, "test", "v1.0.0", 100, 10)
            .await
            .unwrap();
    }
}
