use super::{
    controller::Controller,
    ipc::Ipc<PERSON>anager,
    log::{cleanup_old_logs, setup_log_file},
    message::ZmqPayload,
    process::start_process,
    sync::DexSync,
};
use crate::{
    config::DexConfig,
    dex::DexDataHandler,
    model::{account::AccountId, dex::Log},
};
use algo_common::msg::DexExParams;
use bytes::Bytes;
use dashmap::DashMap;
use downloader::Downloader;
use once_cell::sync::OnceCell;
use quant_common::{Result, qerror};
use serde_json::Value;
use std::{
    collections::VecDeque,
    path::PathBuf,
    sync::Arc,
    time::{Duration, Instant},
};
use tokio::{
    sync::{RwLock, mpsc},
    time::{self},
};
use tracing::{debug, error, info, trace, warn};
use zeromq::{RouterSocket, Socket, SocketOptions, SocketRecv, SocketSend, ZmqMessage};

pub struct DexManager {
    /// 消息发送通道
    sender: mpsc::Sender<(AccountId, Value)>,
    /// 消息接收通道
    receiver: RwLock<mpsc::Receiver<(AccountId, Value)>>,
    /// ZeroMQ根节点Socket
    router: RwLock<RouterSocket>,
    /// 下载器
    downloader: Arc<Downloader>,
    /// DEX配置
    config: DexConfig,
    /// DEX 基础目录
    base_dir: PathBuf,
    /// 控制器
    controller: Controller,
    /// 基础索引值
    base_index: usize,
    /// DEX进程映射表
    processes: DashMap<usize, (String, tokio::process::Child)>,
    /// 心跳检测
    last_heartbeats: DashMap<usize, Instant>,
    // 订阅信息
    subscribes: DashMap<usize, Value>,
    /// 数据处理器
    data_handler: Arc<OnceCell<DexSync>>,
    /// ZeroMQ地址
    zero_mq_addr: PathBuf,
    /// IPC管理器
    #[allow(dead_code)]
    ipc_manager: Option<IpcManager>,
    /// 进程监控控制器
    monitor_controller: Controller,
}

impl DexManager {
    pub async fn new(
        config: DexConfig,
        downloader: Arc<Downloader>,
        base_index: usize,
        data_handler: Arc<OnceCell<DexSync>>,
    ) -> Result<Self> {
        let (sender, receiver) = mpsc::channel(100);
        let mut router = RouterSocket::with_options(SocketOptions::default());

        // 设置基础目录
        let base_dir = std::env::current_dir()?.join("dexs");

        // 清理过期文件
        IpcManager::cleanup_stale_files(&base_dir).await?;

        // 创建IPC管理器
        let (ipc_manager, zero_mq_addr) = if !config.exchanges.is_empty() {
            let manager = IpcManager::new(&base_dir).await?;

            // 绑定IPC地址
            router
                .bind(&manager.get_ipc_addr())
                .await
                .map_err(|e| qerror!("绑定IPC地址失败: {}", e))?;

            let get_ipc_path = manager.get_ipc_path();
            (Some(manager), get_ipc_path)
        } else {
            (None, PathBuf::new())
        };

        Ok(Self {
            sender,
            receiver: RwLock::new(receiver),
            router: RwLock::new(router),
            downloader,
            config,
            base_dir,
            controller: Controller::new(),
            base_index,
            processes: DashMap::new(),
            last_heartbeats: DashMap::new(),
            data_handler,
            zero_mq_addr,
            ipc_manager,
            subscribes: DashMap::new(),
            monitor_controller: Controller::new(),
        })
    }

    /// 启动所有DEX节点并等待首次消息
    pub async fn start_and_wait_ready(self: Arc<Self>) -> Result<()> {
        if self.config.exchanges.is_empty() {
            return Ok(());
        }

        debug!("启动所有DEX节点...");
        let mut router = self.router.write().await;
        let mut receiver = self.receiver.write().await;
        let mut waiting_heartbeats = Vec::new();

        // 启动所有DEX节点
        for (i, exchange) in self.config.exchanges.iter().enumerate() {
            let index = i + self.base_index;
            if let Err(e) = self.start_dex(exchange, index).await {
                return Err(qerror!(
                    "启动DEX节点失败 {} (index: {}): {}",
                    exchange.name,
                    index,
                    e
                ));
            }
            waiting_heartbeats.push((index, exchange.name.clone()));
        }

        // 等待所有节点的首次消息
        let mut heartbeat_timeout =
            time::interval(Duration::from_secs(self.config.heartbeat_timeout));
        heartbeat_timeout.tick().await;

        let mut wait_check = time::interval(Duration::from_millis(100));
        wait_check.tick().await;

        'wait_loop: loop {
            if waiting_heartbeats.is_empty() {
                break;
            }

            tokio::select! {
                _ = heartbeat_timeout.tick() => {
                    debug!("等待dex首次消息超时");
                    break 'wait_loop;
                }

                Some((client_id, payload)) = receiver.recv() => {
                    self.handle_send_message(&mut router, client_id, payload).await?;
                }

                msg = router.recv() => {
                    if let Ok(msg) = msg
                        && let Err(e) = self.handle_first_message(msg, &mut waiting_heartbeats).await {
                            error!("处理首次消息失败: {}", e);
                        }
                }

                _ = wait_check.tick() => {
                    if waiting_heartbeats.is_empty() {
                        break;
                    }
                }
            }
        }

        // 检查是否有节点未收到消息
        if !waiting_heartbeats.is_empty() {
            let failed_nodes: Vec<String> = waiting_heartbeats
                .iter()
                .map(|(_, name)| name.clone())
                .collect();
            return Err(qerror!(
                "以下DEX节点在{}秒内未收到消息: {}",
                self.config.heartbeat_timeout,
                failed_nodes.join(", ")
            ));
        }

        info!("所有DEX节点启动成功并已收到消息");

        // 启动进程监控
        self.clone().start_process_monitor().await;

        Ok(())
    }

    /// 启动消息处理循环
    pub async fn start_message_loop(self: Arc<Self>) -> Result<()> {
        if self.config.exchanges.is_empty() {
            return Ok(());
        }

        debug!("启动消息循环和心跳检测");
        let mut stop_rx = self.controller.subscribe();
        let mut heartbeat_interval = time::interval(Duration::from_secs(5));
        let mut cleanup_interval = time::interval(Duration::from_secs(3600));

        let mut router = self.router.write().await;
        let mut receiver = self.receiver.write().await;

        while self.data_handler.get().is_none() {
            tokio::time::sleep(Duration::from_millis(200)).await;
        }
        let data_handler = self.data_handler.get().unwrap();

        loop {
            tokio::select! {
                Ok(_) = stop_rx.recv() => {
                    info!("DexManager 收到停止信号");
                    break;
                }

                _ = heartbeat_interval.tick() => {
                    self.check_heartbeats().await;
                }

                _ = cleanup_interval.tick() => {
                    for exchange in &self.config.exchanges {
                        if let Err(e) = cleanup_old_logs(
                            &self.base_dir,
                            &exchange.name,
                            &exchange.version,
                            self.config.max_log_size,
                            self.config.max_log_files,
                        )
                        .await {
                            error!("清理日志失败: {}", e);
                        }
                    }
                }

                Some((client_id, payload)) = receiver.recv() => {
                    if let Err(e) = self.handle_send_message(&mut router, client_id, payload).await {
                        error!("发送消息失败: {}", e);
                    }
                }

                msg = router.recv() => {
                    if let Ok(msg) = msg
                        && let Err(e) = self.handle_message(msg, data_handler).await {
                            error!("处理消息失败: {}", e);
                        }
                }
            }
        }

        info!("DexManager 消息循环已停止");
        Ok(())
    }

    /// 检查心跳状态
    async fn check_heartbeats(&self) {
        let now = Instant::now();
        let heartbeat_timeout = Duration::from_secs(self.config.heartbeat_timeout);
        let mut dead_indices = Vec::new();

        for entry in self.processes.iter() {
            let index = *entry.key();
            let (name, _) = entry.value();

            if let Some(last_heartbeat) = self.last_heartbeats.get(&index)
                && now.duration_since(*last_heartbeat) > heartbeat_timeout
            {
                warn!("节点 {} (index: {}) 心跳超时", name, index);
                dead_indices.push(index);
            }
        }

        for index in dead_indices {
            warn!("正在重启节点 index: {}", index);
            if let Err(e) = self.restart_dex_by_index(index).await {
                error!("重启节点 index {} 失败: {}", index, e);
            }
        }
    }

    /// 处理接收到的消息
    async fn handle_message(&self, msg: ZmqMessage, data_handler: &DexSync) -> Result<()> {
        trace!("dex收到消息: {:?}", msg);
        let frames: Vec<_> = msg.into_vec();
        if frames.len() < 2 {
            return Err(qerror!("无效的消息格式, frames: {:?}", frames));
        }

        let id = &frames[0];
        let payload = &frames[1];
        let payload_str = String::from_utf8_lossy(payload);

        let zmq_payload: ZmqPayload = serde_json::from_str(&payload_str)
            .map_err(|e| qerror!("解析消息失败: {}, payload: {}", e, payload_str))?;

        let id: usize = String::from_utf8_lossy(id).parse::<i64>()?.unsigned_abs() as usize;

        match zmq_payload.msg_type.as_str() {
            "ping" => {
                if self.processes.contains_key(&id) {
                    // 更新心跳时间
                    self.last_heartbeats.insert(id, Instant::now());
                    // 回复 pong
                    self.send_to_client(id, Value::Null).await?;
                } else {
                    warn!("未找到节点 {}", id);
                }
            }
            "log" => {
                let index = id - self.base_index;
                let name = self.config.exchanges[index].name.clone();
                let log: Log = serde_json::from_value(zmq_payload.data)?;
                match log.level.as_str() {
                    "ERROR" => error!("[{}] {}", name, log.msg),
                    "WARN" => warn!("[{}] {}", name, log.msg),
                    "INFO" => info!("[{}] {}", name, log.msg),
                    "DEBUG" => debug!("[{}] {}", name, log.msg),
                    "TRACE" => trace!("[{}] {}", name, log.msg),
                    _ => warn!("[{}] Unknown level {}: {}", name, log.level, log.msg),
                }
            }
            _ => {
                data_handler
                    .handle_data(id, zmq_payload.msg_type, zmq_payload.data)
                    .await?;
            }
        }

        Ok(())
    }

    /// 向客户端发送消息
    pub async fn send_to_client(&self, client_id: AccountId, payload: Value) -> Result<()> {
        self.sender
            .send((client_id, payload))
            .await
            .map_err(|e| qerror!("发送消息失败: {}", e))?;
        Ok(())
    }

    /// 订阅
    pub async fn subscribe(&self, client_id: AccountId, payload: Value) -> Result<()> {
        self.subscribes.insert(client_id, payload.clone());
        self.send_to_client(client_id, payload).await?;
        Ok(())
    }

    /// 广播消息给所有客户端
    pub async fn broadcast(&self, payload: Value) -> Result<()> {
        for entry in self.processes.iter() {
            let index = entry.key();
            if let Err(e) = self.send_to_client(*index, payload.clone()).await {
                error!("向客户端 index {} 广播消息失败: {}", index, e);
            }
        }
        Ok(())
    }

    /// 重启指定索引的DEX节点
    pub async fn restart_dex_by_index(&self, index: usize) -> Result<()> {
        if let Some(entry) = self.processes.remove(&index) {
            let (name, mut child) = entry.1;
            debug!("停止进程 {}", name);

            // 检查进程是否已经停止
            match child.try_wait()? {
                Some(status) => {
                    debug!("进程 {} 已经停止，状态码: {:?}", name, status.code());
                }
                None => {
                    debug!("进程 {} 仍在运行，发送 kill 信号", name);
                    let _ = child.kill().await;
                }
            }

            let exchange = self
                .config
                .exchanges
                .iter()
                .find(|e| e.name == name)
                .ok_or_else(|| qerror!("未找到交易所配置: {}", name))?;

            // 启动新进程
            self.start_dex(exchange, index).await?;

            // 等待首次心跳消息
            let mut heartbeat_timeout =
                time::interval(Duration::from_secs(self.config.heartbeat_timeout));
            heartbeat_timeout.tick().await;

            let mut received_heartbeat = false;
            while !received_heartbeat {
                tokio::select! {
                    _ = heartbeat_timeout.tick() => {
                        return Err(qerror!("等待进程 {} (index: {}) 首次心跳消息超时", name, index));
                    }
                    _ = tokio::time::sleep(Duration::from_millis(100)) => {
                        if let Some(last_heartbeat) = self.last_heartbeats.get(&index)
                            && last_heartbeat.elapsed() < Duration::from_secs(2) {
                                received_heartbeat = true;
                            }
                    }
                }
            }

            // 收到心跳后再发送订阅消息
            if let Some(subscribe) = self.subscribes.get(&index) {
                debug!("重启DEX节点 index: {} 成功，发送订阅消息", index);
                self.send_to_client(index, subscribe.value().clone())
                    .await?;
            }
        }
        Ok(())
    }

    /// 检查指定索引的DEX节点状态
    pub async fn check_status_by_index(&self, index: usize) -> Result<bool> {
        if let Some(mut entry) = self.processes.get_mut(&index) {
            let (_, (name, child)) = entry.pair_mut();
            match child.try_wait()? {
                Some(status) => {
                    warn!(
                        "节点 {} (index: {}) 已退出, 状态码: {:?}",
                        name,
                        index,
                        status.code()
                    );
                    Ok(false)
                }
                None => Ok(true),
            }
        } else {
            warn!("未找到 index {} 对应的进程", index);
            Ok(false)
        }
    }

    /// 关闭所有DEX节点
    pub async fn shutdown(&self) -> Result<()> {
        info!("正在关闭所有DEX节点");
        self.controller.stop();
        self.monitor_controller.stop(); // 停止进程监控

        for mut entry in self.processes.iter_mut() {
            let (_, (name, child)) = entry.pair_mut();
            match child.try_wait()? {
                Some(status) => {
                    debug!("进程 {} 已经停止，状态码: {:?}", name, status.code());
                }
                None => {
                    debug!("进程 {} 仍在运行，发送停止信号", name);
                    if let Err(e) = child.start_kill() {
                        error!("停止进程 {} 失败: {}", name, e);
                    }
                }
            }
        }

        Ok(())
    }

    /// 启动单个DEX节点
    async fn start_dex(&self, exchange: &DexExParams, index: usize) -> Result<()> {
        // 使用 base_dir 构建路径
        let bin_dir = self
            .base_dir
            .join("bins")
            .join(&exchange.name)
            .join(&exchange.version);
        tokio::fs::create_dir_all(&bin_dir).await?;

        let file_name = exchange.name.to_string();
        let bin_path = bin_dir.join(&file_name);

        // 检查并清理旧日志
        cleanup_old_logs(
            &self.base_dir,
            &exchange.name,
            &exchange.version,
            self.config.max_log_size,
            self.config.max_log_files,
        )
        .await?;

        // 只在可执行文件不存在时下载
        if !bin_path.exists() {
            let os_type = if cfg!(target_os = "windows") {
                "windows"
            } else if cfg!(target_os = "macos") {
                "macos"
            } else {
                "linux"
            };

            let arch = if cfg!(target_arch = "x86_64") {
                "amd64"
            } else if cfg!(target_arch = "aarch64") {
                "arm64"
            } else {
                return Err(qerror!("不支持的系统架构"));
            };

            let _platform = format!("{os_type}-{arch}");
            let download_path = format!("dex/{}/{}", exchange.name, exchange.version,);

            debug!("正在下载DEX可执行文件: {}", download_path);
            self.downloader.download(&download_path, &bin_path).await?;

            #[cfg(unix)]
            {
                use std::os::unix::fs::PermissionsExt;
                let mut perms = tokio::fs::metadata(&bin_path).await?.permissions();
                perms.set_mode(0o755);
                tokio::fs::set_permissions(&bin_path, perms).await?;
            }
        }

        debug!("正在启动DEX节点: {} (index: {})", exchange.name, index);

        // 日志目录也使用 base_dir
        let log_dir = self.base_dir.join("logs");
        let log_file = setup_log_file(&exchange.name, &exchange.version, &log_dir).await?;

        // 启动进程
        let child = start_process(
            exchange,
            &bin_path,
            log_file,
            self.zero_mq_addr.to_str().unwrap(),
            index,
        )
        .await?;

        self.processes.insert(index, (exchange.name.clone(), child));
        self.last_heartbeats.insert(index, Instant::now());

        debug!("DEX节点启动成功: {} (index: {})", exchange.name, index);
        Ok(())
    }

    // 辅助方法：处理首次消息
    async fn handle_first_message(
        &self,
        msg: ZmqMessage,
        waiting_heartbeats: &mut Vec<(usize, String)>,
    ) -> Result<()> {
        let frames: Vec<_> = msg.into_vec();
        if !frames.is_empty()
            && let Ok(id) = String::from_utf8_lossy(&frames[0]).parse::<i64>()
        {
            let id = id.unsigned_abs() as usize;
            waiting_heartbeats.retain(|(index, name)| {
                if *index == id {
                    debug!("DEX节点 {} (index: {}) 收到首次消息", name, index);
                    self.last_heartbeats.insert(id, Instant::now());
                    false
                } else {
                    true
                }
            });
        }
        Ok(())
    }

    // 辅助方法：处理发送消息
    async fn handle_send_message(
        &self,
        router: &mut RouterSocket,
        client_id: AccountId,
        payload: Value,
    ) -> Result<()> {
        trace!(
            "发送dex消息, client_id: {}, payload: {:?}",
            client_id, payload
        );
        let client_id_bytes = Bytes::from(client_id.to_string());
        let payload = if payload.is_null() {
            Bytes::from(vec![])
        } else {
            Bytes::from(serde_json::to_vec(&payload)?)
        };

        let mut frames = VecDeque::with_capacity(3);
        frames.push_back(client_id_bytes);
        frames.push_back(payload);

        if let Ok(msg) = ZmqMessage::try_from(frames)
            && let Err(e) = router.send(msg).await
        {
            error!("发送消息失败: {:?}", e);
        }
        Ok(())
    }

    /// 启动进程监控
    async fn start_process_monitor(self: Arc<Self>) {
        let mut stop_rx = self.monitor_controller.subscribe();

        tokio::spawn(async move {
            let mut check_interval = time::interval(Duration::from_secs(1));

            loop {
                tokio::select! {
                    Ok(_) = stop_rx.recv() => {
                        debug!("进程监控任务收到停止信号");
                        break;
                    }

                    _ = check_interval.tick() => {
                        // 收集需要重启的进程
                        let mut to_restart = Vec::new();

                        // 检查进程状态，但不立即重启
                        {
                            let processes: Vec<_> = self.processes.iter().map(|e| (*e.key(), e.value().0.clone())).collect();
                            for (index, name) in processes {
                                if let Some(mut entry) = self.processes.get_mut(&index) {
                                    let (_, child) = entry.pair_mut();
                                    match child.1.try_wait() {
                                        Ok(Some(status)) => {
                                            warn!("检测到进程异常退出 {} (index: {}), 状态码: {:?}", name, index, status.code());
                                            to_restart.push((index, name));
                                        }
                                        Err(e) => {
                                            error!("检查进程状态失败 {} (index: {}): {}", name, index, e);
                                        }
                                        _ => {} // 进程正常运行
                                    }
                                }
                            }
                        }

                        // 重启需要重启的进程
                        for (index, name) in to_restart {
                            if let Err(e) = self.restart_dex_by_index(index).await {
                                error!("重启进程失败 {} (index: {}): {}", name, index, e);
                            } else {
                                info!("成功重启进程 {} (index: {})", name, index);
                            }
                        }
                    }
                }
            }
        });
    }
}
