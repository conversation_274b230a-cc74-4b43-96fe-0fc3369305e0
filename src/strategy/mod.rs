use crate::model::{
    account::AccountId,
    context::{Context, LatencyRecord, Milestone},
    data_source::StrategySubscribe,
    event::{
        Event, SystemCommand,
        account::{AccountEvent, AccountEventInner},
        ex_command::ExecutionAsyncCommandResult,
        extras::ExtrasEvent,
        market::{MarketEvent, MarketEventInner},
        net::NetEventInner,
    },
};
use algo_common::msg::AsyncCmd;
use async_trait::async_trait;
use quant_common::{
    Result,
    base::{FundingFee, Kline, OrderId, Symbol, traits::instrument::InstrumentHandler},
};
use quant_common::{
    base::{
        AsyncCmdResult, Balance, BatchOrderRsp, BboTicker, Depth, Exchange, Funding, Instrument,
        MarkPrice, Order, Position, Ticker, Trade,
    },
    qerror,
};
use serde_json::Value;

#[allow(unused_variables)]
#[async_trait]
pub trait Strategy: InstrumentHandler + Send + Sync + 'static {
    fn name(&self) -> &'static str {
        "default_strategy"
    }

    async fn start(&self) -> Result<()> {
        Ok(())
    }

    async fn subscribes(&self) -> Result<Vec<StrategySubscribe>> {
        Ok(vec![])
    }

    async fn handle_event(&self, event: Event) -> Result<()> {
        match event {
            Event::Market(event) => self.handle_market_event(event).await?,
            Event::Account(event) => self.handle_account_event(event).await?,
            Event::ExecutionResult(event) => self.handle_execution_result(event).await?,
            Event::Command(event) => self.on_command(event).await?,
            Event::Net(event) => match event.event {
                NetEventInner::WsConnected => {
                    self.on_ws_connected(event.exchange, event.account_id)
                        .await?
                }
                NetEventInner::WsDisconnected => {
                    self.on_ws_disconnected(event.exchange, event.account_id)
                        .await?
                }
            },
            Event::Extras(event) => match event {
                ExtrasEvent::Latency((latency, account_id)) => {
                    self.on_latency(latency, account_id).await?
                }
            },
            Event::System(event) => match event {
                SystemCommand::HotUpdate(config) => self.on_config_update(config).await?,
                SystemCommand::Stop => self.on_stop().await?,
            },
            Event::Timer(timer_event) => self.handle_timer_event(timer_event.name).await?,
            Event::Execution(execution_command) => {
                return Err(qerror!("Execution command not supported"));
            }
        }
        Ok(())
    }

    async fn handle_account_event(&self, event: AccountEvent) -> Result<()> {
        match event.event {
            AccountEventInner::Position(positions) => {
                self.on_position(event.account_id, positions).await?
            }
            AccountEventInner::Balance(balances) => {
                self.on_balance(event.account_id, balances).await?
            }
            AccountEventInner::Order(order) => {
                self.on_order(event.account_id, event.context, order)
                    .await?
            }
            AccountEventInner::FudingFee(fuding_fee) => {
                self.on_fuding_fee(event.account_id, fuding_fee).await?
            }
            AccountEventInner::OrderAndFill(order) => {
                self.on_order_and_fill(event.account_id, event.context, order)
                    .await?
            }
            AccountEventInner::Dex((typ, data)) => self.on_dex(event.account_id, typ, data).await?,
        }
        Ok(())
    }

    async fn handle_market_event(&self, mut event: MarketEvent) -> Result<()> {
        if let Some(latency) = event.context.latency.as_mut() {
            latency.record(Milestone::StrategyBegin);
        }
        match event.event {
            MarketEventInner::BboTicker(ticker) => {
                self.on_bbo(event.exchange, event.context, ticker).await?
            }
            MarketEventInner::Depth(depth) => {
                self.on_depth(event.exchange, event.context, depth).await?
            }
            MarketEventInner::Ticker(ticker) => {
                self.on_ticker(event.exchange, event.context, ticker)
                    .await?
            }
            MarketEventInner::Funding(fundings) => {
                self.on_fundings(event.exchange, fundings).await?
            }
            MarketEventInner::Instrument(instruments) => {
                self.on_instrument(event.exchange, instruments).await?
            }
            MarketEventInner::Trade(trade) => {
                self.on_trade(event.exchange, event.context, trade).await?
            }
            MarketEventInner::MarkPrice(mark_price) => {
                self.on_mark_price(event.exchange, mark_price).await?
            }
            MarketEventInner::InstrumentUpdated(instruments) => {
                self.on_instrument_updated(event.exchange, instruments)
                    .await?
            }
            MarketEventInner::InstrumentRemoved(instruments) => {
                self.on_instrument_removed(event.exchange, instruments)
                    .await?
            }
            MarketEventInner::InstrumentAdded(instruments) => {
                self.on_instrument_added(event.exchange, instruments)
                    .await?
            }
            MarketEventInner::Kline(kline) => self.on_kline(event.exchange, kline).await?,
        }
        Ok(())
    }

    async fn handle_execution_result(&self, event: ExecutionAsyncCommandResult) -> Result<()> {
        match event.result {
            AsyncCmdResult::PlaceOrder(res) => {
                self.on_order_submitted(event.account_id, event.context, res.result, res.order)
                    .await?
            }
            AsyncCmdResult::AmendOrder(res) => {
                self.on_order_amended(event.account_id, event.context, res.result, res.order)
                    .await?
            }
            AsyncCmdResult::CancelOrder(res) => {
                self.on_order_canceled(
                    event.account_id,
                    event.context,
                    res.result,
                    res.order_id,
                    res.symbol,
                )
                .await?
            }
            AsyncCmdResult::BatchPlaceOrder(res) => {
                self.on_batch_order_submitted(event.account_id, event.context, res)
                    .await?
            }
            AsyncCmdResult::BatchCancelOrder(res) => {
                self.on_batch_order_canceled(event.account_id, event.context, res)
                    .await?
            }
            AsyncCmdResult::BatchCancelOrderByIds(res) => {
                self.on_batch_order_canceled(event.account_id, event.context, res)
                    .await?
            }
        }

        // if let Some(l) = latency {
        //     if let Ok(latency) = l.report() {
        //         if let Err(e) = self
        //             .handle_event(Event::Extras(ExtrasEvent::Latency((
        //                 latency,
        //                 event.account_id,
        //             ))))
        //             .await
        //         {
        //             error!("Failed to handle latency event: {:?}", e);
        //         }
        //     }
        // }
        Ok(())
    }

    async fn on_ws_connected(&self, exchange: Exchange, account_id: AccountId) -> Result<()> {
        info!("{:?} ws connected", exchange);
        Ok(())
    }

    async fn on_ws_disconnected(&self, exchange: Exchange, account_id: AccountId) -> Result<()> {
        info!("{:?} ws disconnected", exchange);
        Ok(())
    }

    async fn on_latency(&self, latency: LatencyRecord, account_id: AccountId) -> Result<()> {
        Ok(())
    }

    async fn handle_timer_event(&self, timer_name: String) -> Result<()> {
        Ok(())
    }

    async fn on_dex(&self, account_id: AccountId, typ: String, data: Value) -> Result<()> {
        Ok(())
    }

    /// 订单更新回调
    ///
    /// # 参数
    /// -exchange: 交易所类型
    /// -account_id: 账户ID
    /// -context: 上下文
    /// -order: 订单
    async fn on_order(&self, account_id: AccountId, context: Context, order: Order) -> Result<()> {
        Ok(())
    }

    /// 订单和成交回调
    ///
    /// # 参数
    /// -account_id: 账户ID
    /// -context: 上下文
    /// -order: 订单
    async fn on_order_and_fill(
        &self,
        account_id: AccountId,
        context: Context,
        order: Order,
    ) -> Result<()> {
        Ok(())
    }

    /// 资金费回调
    ///
    /// # 参数
    /// -account_id: 账户ID
    /// -fuding_fee: 资金费
    async fn on_fuding_fee(&self, account_id: AccountId, fuding_fee: FundingFee) -> Result<()> {
        Ok(())
    }

    /// 订单提交回报回调
    ///
    /// # 参数
    /// -exchange: 交易所类型
    /// -account_id: 账户ID
    /// -context: 上下文
    /// -id: 订单ID
    async fn on_order_submitted(
        &self,
        account_id: AccountId,
        context: Context,
        id: Result<String>,
        order: Order,
    ) -> Result<()> {
        Ok(())
    }

    /// 批量下单回报回调
    ///
    /// # 参数
    /// -exchange: 交易所类型
    /// -account_id: 账户ID
    /// -ids: 订单ID列表
    async fn on_batch_order_submitted(
        &self,
        account_id: AccountId,
        context: Context,
        ids: Result<BatchOrderRsp>,
    ) -> Result<()> {
        Ok(())
    }

    /// 修改订单回报回调
    ///
    /// # 参数
    /// -exchange: 交易所类型
    /// -account_id: 账户ID
    /// -id: 订单ID
    async fn on_order_amended(
        &self,
        account_id: AccountId,
        context: Context,
        id: Result<String>,
        order: Order,
    ) -> Result<()> {
        Ok(())
    }

    /// 撤单回报回调
    ///
    /// # 参数
    /// -exchange: 交易所类型
    /// -account_id: 账户ID
    /// -id: 订单ID
    async fn on_order_canceled(
        &self,
        account_id: AccountId,
        context: Context,
        res: Result<()>,
        oid: OrderId,
        symbol: Symbol,
    ) -> Result<()> {
        Ok(())
    }

    /// 批量撤单回报回调
    ///
    /// # 参数
    /// -exchange: 交易所类型
    /// -account_id: 账户ID
    /// -ids: 订单ID列表
    async fn on_batch_order_canceled(
        &self,
        account_id: AccountId,
        context: Context,
        ids: Result<BatchOrderRsp>,
    ) -> Result<()> {
        Ok(())
    }

    /// 全部持仓回调
    ///
    /// # 参数
    /// -exchange: 交易所类型
    /// -account_id: 账户ID
    /// -positions: 全部持仓
    async fn on_position(&self, account_id: AccountId, positions: Vec<Position>) -> Result<()> {
        Ok(())
    }

    /// 全部币种余额回调
    ///
    /// # 参数
    /// -exchange: 交易所类型
    /// -account_id: 账户ID
    /// -balances: 全部币种余额
    async fn on_balance(&self, account_id: AccountId, balances: Vec<Balance>) -> Result<()> {
        Ok(())
    }

    /// BBO行情回调
    ///
    /// # 参数
    /// -exchange: 交易所类型
    /// -ticker: BBO行情
    async fn on_bbo(&self, exchange: Exchange, context: Context, ticker: BboTicker) -> Result<()> {
        Ok(())
    }

    /// 深度行情回调
    ///
    /// # 参数
    /// -exchange: 交易所类型
    /// -depth: 深度行情
    async fn on_depth(&self, exchange: Exchange, context: Context, depth: Depth) -> Result<()> {
        Ok(())
    }

    /// K线行情回调
    ///
    /// # 参数
    /// -exchange: 交易所类型
    /// -ticker: K线行情
    async fn on_ticker(&self, exchange: Exchange, context: Context, ticker: Ticker) -> Result<()> {
        Ok(())
    }

    /// 成交回调
    ///
    /// # 参数
    /// -exchange: 交易所类型
    /// -trade: 成交
    async fn on_trade(&self, exchange: Exchange, context: Context, trade: Trade) -> Result<()> {
        Ok(())
    }

    /// 全部资金费回调
    ///
    /// # 参数
    /// -exchange: 交易所类型
    /// -fundings: 全部资金费
    async fn on_fundings(&self, exchange: Exchange, fundings: Vec<Funding>) -> Result<()> {
        Ok(())
    }

    /// 标记价格回调
    ///
    /// # 参数
    /// -exchange: 交易所类型
    /// -mark_price: 标记价格
    async fn on_mark_price(&self, exchange: Exchange, mark_price: MarkPrice) -> Result<()> {
        Ok(())
    }

    /// kline行情回调
    ///
    /// # 参数
    /// -exchange: 交易所类型
    /// -kline: kline行情
    async fn on_kline(&self, exchange: Exchange, kline: Kline) -> Result<()> {
        Ok(())
    }

    /// 全部产品信息更新回调
    ///
    /// # 参数
    /// -exchange: 交易所类型
    /// -instrument: 产品信息
    async fn on_instrument(&self, exchange: Exchange, instruments: Vec<Instrument>) -> Result<()> {
        Ok(())
    }

    /// 配置热更新
    ///
    /// # 参数
    /// -config: 配置
    async fn on_config_update(&self, config: Value) -> Result<()> {
        Ok(())
    }

    /// 停止程序
    async fn on_stop(&self) -> Result<()> {
        Ok(())
    }

    /// 处理线程任务
    ///
    /// # 参数
    /// -task_name: 任务名称
    async fn on_thread_task(&self, task_name: String) -> Result<()> {
        Ok(())
    }

    /// 处理命令
    ///
    /// # 参数
    /// -command: 命令
    async fn on_command(&self, command: AsyncCmd) -> Result<()> {
        Ok(())
    }
}
