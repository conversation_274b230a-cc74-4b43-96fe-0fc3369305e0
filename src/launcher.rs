use crate::{
    cache::{CacheFactory, StaticCache},
    config::Config,
    data::{DataSourceFactory, StaticDataSource, sub::sub},
    dex::{DexManager, sync::DexSync},
    execution::{ExecutionEngine, ExecutionEngineFactory, StaticExecutionEngine},
    model::event::SystemCommand,
    strategy::Strategy,
    utils::watcher::async_watcher,
};
use algo_common::logger::{tracing::init_tracing_logger, tracing_layer::LogMsg};
use downloader::Downloader;
use notify::RecursiveMode;
use once_cell::sync::OnceCell;
use quant_api::cache::init::init_instruments;
use quant_common::{Result, base::ExConfig};
use std::{path::Path, sync::Arc};
use tokio::{fs::read_to_string, select, sync::mpsc::Receiver};

pub struct Launcher {
    config: Config,
    data_source: Arc<StaticDataSource>,
    execution_engine: Arc<StaticExecutionEngine>,
    arc_dex_sync: Arc<OnceCell<DexSync>>,
    dex_manager: Arc<DexManager>,
    cache: Arc<StaticCache>,
    rx: Option<Receiver<LogMsg>>,
}

impl Launcher {
    pub async fn new(config: Config, package_name: &str, recv_log: bool) -> Result<Self> {
        let fmt_env_filter = format!(
            "{}={level},quant_common={level},quant_api={level},trader={level},info",
            package_name,
            level = config.log.level
        );

        let file = config.log.file.clone();
        let rate_limit = &config.log.rate_limit;
        let rx = init_tracing_logger(
            fmt_env_filter,
            file,
            recv_log,
            rate_limit.max_tokens,
            rate_limit.interval,
        )?;

        let exconfigs: Vec<ExConfig> = config.exchanges.iter().map(|e| e.config.clone()).collect();
        let base_index = exconfigs.len();

        // 创建数据源
        let data_source = Arc::new(DataSourceFactory::create(
            config.data_source.clone(),
            exconfigs.clone(),
        ));

        // 创建执行引擎
        let execution_engine = Arc::new(
            ExecutionEngineFactory::create(
                config.exchanges.clone(),
                config.execution_engine.clone(),
            )
            .await?,
        );

        // 创建dex同步器
        let arc_dex_sync = Arc::new(OnceCell::new());

        // 创建下载器
        let downloader = Arc::new(Downloader::new(config.downloader.clone())?);

        // 创建dex管理器
        let dex_manager = Arc::new(
            DexManager::new(
                config.dex.clone(),
                downloader,
                base_index,
                arc_dex_sync.clone(),
            )
            .await?,
        );

        // 创建cache
        let cache = Arc::new(CacheFactory::create(config.cache.clone()));

        Ok(Self {
            config,
            data_source,
            execution_engine,
            arc_dex_sync,
            dex_manager,
            cache,
            rx,
        })
    }

    pub async fn start<S: Strategy + Clone + 'static>(&mut self, strategy: S) -> Result<()> {
        info!("启动...");

        // 在启动开始时就设置 ctrl+c 监听器
        let ctrl_c = tokio::signal::ctrl_c();
        tokio::pin!(ctrl_c);

        // 使用 select 在启动过程中也能响应 ctrl+c
        select! {
            result = async {
                // 执行启动序列
                init_instruments(
                    self.config
                        .exchanges
                        .iter()
                        .map(|e| e.config.clone())
                        .collect(),
                    self.config.instruments_refresh_sec,
                    strategy.clone(),
                )
                .await?;

                let box_strategy = Box::new(strategy.clone());

                // 执行引擎设置策略
                self.execution_engine
                    .set_strategy(box_strategy.clone())
                    .await;

                // 启动执行引擎
                self.execution_engine.start().await?;
                info!("执行引擎启动成功");

                // 创建dex同步器
                let dex_sync = DexSync::new(box_strategy.clone());
                if self.arc_dex_sync.set(dex_sync).is_err() {
                    panic!("DexSync already set");
                }

                // 启动dex管理器
                self.dex_manager.clone().start_and_wait_ready().await?;
                let dex_manager_clone = self.dex_manager.clone();
                tokio::spawn(async move {
                    if let Err(e) = DexManager::start_message_loop(dex_manager_clone).await {
                        error!("dex管理器启动消息处理失败: {}", e);
                        panic!("dex管理器启动消息处理失败: {e}");
                    }
                });

                // 启动策略
                box_strategy.start().await?;
                info!("策略启动成功");

                // 订阅数据源
                let subs = box_strategy.subscribes().await?;
                sub(
                    subs,
                    strategy.clone(),
                    self.dex_manager.clone(),
                    self.config.data_source.market_mode.clone(),
                    self.data_source.clone(),
                )
                .await?;

                info!("数据源订阅成功");
                info!("启动成功");

                Ok::<(), quant_common::Error>(())
            } => {
                match result {
                    Ok(()) => {
                        // 创建配置文件监听器
                        let mut watcher = if let Some(config_path) = self.config.strategy_config_path.as_ref() {
                            if !config_path.is_empty() {
                                let (mut watcher, rx) = async_watcher()?;
                                let path = Path::new(config_path);
                                watcher.watcher().watch(path, RecursiveMode::NonRecursive)?;
                                Some((watcher, rx))
                            } else {
                                None
                            }
                        } else {
                            None
                        };
                        // 启动成功，进入主循环
                        loop {
                            select! {
                                _ = tokio::signal::ctrl_c() => {
                                    info!("收到 ctrl c, 停止...");
                                    Self::graceful_shutdown(&strategy).await?;
                                    break;
                                }
                                event = async {
                                    if let Some((_, rx)) = watcher.as_mut() {
                                        rx.recv().await
                                    } else {
                                        // 如果没有文件监控，则永远等待
                                        std::future::pending().await
                                    }
                                } => {
                                    match event {
                                        Some(_) => {
                                            if let Some(config_path) = self.config.strategy_config_path.as_ref() {
                                                let path = Path::new(config_path);
                                                if let Err(e) = Self::handle_file_change(path, &strategy).await {
                                                    error!("处理文件变更失败: {}", e);
                                                }
                                            }
                                        }
                                        None => {
                                            error!("处理文件变更失败: 连接已关闭");
                                        }
                                    }
                                }
                            }
                        }
                    }
                    Err(e) => {
                        error!("启动失败: {}", e);
                        return Err(e);
                    }
                }
            }
            _ = &mut ctrl_c => {
                error!("启动过程中收到 ctrl c, 强制退出不执行任何操作...");
                std::process::exit(1);
            }
        }

        Ok(())
    }

    async fn graceful_shutdown<S: Strategy>(strategy: &S) -> Result<()> {
        strategy
            .handle_event(crate::model::event::Event::System(SystemCommand::Stop))
            .await?;
        Ok(())
    }

    async fn handle_file_change<S: Strategy>(path: &Path, strategy: &S) -> Result<()> {
        info!("文件变更, 热更新策略");
        let config_str = read_to_string(path).await?;
        let toml_value: toml::Value = toml::from_str(&config_str)?;
        let config = serde_json::to_value(&toml_value)?;
        strategy
            .handle_event(crate::model::event::Event::System(
                SystemCommand::HotUpdate(config),
            ))
            .await?;

        Ok(())
    }

    pub fn config(&self) -> &Config {
        &self.config
    }

    pub fn data_source(&self) -> Arc<StaticDataSource> {
        self.data_source.clone()
    }

    pub fn execution_engine(&self) -> Arc<StaticExecutionEngine> {
        self.execution_engine.clone()
    }

    pub fn dex_manager(&self) -> Arc<DexManager> {
        self.dex_manager.clone()
    }

    pub fn arc_dex_sync(&self) -> Arc<OnceCell<DexSync>> {
        self.arc_dex_sync.clone()
    }

    pub fn cache(&self) -> Arc<StaticCache> {
        self.cache.clone()
    }

    pub fn log_receiver(&mut self) -> Option<Receiver<LogMsg>> {
        self.rx.take()
    }
}
