#[macro_use]
extern crate tracing;

use trader::launcher::Launcher;
use trader::logo::print_logo;
use wrapper::python::init::get_strategy;

use quant_common::{Result, base::Exchange, load_toml_file, set_max_priority, set_thread_affinity};

use crate::{
    config::Config,
    web::{WebServer, client::get_global_http_client},
};

mod config;
mod utils;
mod web;
mod wrapper;

fn main() -> Result<()> {
    // 创建主运行时
    let main_rt = tokio::runtime::Builder::new_multi_thread()
        .on_thread_start(move || {
            use std::sync::atomic::{AtomicUsize, Ordering};
            static ATOMIC_ID: AtomicUsize = AtomicUsize::new(0);
            let id = ATOMIC_ID.fetch_add(1, Ordering::SeqCst);
            if id == 0
                && let Err(e) = set_thread_affinity(0) {
                    warn!(
                        "无法设置 CPU 线程亲和性 (Thread Affinity)。\n如果您使用 Linux/Unix 系统，请尝试使用 sudo 运行。\n错误详情: {}",
                        e
                    );
                }
            if let Err(e) = set_max_priority() {
                warn!(
                    "无法设置线程优先级 (Thread Priority)。\n如果您使用 Linux/Unix 系统，请尝试使用 sudo 运行。\n错误详情: {}",
                    e
                );
            }
        })
        .enable_all()
        .build()
        .unwrap();

    // 获取命令行参数
    let mut args = std::env::args();
    let path = args.nth(1).unwrap_or_else(|| "config.toml".to_string());

    print_logo();

    let config: Config = load_toml_file(&path)?;

    // 初始化全局web客户端
    let web_client = get_global_http_client(Some(config.web.clone()))?;
    let web_server = WebServer::new(web_client);

    let mut launcher = main_rt
        .block_on(async { Launcher::new(config.trader.clone(), "open_quant", true).await })?;

    main_rt.block_on(async {
        web_server.validate().await?;
        web_server.loop_report().await
    })?;

    let trader_config = launcher.config();

    info!("配置文件: {}", path);
    if config.strategy_path.is_some() {
        info!("策略文件: {}", config.strategy_path.as_ref().unwrap());
    }
    if trader_config.strategy_config_path.is_some() {
        info!(
            "策略配置文件: {}",
            trader_config.strategy_config_path.as_ref().unwrap()
        );
    }
    info!("日志级别: {}", trader_config.log.level);
    info!("版本: {}", get_version());

    // 启动主运行时
    main_rt.block_on(async {
        info!("初始化策略");
        Exchange::warmup_cid_generator().await;
        let strategy = get_strategy(
            &mut launcher,
            main_rt.handle().to_owned(),
            config.trader_version.clone(),
            config.strategy_path.as_ref().unwrap(),
        )
        .await?;
        // 启动策略
        launcher.start(strategy.clone()).await?;

        Ok(())
    })
}

pub fn get_version() -> String {
    let pkg_version = built_info::PKG_VERSION;
    let build_desc = built_info::BUILD_DESC;
    let build_string = match build_desc {
        Some(desc) => format!("{} {}", built_info::TARGET, desc),
        None => built_info::TARGET.into(),
    };

    format!(
        "{} ({} {} {})",
        pkg_version,
        built_info::NOW,
        built_info::GIT_SHORT_HASH,
        build_string,
    )
}

pub mod built_info {
    include!(concat!(env!("OUT_DIR"), "/built.rs"));
}
