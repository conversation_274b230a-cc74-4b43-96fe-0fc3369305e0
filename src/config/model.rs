use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON><PERSON>, Deserialize, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct WebConfig {
    pub is_production: bool,
    pub secret_id: String,
    pub secret_key: String,
}

#[derive(Serialize, Deserialize, <PERSON><PERSON>, Debug, De<PERSON>ult, PartialEq, Eq)]
pub enum TraderVersion {
    V1,
    #[default]
    V2,
}
