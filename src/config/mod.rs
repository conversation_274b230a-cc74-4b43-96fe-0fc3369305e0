use serde::{Deserialize, Serialize};
use trader::config::Config as TraderConfig;

use crate::config::model::{TraderVersion, WebConfig};

pub mod model;

#[derive(Debug, Serialize, Deserialize)]
pub struct Config {
    #[serde(flatten)]
    pub trader: TraderConfig,

    /// web配置
    #[serde(default)]
    pub web: WebConfig,
    /// Trader版本
    #[serde(default)]
    pub trader_version: TraderVersion,
    /// 策略路径
    pub strategy_path: Option<String>,
}
