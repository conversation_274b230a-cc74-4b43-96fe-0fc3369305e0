use algo_common::msg::DexExParams;
use downloader::DownloaderConfig;
use quant_common::base::ExConfig;
use serde::{Deserialize, Serialize};

use crate::{
    cache::CacheType, data::DataSourceType, execution::ExecutionEngineType,
    utils::common::default_use_ws_api,
};

#[derive(Serialize, Deserialize, Clone, Debug, Default)]
pub struct Config {
    #[serde(default)]
    pub exchanges: Vec<ExParams>,

    #[serde(default)]
    pub dex: DexConfig,

    #[serde(default)]
    pub log: LogConfig,

    #[serde(default)]
    pub cache: CacheConfig,

    #[serde(default)]
    pub data_source: DataSourceConfig,

    #[serde(default)]
    pub execution_engine: ExecutionEngineConfig,

    #[serde(default)]
    pub downloader: DownloaderConfig,

    #[serde(default)]
    pub launcher_mode: LauncherMode,

    pub strategy_config_path: Option<String>,
    /// 产品信息刷新间隔(秒)
    #[serde(default = "default_instruments_refresh_sec")]
    pub instruments_refresh_sec: u64,
}

fn default_instruments_refresh_sec() -> u64 {
    60 * 5
}

#[derive(Serialize, Deserialize, Clone, Debug, Default, PartialEq, Eq)]
pub enum LauncherMode {
    #[default]
    Bin,
    AlgoRunner,
}

#[derive(Serialize, Clone, Debug, Default)]
pub struct ExParams {
    #[serde(flatten)]
    pub config: ExConfig,
    /// 交易所的返佣比例
    pub rebate_rate: f64,
    /// 使用ws api
    pub use_ws_api: bool,
}

impl<'de> Deserialize<'de> for ExParams {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        #[derive(Deserialize)]
        struct ExParamsHelper {
            #[serde(flatten)]
            config: ExConfig,
            rebate_rate: f64,
            use_ws_api: Option<bool>,
        }

        let helper = ExParamsHelper::deserialize(deserializer)?;
        let mut ex_params = ExParams {
            config: helper.config,
            rebate_rate: helper.rebate_rate,
            use_ws_api: false,
        };
        match helper.use_ws_api {
            Some(use_ws_api) => ex_params.use_ws_api = use_ws_api,
            None => ex_params.use_ws_api = default_use_ws_api(ex_params.config.exchange),
        }
        Ok(ex_params)
    }
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct DexConfig {
    #[serde(default = "default_heartbeat_timeout")]
    pub heartbeat_timeout: u64,
    /// 同步命令等待超时
    #[serde(default = "default_sync_timeout")]
    pub sync_timeout: u64,
    pub exchanges: Vec<DexExParams>,
    /// 单个日志文件最大大小(MB)
    #[serde(default = "default_max_log_size")]
    pub max_log_size: u64,
    /// 每个 DEX 保留的最大日志文件数
    #[serde(default = "default_max_log_files")]
    pub max_log_files: usize,
}

impl Default for DexConfig {
    fn default() -> Self {
        Self {
            heartbeat_timeout: default_heartbeat_timeout(),
            sync_timeout: default_sync_timeout(),
            exchanges: Vec::new(),
            max_log_size: default_max_log_size(),
            max_log_files: default_max_log_files(),
        }
    }
}

fn default_heartbeat_timeout() -> u64 {
    25
}

fn default_sync_timeout() -> u64 {
    30
}

fn default_max_log_size() -> u64 {
    100 * 1024 * 1024
}

fn default_max_log_files() -> usize {
    2
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct LogConfig {
    #[serde(default = "default_log_level")]
    pub level: String,
    #[serde(default)]
    pub file: Option<String>,
    #[serde(default)]
    pub rate_limit: LogRateLimitConfig,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct LogRateLimitConfig {
    /// 日志限频最大令牌数
    #[serde(default = "default_max_tokens")]
    pub max_tokens: f64,
    /// 日志限频令牌生成速率
    #[serde(default = "default_interval")]
    pub interval: f64,
}

impl Default for LogRateLimitConfig {
    fn default() -> Self {
        Self {
            max_tokens: default_max_tokens(),
            interval: default_interval(),
        }
    }
}

fn default_max_tokens() -> f64 {
    50.
}

fn default_interval() -> f64 {
    0.
}

fn default_log_level() -> String {
    "info".to_string()
}

impl Default for LogConfig {
    fn default() -> Self {
        Self {
            level: default_log_level(),
            file: None,
            rate_limit: LogRateLimitConfig::default(),
        }
    }
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct CacheConfig {
    #[serde(default)]
    pub typ: CacheType,
    #[serde(default = "default_cache_path")]
    pub path: String,
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            typ: CacheType::File,
            path: default_cache_path(),
        }
    }
}

fn default_cache_path() -> String {
    "cache.json".to_string()
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct MarketConfig {
    /// 标记价格消息处理模式
    #[serde(default = "default_mark_price_mode")]
    pub mark_price: MarketModeType,

    /// BBO行情消息处理模式
    #[serde(default = "default_bbo_mode")]
    pub bbo: MarketModeType,

    /// 深度行情消息处理模式
    #[serde(default = "default_depth_mode")]
    pub depth: MarketModeType,

    /// 资金费率消息处理模式
    #[serde(default = "default_funding_mode")]
    pub funding: MarketModeType,

    /// 交易消息处理模式
    #[serde(default = "default_trade_mode")]
    pub trade: MarketModeType,
}

impl Default for MarketConfig {
    fn default() -> Self {
        Self {
            mark_price: default_mark_price_mode(),
            bbo: default_bbo_mode(),
            depth: default_depth_mode(),
            funding: default_funding_mode(),
            trade: default_trade_mode(),
        }
    }
}

fn default_mark_price_mode() -> MarketModeType {
    MarketModeType::Latest
}

fn default_bbo_mode() -> MarketModeType {
    MarketModeType::Latest
}

fn default_depth_mode() -> MarketModeType {
    MarketModeType::Latest
}

fn default_funding_mode() -> MarketModeType {
    MarketModeType::All
}

fn default_trade_mode() -> MarketModeType {
    MarketModeType::All
}

#[derive(Serialize, Deserialize, Clone, Copy, Debug, PartialEq, Eq)]
pub enum MarketModeType {
    /// 只处理最新行情
    Latest,
    /// 顺序处理所有行情
    All,
}

#[derive(Serialize, Deserialize, Clone, Debug, Default)]
pub struct DataSourceConfig {
    #[serde(default)]
    pub typ: DataSourceType,

    /// 行情处理模式
    #[serde(default)]
    pub market_mode: MarketConfig,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct ExecutionEngineConfig {
    #[serde(default)]
    pub typ: ExecutionEngineType,

    #[serde(default = "default_task_count")]
    pub task_count: usize,
}

impl Default for ExecutionEngineConfig {
    fn default() -> Self {
        Self {
            typ: ExecutionEngineType::Stand,
            task_count: default_task_count(),
        }
    }
}

fn default_task_count() -> usize {
    500
}

#[derive(Serialize, Deserialize, Clone, Debug, Default)]
pub struct MonitorConfig {
    pub system_monitor: bool,
    #[serde(default = "default_report_interval")]
    pub report_interval: u64,
}

fn default_report_interval() -> u64 {
    60
}

#[cfg(test)]
mod tests {

    use algo_common::msg::DexExParams;

    use super::*;

    #[test]
    fn test_config_to_toml() {
        let config = Config {
            exchanges: vec![ExParams {
                config: ExConfig::default(),
                rebate_rate: 0.0001,
                use_ws_api: true,
            }],
            dex: DexConfig {
                max_log_size: 100 * 1024 * 1024,
                max_log_files: 10,
                sync_timeout: 30,
                exchanges: vec![DexExParams {
                    name: "solona".to_string(),
                    version: "0.1.0".to_string(),
                    params: vec![
                        ("api_key".to_string(), "1234567890".to_string()),
                        ("api_secret".to_string(), "0987654321".to_string()),
                    ]
                    .into_iter()
                    .collect(),
                }],
                heartbeat_timeout: 15,
            },
            log: LogConfig {
                level: "info".to_string(),
                file: None,
                rate_limit: LogRateLimitConfig {
                    max_tokens: 50.,
                    interval: 0.,
                },
            },
            cache: CacheConfig {
                typ: CacheType::File,
                path: "cache.json".to_string(),
            },
            data_source: DataSourceConfig::default(),
            execution_engine: ExecutionEngineConfig {
                typ: ExecutionEngineType::Stand,
                task_count: 1,
            },
            downloader: DownloaderConfig {
                timeout_secs: 30,
                retry_times: 3,
                chunk_size: 1024 * 1024,
                temp_suffix: ".downloading".to_string(),
                base_url: "https://example.com/dex/".to_string(),
                verify_local: true,
            },
            launcher_mode: LauncherMode::Bin,
            strategy_config_path: Some("strategy_configs".to_string()),
            instruments_refresh_sec: 60 * 5,
        };
        let toml = toml::to_string_pretty(&config).unwrap();
        println!("{toml}");
    }
}
