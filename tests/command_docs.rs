use std::collections::HashMap;
use std::fs::File;
use std::io::Write;
use std::path::Path;

use quant_common::base::traits::{
    AmendOrderParams, BatchCancelOrderByIdsParams, BatchCancelOrderParams, BorrowCoinParams,
    CancelOrderParams, GetAccountInfoParams, GetAccountModeParams, GetAllOpenOrdersParams,
    GetBalanceParams, GetBalancesParams, GetBboTickerParams, GetBboTickersParams,
    GetBorrowLimitsParams, GetBorrowParams, GetBorrowRateParams, GetDepositAddressParams,
    GetDepthParams, GetFeeDiscountInfoParams, GetFeeRateParams, GetFundingFeeParams,
    GetFundingRateHistoryParams, GetFundingRateParams, GetFundingRatesParams, GetInstrumentParams,
    GetInstrumentsParams, GetKlineParams, GetMarginModeParams, GetMarkPriceParams,
    GetMaxLeverageParams, GetMaxPositionParams, GetOpenOrdersParams, GetOrderByIdParams,
    GetOrdersParams, GetPositionParams, GetPositionsParams, GetTickerParams, GetTickersParams,
    GetUsdtBalanceParams, GetUserIdParams, IsDualSideParams, IsFeeDiscountEnabledParams,
    PostBatchOrderParams, PostOrderParams, RepayCoinParams, SetAccountModeParams,
    SetDualSideParams, SetFeeDiscountEnabledParams, SetLeverageParams, SetMarginModeParams,
    SubTransferParams, TransferParams, WithdrawalParams,
};
use quant_common::base::{
    AccountMode, Chain, KlineInterval, MarginMode, Order, OrderId, OrderParams, OrderSide,
    OrderType, PosSide, SubTransfer, Symbol, TimeInForce, Transfer, UserRequest, WithDrawlParams,
};
use sonic_rs::{Value, json};
use trader::model::account::AccountId;
use trader::model::context::Context;
use trader::model::event::ex_command::{AsyncCommand, Command, ExecutionCommand, SyncCommand};

#[test]
#[ignore]
fn generate_command_docs() {
    // 创建目标文件
    let path = Path::new("command_docs.md");
    let mut file = File::create(path).expect("无法创建文件");

    // 写入文档头部
    writeln!(file, "# 交易命令JSON文档\n").unwrap();
    writeln!(
        file,
        "本文档包含所有交易命令序列化后的JSON格式示例，用于API调用参考。\n"
    )
    .unwrap();

    // 同步命令部分
    writeln!(file, "## 同步命令\n").unwrap();
    writeln!(file, "同步命令会立即返回结果。\n").unwrap();

    // 生成所有同步命令的JSON示例
    generate_sync_commands(&mut file);

    // 异步命令部分
    writeln!(file, "## 异步命令\n").unwrap();
    writeln!(file, "异步命令会通过回调或事件返回结果。\n").unwrap();

    // 生成所有异步命令的JSON示例
    generate_async_commands(&mut file);

    println!("命令文档已生成: {}", path.display());
}

fn generate_sync_commands(file: &mut File) {
    let account_id: AccountId = 1;
    let context = Context::default();
    let empty_extra = HashMap::new();

    // 创建常用的测试值
    let symbol = Symbol::new("BTC");

    // ==================== 用户自定义接口 ====================

    // 0. 自定义请求
    let mut headers = HashMap::new();
    headers.insert(
        "Content-Type".to_string(),
        Value::from_static_str("application/json"),
    );
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::Request(UserRequest {
            method: "GET".to_string(),
            path: "/api/v1/test".to_string(),
            auth: true,
            query: Some(json!({
                "key": "value",
            })),
            body: Some(json!({
                "key2": "value2",
            })),
            url: Some("https://api.example.com/api/v1/test".to_string()),
            headers: Some(headers),
        })),
    };
    write_command_to_doc(file, "自定义请求", cmd);

    // ==================== 私有接口 - 订单相关 ====================

    // 1. 获取订单历史（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::GetOrdersExt(GetOrdersParams {
            symbol: symbol.clone(),
            start_time: **********000, // 2021-01-01
            end_time: *************,   // 2022-01-01
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "获取订单历史", cmd);

    // 2. 获取当前挂单（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::GetOpenOrdersExt(GetOpenOrdersParams {
            symbol: symbol.clone(),
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "获取当前挂单", cmd);

    // 3. 获取所有挂单（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::GetAllOpenOrdersExt(GetAllOpenOrdersParams {
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "获取所有挂单", cmd);

    // 4. 获取订单（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::GetOrderByIdExt(GetOrderByIdParams {
            symbol: symbol.clone(),
            order_id: OrderId::Id("test_order_id".to_string()),
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "获取订单", cmd);

    // 5. 同步下单（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::PlaceOrderExt(PostOrderParams {
            order: Order {
                cid: Some("sync_client123456".to_string()),
                symbol: symbol.clone(),
                order_type: OrderType::Limit,
                side: OrderSide::Buy,
                pos_side: Some(PosSide::Long),
                price: Some(50000.0),
                amount: Some(0.1),
                time_in_force: TimeInForce::GTC,
                ..Order::default()
            },
            params: OrderParams {
                leverage: 10,
                margin_mode: Some(MarginMode::Cross),
                ..OrderParams::default()
            },
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "同步下单", cmd);

    // 6. 同步批量下单（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::BatchPlaceOrderExt(PostBatchOrderParams {
            orders: vec![
                Order {
                    cid: Some("sync_batch_client1".to_string()),
                    symbol: symbol.clone(),
                    order_type: OrderType::Limit,
                    side: OrderSide::Buy,
                    pos_side: Some(PosSide::Long),
                    price: Some(50000.0),
                    amount: Some(0.1),
                    time_in_force: TimeInForce::GTC,
                    ..Order::default()
                },
                Order {
                    cid: Some("sync_batch_client2".to_string()),
                    symbol: Symbol::new("ETH"),
                    order_type: OrderType::Limit,
                    side: OrderSide::Sell,
                    pos_side: Some(PosSide::Short),
                    price: Some(3000.0),
                    amount: Some(1.0),
                    time_in_force: TimeInForce::GTC,
                    ..Order::default()
                },
            ],
            params: OrderParams {
                leverage: 10,
                margin_mode: Some(MarginMode::Cross),
                ..OrderParams::default()
            },
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "同步批量下单", cmd);

    // 7. 同步修改订单（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::AmendOrderExt(AmendOrderParams {
            order: Order {
                cid: Some("sync_amend_client".to_string()),
                symbol: symbol.clone(),
                price: Some(52000.0),
                amount: Some(0.15),
                ..Order::default()
            },
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "同步修改订单", cmd);

    // 8. 同步撤单（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::CancelOrderExt(CancelOrderParams {
            symbol: symbol.clone(),
            order_id: OrderId::Id("sync_cancel_order_id".to_string()),
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "同步撤单", cmd);

    // 9. 同步批量撤单（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::BatchCancelOrderExt(BatchCancelOrderParams {
            symbol: symbol.clone(),
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "同步批量撤单", cmd);

    // 10. 同步批量撤单根据订单ID（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::BatchCancelOrderByIdExt(
            BatchCancelOrderByIdsParams {
                symbol: Some(symbol.clone()),
                ids: Some(vec!["sync_order1".to_string(), "sync_order2".to_string()]),
                cids: Some(vec!["sync_client1".to_string(), "sync_client2".to_string()]),
                extra: empty_extra.clone(),
            },
        )),
    };
    write_command_to_doc(file, "同步批量撤单根据订单ID", cmd);

    // ==================== 私有接口 - 账户相关 ====================

    // 11. 查询持仓（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::PositionExt(GetPositionParams {
            symbol: symbol.clone(),
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "查询持仓", cmd);

    // 12. 查询所有持仓（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::PositionsExt(GetPositionsParams {
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "查询所有持仓", cmd);

    // 13. 获取最大持仓（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::MaxPositionExt(GetMaxPositionParams {
            symbol: symbol.clone(),
            leverage: 10,
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "获取最大持仓", cmd);

    // 14. 查询结算资金费（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::FundingFeeExt(GetFundingFeeParams {
            symbol: symbol.clone(),
            start_time: Some(**********000),
            end_time: Some(*************),
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "查询结算资金费", cmd);

    // 15. 最大杠杆（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::MaxLeverageExt(GetMaxLeverageParams {
            symbol: symbol.clone(),
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "最大杠杆", cmd);

    // 16. 查询保证金模式（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::MarginModeExt(GetMarginModeParams {
            symbol: symbol.clone(),
            margin_coin: "USDT".to_string(),
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "查询保证金模式", cmd);

    // 17. 设置保证金模式（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::SetMarginModeExt(SetMarginModeParams {
            symbol: symbol.clone(),
            margin_mode: MarginMode::Cross,
            margin_coin: "USDT".to_string(),
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "设置保证金模式", cmd);

    // 18. 查询USDT余额（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::UsdtBalanceExt(GetUsdtBalanceParams {
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "查询USDT余额", cmd);

    // 19. 查询多币种余额（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::BalanceExt(GetBalancesParams {
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "查询多币种余额", cmd);

    // 20. 查询指定币种余额（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::BalanceByCoinExt(GetBalanceParams {
            asset: "BTC".to_string(),
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "查询指定币种余额", cmd);

    // 21. 获取手续费（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::FeeRateExt(GetFeeRateParams {
            symbol: symbol.clone(),
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "获取手续费", cmd);

    // 22. 设置杠杆（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::SetLeverageExt(SetLeverageParams {
            symbol: symbol.clone(),
            leverage: 20,
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "设置杠杆", cmd);

    // 23. 查询是否双向持仓（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::IsDualSidePositionExt(IsDualSideParams {
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "查询是否双向持仓", cmd);

    // 24. 设置双向持仓（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::SetDualSidePositionExt(SetDualSideParams {
            is_dual_side: true,
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "设置双向持仓", cmd);

    // 25. 万向划转（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::TransferExt(TransferParams {
            transfer: Transfer {
                asset: "USDT".to_string(),
                amount: 100.0,
                from: quant_common::base::model::transfer::WalletType::Spot,
                to: quant_common::base::model::transfer::WalletType::UsdtFuture,
            },
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "万向划转", cmd);

    // 26. 子账户划转（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::SubTransferExt(SubTransferParams {
            transfer: SubTransfer {
                cid: Some("sub_transfer_123".to_string()),
                asset: "USDT".to_string(),
                amount: 50.0,
                from: quant_common::base::model::transfer::WalletType::Spot,
                to: quant_common::base::model::transfer::WalletType::UsdtFuture,
                from_account: Some("sub_account_1".to_string()),
                to_account: Some("sub_account_2".to_string()),
                direction: quant_common::base::model::transfer::TransferDirection::SubToSub,
            },
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "子账户划转", cmd);

    // 27. 获取充值地址（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::GetDepositAddressExt(GetDepositAddressParams {
            ccy: "USDT".to_string(),
            chain: Some(Chain::Trc20),
            amount: Some(100.0),
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "获取充值地址", cmd);

    // 28. 提币（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::WithdrawalExt(WithdrawalParams {
            params: WithDrawlParams {
                cid: Some("withdrawal_client_123".to_string()),
                asset: "USDT".to_string(),
                amt: 100.0,
                addr: quant_common::base::model::account::WithdrawalAddr::OnChain(
                    quant_common::base::model::account::OnChainParams {
                        chain: Chain::Trc20,
                        address: "TRX_ADDRESS_EXAMPLE".to_string(),
                        tag: None,
                    },
                ),
            },
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "提币", cmd);

    // 29. 获取账户信息（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::GetAccountInfoExt(GetAccountInfoParams {
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "获取账户信息", cmd);

    // 30. 获取账户模式（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::GetAccountModeExt(GetAccountModeParams {
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "获取账户模式", cmd);

    // 31. 设置账户模式（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::SetAccountModeExt(SetAccountModeParams {
            mode: AccountMode::MultiCurrency,
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "设置账户模式", cmd);

    // 32. 获取用户ID（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::GetUserIdExt(GetUserIdParams {
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "获取用户ID", cmd);

    // 33. 查询交易所折扣信息（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::GetFeeDiscountInfoExt(
            GetFeeDiscountInfoParams {
                extra: empty_extra.clone(),
            },
        )),
    };
    write_command_to_doc(file, "查询交易所折扣信息", cmd);

    // 34. 查询账户是否开启原生币手续费折扣（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::IsFeeDiscountEnabledExt(
            IsFeeDiscountEnabledParams {
                extra: empty_extra.clone(),
            },
        )),
    };
    write_command_to_doc(file, "查询账户是否开启原生币手续费折扣", cmd);

    // 35. 开关账户原生币手续费折扣（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::SetFeeDiscountEnabledExt(
            SetFeeDiscountEnabledParams {
                enable: true,
                extra: empty_extra.clone(),
            },
        )),
    };
    write_command_to_doc(file, "开关账户原生币手续费折扣", cmd);

    // 36. 借币（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::BorrowExt(BorrowCoinParams {
            coin: "USDT".to_string(),
            amount: 1000.0,
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "借币", cmd);

    // 37. 查询借币（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::GetBorrowedExt(GetBorrowParams {
            coin: Some("USDT".to_string()),
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "查询借币", cmd);

    // 38. 还币（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::RepayExt(RepayCoinParams {
            coin: "USDT".to_string(),
            amount: 500.0,
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "还币", cmd);

    // 39. 获取借贷利率（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::GetBorrowRateExt(GetBorrowRateParams {
            coin: Some("USDT".to_string()),
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "获取借贷利率", cmd);

    // 40. 获取借贷限额（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::GetBorrowLimitExt(GetBorrowLimitsParams {
            coin: "USDT".to_string(),
            is_vip: Some(false),
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "获取借贷限额", cmd);

    // ==================== 公有接口 ====================

    // 41. 查询24h交易信息（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::TickerExt(GetTickerParams {
            symbol: symbol.clone(),
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "查询24h交易信息", cmd);

    // 42. 查询所有交易对的24h交易信息（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::TickersExt(GetTickersParams {
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "查询所有交易对的24h交易信息", cmd);

    // 43. 查询最佳买卖价（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::BboExt(GetBboTickerParams {
            symbol: symbol.clone(),
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "查询最佳买卖价", cmd);

    // 44. 查询所有交易对的最佳买卖价（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::BboTickersExt(GetBboTickersParams {
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "查询所有交易对的最佳买卖价", cmd);

    // 45. 查询深度（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::DepthExt(GetDepthParams {
            symbol: symbol.clone(),
            limit: Some(20),
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "查询深度", cmd);

    // 46. 查询产品信息（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::InstrumentExt(GetInstrumentParams {
            symbol: symbol.clone(),
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "查询产品信息", cmd);

    // 47. 查询所有产品信息（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::InstrumentsExt(GetInstrumentsParams {
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "查询所有产品信息", cmd);

    // 48. 查询资金费率（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::FundingRateExt(GetFundingRatesParams {
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "查询资金费率", cmd);

    // 49. 查询单个交易对资金费率（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::FundingRateBySymbolExt(GetFundingRateParams {
            symbol: symbol.clone(),
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "查询单个交易对资金费率", cmd);

    // 50. 标记价格（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::MarkPriceExt(GetMarkPriceParams {
            symbol: Some(symbol.clone()),
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "标记价格", cmd);

    // 51. K线数据（扩展接口）
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::KlineExt(GetKlineParams {
            symbol: symbol.clone(),
            interval: KlineInterval::Min1,
            start_time: Some(**********000), // 2023-01-01
            end_time: Some(*************),   // 2023-01-02
            limit: Some(100),
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "K线数据", cmd);

    // 52. 查询资金费率历史
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Sync(SyncCommand::FundingRateHistory(
            GetFundingRateHistoryParams {
                symbol: Some(symbol.clone()),
                since_secs: Some(**********), // 2023-01-01 (秒级时间戳)
                limit: 100,
                extra: empty_extra.clone(),
            },
        )),
    };
    write_command_to_doc(file, "查询资金费率历史", cmd);
}

fn generate_async_commands(file: &mut File) {
    let account_id: AccountId = 1;
    let context = Context::default();
    let empty_extra = HashMap::new();

    // 创建常用的测试值
    let symbol = Symbol::new("BTC");

    // 1. 异步下单
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Async(AsyncCommand::PlaceOrderExt(PostOrderParams {
            order: Order {
                cid: Some("client123456".to_string()),
                symbol: symbol.clone(),
                order_type: OrderType::Limit,
                side: OrderSide::Buy,
                pos_side: Some(PosSide::Long),
                price: Some(50000.0),
                amount: Some(0.1),
                time_in_force: TimeInForce::GTC,
                ..Order::default()
            },
            params: OrderParams {
                leverage: 10,
                margin_mode: Some(MarginMode::Cross),
                ..OrderParams::default()
            },
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "异步下单", cmd);

    // 2. 异步批量下单
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Async(AsyncCommand::BatchPlaceOrderExt(PostBatchOrderParams {
            orders: vec![
                Order {
                    cid: Some("client123456".to_string()),
                    symbol: symbol.clone(),
                    order_type: OrderType::Limit,
                    side: OrderSide::Buy,
                    pos_side: Some(PosSide::Long),
                    price: Some(50000.0),
                    amount: Some(0.1),
                    time_in_force: TimeInForce::GTC,
                    ..Order::default()
                },
                Order {
                    cid: Some("client789012".to_string()),
                    symbol: Symbol::new("ETH"),
                    order_type: OrderType::Limit,
                    side: OrderSide::Sell,
                    pos_side: Some(PosSide::Short),
                    price: Some(3000.0),
                    amount: Some(1.0),
                    time_in_force: TimeInForce::GTC,
                    ..Order::default()
                },
            ],
            params: OrderParams {
                leverage: 10,
                margin_mode: Some(MarginMode::Cross),
                ..OrderParams::default()
            },
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "异步批量下单", cmd);

    // 3. 异步修改订单
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Async(AsyncCommand::AmendOrderExt(AmendOrderParams {
            order: Order {
                cid: Some("client123456".to_string()),
                symbol: symbol.clone(),
                price: Some(52000.0),
                amount: Some(0.15),
                ..Order::default()
            },
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "异步修改订单", cmd);

    // 4. 异步撤单
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Async(AsyncCommand::CancelOrderExt(CancelOrderParams {
            symbol: symbol.clone(),
            order_id: OrderId::Id("test_order_id".to_string()),
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "异步撤单", cmd);

    // 5. 异步批量撤单
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Async(AsyncCommand::BatchCancelOrderExt(BatchCancelOrderParams {
            symbol: symbol.clone(),
            extra: empty_extra.clone(),
        })),
    };
    write_command_to_doc(file, "异步批量撤单", cmd);

    // 6. 异步批量撤单根据订单ID
    let cmd = ExecutionCommand {
        account_id,
        context: context.clone(),
        cmd: Command::Async(AsyncCommand::BatchCancelOrderByIdExt(
            BatchCancelOrderByIdsParams {
                symbol: Some(symbol.clone()),
                ids: Some(vec!["order1".to_string(), "order2".to_string()]),
                cids: Some(vec!["client1".to_string(), "client2".to_string()]),
                extra: empty_extra.clone(),
            },
        )),
    };
    write_command_to_doc(file, "异步批量撤单根据订单ID", cmd);
}

fn write_command_to_doc(file: &mut File, title: &str, cmd: ExecutionCommand) {
    writeln!(file, "### {title}\n").unwrap();

    match cmd.to_flatten_pretty() {
        Ok(json) => {
            writeln!(file, "```json\n{json}\n```\n").unwrap();
        }
        Err(e) => {
            writeln!(file, "序列化失败: {e}\n").unwrap();
        }
    }
}
